# Pull Request Template for Frontend Application

## Provide summary of the frontend changes

---

## Description
Briefly describe the purpose of this change and any relevant context.

---

## Technical Details
Provide a summary of what was changed in the codebase, such as affected components, pages, utilities, or styling.

### Changed Files/Components:
- [ ] Components (`src/components/`)
- [ ] Pages (`src/app/`)
- [ ] Utilities (`src/utils/`)
- [ ] Hooks (`src/hooks/`)
- [ ] Styles (`src/assets/scss/` or `public/css/`)
- [ ] Configuration files
- [ ] Tests (`src/tests/`)
- [ ] Other: _____________

---

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Performance improvement
- [ ] Code refactoring (no functional changes)
- [ ] Documentation update
- [ ] Dependency update
- [ ] Configuration change

---

## Testing
- [ ] Unit tests added/updated (`npm run test:unit`)
- [ ] Integration tests added/updated (`npm run test:integration`)
- [ ] All existing tests pass (`npm run test:all`)
- [ ] Manual testing completed
- [ ] Cross-browser testing completed (if applicable)
- [ ] Mobile responsiveness tested (if applicable)

### Test Coverage:
- [ ] Test coverage maintained or improved
- [ ] Coverage report reviewed (`npm run test:coverage`)

---

## Build & Quality Checks
- [ ] Application builds successfully (`npm run build`)
- [ ] No TypeScript errors
- [ ] ESLint checks pass (`npm run lint`)
- [ ] Code formatting applied (`npm run format`)
- [ ] Pre-commit hooks pass (`npm run pre-commit`)
- [ ] SonarQube quality gate passed with no high or critical issues
- [ ] No hardcoded credentials or sensitive information in the codebase

---

## Accessibility & Performance
- [ ] Accessibility standards followed (WCAG guidelines)
- [ ] Performance impact considered
- [ ] Images optimized (if applicable)
- [ ] Bundle size impact reviewed
- [ ] SEO considerations addressed (if applicable)

---

## Browser Compatibility
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers (if applicable)

---

## Environment Testing
- [ ] Development environment
- [ ] Staging environment (if applicable)
- [ ] Production build tested locally

---

## Security Considerations
- [ ] No sensitive data exposed in client-side code
- [ ] Input validation implemented (if applicable)
- [ ] XSS prevention measures in place (if applicable)
- [ ] CSRF protection maintained (if applicable)

---

## Related Issue / Ticket
Add a link or reference to the relevant issue, task, or ticket.

---

## Screenshots/Videos (if applicable)
Include screenshots or videos demonstrating the changes, especially for UI modifications.

### Before:
<!-- Add screenshots/videos of the previous state -->

### After:
<!-- Add screenshots/videos of the new state -->

---

## Deployment Notes
- [ ] No special deployment steps required
- [ ] Environment variables need to be updated
- [ ] Database migrations required (if applicable)
- [ ] Third-party service configuration changes needed
- [ ] CDN cache invalidation required

### Special Instructions:
<!-- Add any specific deployment instructions or considerations -->

---

## Additional Notes
Include any setup instructions, testing notes, reviewer guidance, or other relevant information.

### Reviewer Checklist:
- [ ] Code follows project conventions and style guidelines
- [ ] Logic is clear and well-documented
- [ ] Error handling is appropriate
- [ ] Performance implications are acceptable
- [ ] Security considerations are addressed
- [ ] Tests are comprehensive and meaningful
