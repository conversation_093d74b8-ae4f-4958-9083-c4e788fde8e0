name: SonarQube Analysis

on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true

jobs:
  sonarqube:
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Run Tests (with coverage)
        run: npm run test:coverage

      - name: Install SonarScanner
        run: npm install -g sonarqube-scanner

      - name: Run SonarQube Analysis
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          sonar-scanner \
            -Dsonar.projectKey=pure-heart-guest-frontend \
            -Dsonar.projectName="pure-heart-guest-frontend" \
            -Dsonar.sources=src/app/demo-2,src/configs,src/components/modals \
            -Dsonar.tests=src/tests \
            -Dsonar.host.url=http://*************:9000 \
            -Dsonar.login=$SONAR_TOKEN \
            -Dsonar.inclusions=src/app/demo-2/page.jsx,src/components/modals/DonateModal.jsx \
            -Dsonar.exclusions=src/**/*.test.js,src/**/*.test.jsx,src/**/*.spec.js,src/**/*.spec.jsx,src/setupTests.js,src/__mocks__/**/*,src/tests/**/*,node_modules/**/*,coverage/**/*,public/**/* \
            -Dsonar.test.inclusions=src/tests/**/* \
            -Dsonar.test.exclusions=src/tests/**/__snapshots__/**/* \
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
            -Dsonar.coverage.exclusions=src/**/*.test.jsx,src/**/*.spec.jsx,src/**/*.test.js,src/**/*.spec.js,src/setupTests.js,src/__mocks__/**/*,src/tests/**/* \
            -Dsonar.sourceEncoding=UTF-8

      - name: Wait for SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: http://*************:9000
