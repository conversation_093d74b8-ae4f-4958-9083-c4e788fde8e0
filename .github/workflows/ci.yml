
name: CI Pipeline
on:
  pull_request:
    branches: [main]
jobs:
  sonar:
    uses: ./.github/workflows/sonarqube-analysis.yml
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
  semgrep:
    needs: sonar
    uses: ./.github/workflows/semgrep-scan.yml
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      KUBERNETES_CLUSTER_ID: ${{ secrets.KUBERNETES_CLUSTER_ID }}
      KUBERNETES_CONTEXT: ${{ secrets.KUBERNETES_CONTEXT }}
      SECURE_GITHUB_TOKEN: ${{ secrets.SECURE_GITHUB_TOKEN }}
