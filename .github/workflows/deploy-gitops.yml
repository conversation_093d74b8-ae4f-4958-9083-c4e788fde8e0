name: Gitops Deployment trigger

on:
  workflow_call:
    secrets:
      SECURE_GITHUB_TOKEN:
        required: true

jobs:
  gitops-deploy:
    runs-on: [self-hosted, Linux]
    steps:
      - name: Prepare Secrets
        id: secrets
        run: |
          SECRETS_JSON=$(cat << EOF | base64 -w 0
          {
            "CONTAINER_PORT": 3003,
            "HEALTH_CHECK_PATH": "/"
          }
          EOF
          )
          echo "secrets_encoded=$SECRETS_JSON" >> $GITHUB_OUTPUT
      - name: 🚀 Trigger GitOps Deployment
        uses: actions/github-script@v7
        env:
          SECRETS_ENCODED: ${{ steps.secrets.outputs.secrets_encoded }}
        with:
          github-token: ${{ secrets.SECURE_GITHUB_TOKEN }}
          script: |
            console.log('🚀 Triggering GitOps deployment for Pure Heart React application...');
            console.log('Branch:', '${{ github.ref_name }}');
            console.log('Event:', '${{ github.event_name }}');

            // Use latest tag for universal runtime-configured deployment
            const dockerTag = 'latest';
            const secretsEncoded = process.env.SECRETS_ENCODED || '';

            console.log('Using universal Docker image tag:', dockerTag);

            // Determine environment based on branch
            let environment = 'dev';  // Default for feature branches
            if ('${{ github.ref_name }}' === 'main') {
              environment = 'production';  // Main branch goes to production
            } else if ('${{ github.ref_name }}' === 'staging') {
              environment = 'staging';  // Staging branch goes to staging
            }

            // Docker tag is always 'latest' - no validation needed

            const payload = {
              project_id: 'pure-heart-frontend-guest',
              application_type: 'react-frontend',
              environment: environment,
              docker_image: 'registry.digitalocean.com/chidhagni-doks-registry/pure-heart-frontend-guest',
              host_name: 'dev.pheart.in',
              docker_tag: dockerTag,
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha,
              secrets_encoded: secretsEncoded || ''
            };

            console.log('📦 Dispatch payload for react-frontend:', JSON.stringify(payload, null, 2));

            // Validate payload before sending
            const requiredFields = ['project_id', 'environment', 'docker_image', 'docker_tag'];
            for (const field of requiredFields) {
              if (!payload[field] || payload[field] === '') {
                throw new Error(`Required field '${field}' is missing or empty`);
              }
            }
            // Validate project_id format (must be lowercase alphanumeric with hyphens)
            if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
              throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
            }

            // Validate environment
            if (!['dev', 'staging', 'production'].includes(payload.environment)) {
              throw new Error(`Invalid environment: ${payload.environment}. Must be dev, staging, or production.`);
            }

            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ChidhagniConsulting',
                repo: 'gitops-argocd-apps',
                event_type: 'deploy-to-argocd',
                client_payload: payload
              });

              console.log(`✅ GitOps deployment triggered successfully!`);
              console.log(`📱 App: Pure Heart Frontend Guest (pure-heart-frontend-guest)`);
              console.log(`🌍 Environment: ${environment}`);
              console.log(`🐳 Universal Docker image: registry.digitalocean.com/doks-registry/pure-heart-frontend-guest:${dockerTag}`);
              console.log(`⚙️  Runtime config: Backend URL will be configured via ConfigMap`);
              console.log(`🌿 Source branch: ${{ github.ref_name }}`);
              console.log(`📝 Commit SHA: ${context.sha}`);
              console.log(`🔗 Monitor deployment: https://github.com/ChidhagniConsulting/gitops-argocd-apps/actions`);
            } catch (error) {
              console.error('❌ Failed to trigger GitOps deployment:', error);
              console.error('Error details:', error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
              }
              throw error;
            }