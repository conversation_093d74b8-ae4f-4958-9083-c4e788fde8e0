# Gradle
.gradle/
build/
!build/libs/*.jar

# IDE
.idea/
*.iml
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime
*.pid
*.port

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md

# Test files
src/test/

# Temporary files
*.tmp
*.temp

# Environment files
.env
.env.*

# Node modules (if any)
node_modules/

# Coverage reports
coverage/
.nyc_output/

# SonarQube
.sonar/ 