README.md
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

logs/*


# Frontend (Node/Next.js) artifacts
node_modules/
**/node_modules/

# Next.js build output
.next/
**/.next/
out/
**/out/

# Generic frontend build output
dist/
**/dist/

# Test coverage reports
coverage/
**/coverage/

# Vercel and common cache directories
.vercel/
**/.vercel/
.cache/
**/.cache/
.turbo/
**/.turbo/
