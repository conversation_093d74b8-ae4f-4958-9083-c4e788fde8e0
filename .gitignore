# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
tools/output

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*



# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Additional cache directories
.cache/
.eslintcache
.turbo/

# Additional build outputs
dist/

# Environment files (keep .env.example)
.env.local
.env.*.local

# Package manager debug logs
pnpm-debug.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
Thumbs.db

# Test coverage
.nyc_output/

# Temporary files
*.tmp
*.temp
