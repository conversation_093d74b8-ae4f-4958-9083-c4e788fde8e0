# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage
.nyc_output/

# next.js
/.next/
/out/

# production
/build
dist/
tools/output

# misc
.DS_Store
Thumbs.db
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# cache directories
.cache/
.eslintcache
.turbo/

# Environment files (keep .env.example)
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp
