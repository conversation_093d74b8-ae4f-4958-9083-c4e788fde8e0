# Observability Deployment Guide

## 📋 Summary of Changes Made

### ✅ Files Modified:
1. **`../gitops-argocd-apps/deployments/donation-receipt-backend/overlays/dev/deployment.yaml`**
   - Added Prometheus annotations
   - Added OpenTelemetry environment variables

### ✅ Files Created:
1. **`prometheus-agent-standalone-updated.yaml`** - Updated Prometheus agent config
2. **`promtail-values-updated.yaml`** - Updated Promtail config
3. **`tempo-agent-updated.yaml`** - Updated Tempo agent config

## 🚀 Deployment Steps

### Step 1: Deploy Central Monitoring (Monitoring Cluster)

Switch to monitoring cluster:
```bash
kubectl config use-context do-blr1-k8s-monitoring-cluster
```

#### 1.1 Install Prometheus Stack
```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

helm install kube-prometheus-stack prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values kube-prom-values.yaml
```

#### 1.2 Install Loki
```bash
helm repo add grafana https://grafana.github.io/helm-charts
helm repo update

helm install loki grafana/loki \
  --namespace monitoring \
  --values loki-values.yaml
```

#### 1.3 Install Tempo
```bash
helm install tempo grafana/tempo \
  --namespace monitoring \
  --values tempo-values.yaml
```

### Step 2: Deploy Agents (Dev/Staging Cluster)

Switch to dev/staging cluster:
```bash
kubectl config use-context do-blr1-k8s-dev-staging-cluster
```

#### 2.1 Deploy Prometheus Agent
```bash
# Copy the updated file to your DigitalOceanSetupRelatedFiles directory
cp prometheus-agent-standalone-updated.yaml "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\prometheus-agent-standalone.yaml"

# Apply the configuration
kubectl apply -f "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\prometheus-agent-standalone.yaml"
```

#### 2.2 Deploy Promtail
```bash
# Copy the updated file
cp promtail-values-updated.yaml "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\promtail-values.yaml"

# Install Promtail
helm install promtail grafana/promtail \
  --namespace monitoring \
  --create-namespace \
  --values "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\promtail-values.yaml"
```

#### 2.3 Deploy Tempo Agent
```bash
# Copy the updated file
cp tempo-agent-updated.yaml "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\tempo-agent.yaml"

# Install Tempo Agent
helm install tempo-agent grafana/tempo \
  --namespace monitoring \
  --values "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\tempo-agent.yaml"
```

### Step 3: Deploy Application with Observability

#### 3.1 Build and Push Application
```bash
# Build the application
./gradlew clean build -x test

# Build Docker image
docker build -t registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend:latest .

# Push to registry
docker push registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend:latest
```

#### 3.2 Deploy via ArgoCD
The deployment will automatically pick up the changes from the git repository.

## 🔍 Verification Commands

### Check Monitoring Cluster Services
```bash
kubectl config use-context do-blr1-k8s-monitoring-cluster

# Check Prometheus
kubectl get pods -n monitoring | grep prometheus

# Check Grafana
kubectl get pods -n monitoring | grep grafana

# Check Loki
kubectl get pods -n monitoring | grep loki

# Check Tempo
kubectl get pods -n monitoring | grep tempo
```

### Check Dev/Staging Cluster Agents
```bash
kubectl config use-context do-blr1-k8s-dev-staging-cluster

# Check Prometheus Agent
kubectl get pods -n monitoring | grep prometheus-agent

# Check Promtail
kubectl get pods -n monitoring | grep promtail

# Check Tempo Agent
kubectl get pods -n monitoring | grep tempo-agent
```

### Check Application Deployment
```bash
kubectl config use-context do-blr1-k8s-dev-staging-cluster

# Check application pods
kubectl get pods -n donation-receipt-backend-dev

# Check application logs
kubectl logs -l app=donation-receipt-backend -n donation-receipt-backend-dev -f
```

## 🌐 Access URLs

### Monitoring Cluster (Central Monitoring)

#### Grafana
```bash
# Get Grafana LoadBalancer IP
kubectl config use-context do-blr1-k8s-monitoring-cluster
kubectl get svc -n monitoring | grep grafana
```
**URL**: `http://<GRAFANA_LOADBALANCER_IP>:80`
**Default Credentials**: 
- Username: `admin`
- Password: `prom-operator`

#### Prometheus
```bash
# Get Prometheus LoadBalancer IP
kubectl get svc -n monitoring | grep prometheus
```
**URL**: `http://<PROMETHEUS_LOADBALANCER_IP>:9090`

#### Loki
```bash
# Get Loki LoadBalancer IP
kubectl get svc -n monitoring | grep loki
```
**URL**: `http://<LOKI_LOADBALANCER_IP>:3100`

#### Tempo
```bash
# Get Tempo LoadBalancer IP
kubectl get svc -n monitoring | grep tempo
```
**URL**: `http://<TEMPO_LOADBALANCER_IP>:3200`

### Dev/Staging Cluster

#### Application Health Check
```bash
# Get application service IP
kubectl config use-context do-blr1-k8s-dev-staging-cluster
kubectl get svc -n donation-receipt-backend-dev
```
**Health URL**: `http://<APP_LOADBALANCER_IP>:8080/pheart/actuator/health`
**Metrics URL**: `http://<APP_LOADBALANCER_IP>:8080/pheart/actuator/prometheus`

## 🧪 Test Observability

### 1. Test Application Endpoints
```bash
# Test health endpoint
curl http://<APP_LOADBALANCER_IP>:8080/pheart/actuator/health

# Test metrics endpoint
curl http://<APP_LOADBALANCER_IP>:8080/pheart/actuator/prometheus

# Test observability endpoints
curl -X POST "http://<APP_LOADBALANCER_IP>:8080/pheart/observability/test-donation-receipt?donorId=123&amount=100.50"
curl -X POST "http://<APP_LOADBALANCER_IP>:8080/pheart/observability/test-database-operation?tableName=donors&operation=SELECT"
curl -X POST "http://<APP_LOADBALANCER_IP>:8080/pheart/observability/test-http-client?url=https://api.example.com&method=GET"
```

### 2. Verify in Grafana

#### Add Data Sources:
1. **Prometheus**: `http://prometheus-kube-prometheus-stack-prometheus.monitoring.svc.cluster.local:9090`
2. **Loki**: `http://loki.monitoring.svc.cluster.local:3100`
3. **Tempo**: `http://tempo.monitoring.svc.cluster.local:3200`

#### Create Dashboards:
1. **Application Metrics Dashboard**
2. **Application Logs Dashboard**
3. **Distributed Tracing Dashboard**

## 📊 Expected Results

After deployment, you should see:

1. **Prometheus**: Metrics from your application
2. **Grafana**: Dashboards showing application metrics
3. **Loki**: Application logs
4. **Tempo**: Distributed traces from your application

## 🔧 Troubleshooting

### Check Prometheus Agent
```bash
kubectl logs -n monitoring deployment/prometheus-agent
```

### Check Promtail
```bash
kubectl logs -n monitoring deployment/promtail
```

### Check Tempo Agent
```bash
kubectl logs -n monitoring deployment/tempo-agent
```

### Check Application
```bash
kubectl logs -n donation-receipt-backend-dev deployment/donation-receipt-backend-dev
```

## ✅ Success Criteria

- [ ] Prometheus scraping metrics from application
- [ ] Grafana showing application dashboards
- [ ] Loki collecting application logs
- [ ] Tempo showing distributed traces
- [ ] Application health checks passing
- [ ] Observability test endpoints working

## 🎯 Next Steps

1. Create custom Grafana dashboards for your application
2. Set up alerting rules in Prometheus
3. Configure log retention policies in Loki
4. Set up trace sampling policies in Tempo
5. Monitor resource usage and scale as needed 