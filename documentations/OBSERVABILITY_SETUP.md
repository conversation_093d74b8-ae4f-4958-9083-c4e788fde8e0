# Observability Setup for PureHeart Donation Receipt Service

This document describes the observability implementation for the PureHeart Donation Receipt Service using OpenTelemetry, Micrometer, and industry best practices.

## Overview

The application has been instrumented with OpenTelemetry to provide comprehensive observability (metrics, traces, and logs). The implementation follows industry standards and is based on proven patterns from the ai-spring-backend project.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Spring Boot   │───▶│   Prometheus    │───▶│     Grafana     │
│   Application   │    │   (Metrics)     │    │  (Visualization)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────▶│     Tempo       │──────────────┘
                        │  (Tracing DB)   │
                        └─────────────────┘
```

## Components

### 1. OpenTelemetry Dependencies
- **Location**: `build.gradle`
- **Purpose**: Provides core OpenTelemetry API and SDK
- **Features**: 
  - OpenTelemetry BOM for dependency management
  - OTLP exporter for sending traces
  - Micrometer OpenTelemetry bridge for Spring Boot integration

### 2. Tracing Utility
- **Location**: `src/main/java/com/chidhagni/utils/TracingUtil.java`
- **Purpose**: Provides utility methods for adding tracing context
- **Features**:
  - Method execution tracing
  - Database operation tracing
  - HTTP client operation tracing
  - Custom event and attribute addition

### 3. Metrics Configuration
- **Location**: `src/main/java/com/chidhagni/config/MetricsConfig.java`
- **Purpose**: Defines custom business metrics
- **Features**:
  - Pre-defined counters for business operations
  - Timers for performance monitoring
  - Custom metrics for donation receipts, payments, emails, SMS

### 4. Example Implementation
- **Location**: `src/main/java/com/chidhagni/donationreceipt/services/ObservabilityService.java`
- **Purpose**: Demonstrates observability integration
- **Features**:
  - Complete tracing integration example
  - Business logic tracing
  - Error handling with tracing
  - Custom metrics usage

## Configuration

### Application Properties

The OpenTelemetry configuration is environment-specific:

#### Local Development (`application-local.properties`)
```properties
# === OpenTelemetry Configuration ===
otel.traces.exporter=otlp
otel.metrics.exporter=none
otel.logs.exporter=none
otel.exporter.otlp.endpoint=http://localhost:4317
otel.exporter.otlp.protocol=grpc
otel.resource.attributes=service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=local
otel.traces.sampler=always_on
otel.traces.sampler.arg=1.0
```

#### Development Environment (`application-dev.properties`)
```properties
# === OpenTelemetry Configuration ===
otel.traces.exporter=otlp
otel.metrics.exporter=none
otel.logs.exporter=none
otel.exporter.otlp.endpoint=http://prometheus-agent:4317
otel.exporter.otlp.protocol=grpc
otel.resource.attributes=service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=dev
otel.traces.sampler=always_on
otel.traces.sampler.arg=1.0
```

#### Production Environment (`application-prod.properties`)
```properties
# === OpenTelemetry Configuration ===
otel.traces.exporter=otlp
otel.metrics.exporter=none
otel.logs.exporter=none
otel.exporter.otlp.endpoint=http://prometheus-agent:4317
otel.exporter.otlp.protocol=grpc
otel.resource.attributes=service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=prod
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=0.1
```

## Usage

### Automatic Instrumentation

The following components are automatically instrumented by the OpenTelemetry Java Agent:
- HTTP requests/responses
- Database operations (via JDBC)
- Spring Security operations
- Actuator endpoints
- JVM metrics
- System metrics

### Custom Spans

Use the `TracingUtil` class to add custom tracing context:

```java
@Autowired
private TracingUtil tracingUtil;

// Trace a method execution
public DonationReceipt createDonationReceipt(CreateDonationRequest request) {
    return tracingUtil.traceMethod("createDonationReceipt", () -> {
        // Add business context
        tracingUtil.setAttribute("donor.id", request.getDonorId());
        tracingUtil.setAttribute("donation.amount", String.valueOf(request.getAmount()));
        tracingUtil.addEvent("donation_receipt.creation.started");
        
        // Your business logic here
        DonationReceipt receipt = donationService.createReceipt(request);
        
        tracingUtil.addEvent("donation_receipt.creation.completed");
        return receipt;
    });
}

// Trace database operations
public List<Donor> findDonorsByType(String type) {
    return tracingUtil.traceDatabaseOperation("findDonorsByType", "donors", () -> {
        return donorRepository.findByType(type);
    });
}

// Trace HTTP client operations
public PaymentResponse processPayment(PaymentRequest request) {
    return tracingUtil.traceHttpClientOperation(
        "processPayment", 
        "https://payment-gateway.com/api/payments", 
        "POST", 
        () -> paymentClient.process(request)
    );
}
```

### Custom Metrics

Use the pre-defined metrics from `MetricsConfig`:

```java
@Autowired
private Counter donationReceiptCounter;

@Autowired
private Timer donationReceiptProcessingTimer;

public void createDonationReceipt(String donorId, double amount) {
    Timer.Sample sample = Timer.start(meterRegistry);
    try {
        // Process donation receipt
        donationReceiptService.process(donorId, amount);
        
        // Increment counter
        donationReceiptCounter.increment();
    } finally {
        sample.stop(donationReceiptProcessingTimer);
    }
}
```

### Adding Attributes to Current Span

```java
// Set attributes on the current span
tracingUtil.setAttribute("operation.type", "donation_creation");
tracingUtil.setAttribute("donor.email", donor.getEmail());

// Add events
tracingUtil.addEvent("validation.completed");
tracingUtil.addEvent("database.insert.started", "table", "donation_receipts");
```

## Local Testing

### Prerequisites

1. **OpenTelemetry Collector** (optional for local testing)
2. **Prometheus** (for metrics)
3. **Tempo** (for traces)
4. **Grafana** (for visualization)

### Quick Start with Docker Compose

Create a `docker-compose.yml` for local testing:

```yaml
version: '3.8'
services:
  otel-collector:
    image: otel/opentelemetry-collector:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"
      - "4318:4318"

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  tempo:
    image: grafana/tempo:latest
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./tempo.yaml:/etc/tempo.yaml
    ports:
      - "3200:3200"

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
```

### Running the Application

1. **Build the application**:
   ```bash
   ./gradlew clean build -x test
   ```

2. **Download OpenTelemetry Java Agent**:
   ```bash
   wget -O opentelemetry-javaagent.jar \
     https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar
   ```

3. **Run with Java Agent**:
   ```bash
   java -javaagent:opentelemetry-javaagent.jar \
     -jar build/libs/*.jar \
     --spring.profiles.active=local
   ```

### Verification

1. **Metrics**: Visit `http://localhost:8092/pheart/actuator/prometheus`
2. **Health**: Visit `http://localhost:8092/pheart/actuator/health`
3. **Traces**: Check Tempo at `http://localhost:3200`
4. **Grafana**: Visit `http://localhost:3000`

## Kubernetes Deployment

### Dockerfile

The application uses a Dockerfile that includes the OpenTelemetry Java Agent:

```dockerfile
FROM eclipse-temurin:21-jre

WORKDIR /app

# Download the OpenTelemetry Java agent
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /otel/opentelemetry-javaagent.jar

# Copy your Spring Boot JAR
COPY build/libs/*.jar /app/app.jar

# Use the agent in the entrypoint
ENTRYPOINT ["java", "-javaagent:/otel/opentelemetry-javaagent.jar", "-jar", "/app/app.jar"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: donation-receipt-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: donation-receipt-service
  template:
    metadata:
      labels:
        app: donation-receipt-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8092"
        prometheus.io/path: "/pheart/actuator/prometheus"
    spec:
      containers:
      - name: donation-receipt-service
        image: donation-receipt-service:latest
        ports:
        - containerPort: 8092
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: OTEL_SERVICE_NAME
          value: "donation-receipt-service"
        - name: OTEL_TRACES_EXPORTER
          value: "otlp"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://prometheus-agent:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_TRACES_SAMPLER
          value: "traceidratio"
        - name: OTEL_TRACES_SAMPLER_ARG
          value: "0.1"
```

## Available Metrics

### Business Metrics
- `donation_receipt_created_total` - Total donation receipts created
- `donor_registration_total` - Total donor registrations
- `payment_success_total` - Successful payments
- `payment_failure_total` - Failed payments
- `email_sent_total` - Emails sent
- `sms_sent_total` - SMS sent
- `api_requests_total` - API requests
- `api_errors_total` - API errors

### Performance Metrics
- `donation_receipt_processing_duration` - Donation receipt processing time
- `payment_processing_duration` - Payment processing time
- `http_server_duration` - HTTP request duration

### System Metrics (Automatic)
- JVM metrics (memory, GC, threads)
- Process metrics (CPU, memory)
- System metrics (CPU, memory, disk)

## Prometheus Queries

### Business Metrics
```promql
# Donation receipts per minute
rate(donation_receipt_created_total[5m])

# Payment success rate
rate(payment_success_total[5m]) / (rate(payment_success_total[5m]) + rate(payment_failure_total[5m]))

# API error rate
rate(api_errors_total[5m]) / rate(api_requests_total[5m])
```

### Performance Metrics
```promql
# 95th percentile response time
histogram_quantile(0.95, rate(http_server_duration_bucket[5m]))

# Average payment processing time
rate(payment_processing_duration_sum[5m]) / rate(payment_processing_duration_count[5m])
```

## Grafana Dashboards

### Key Dashboards to Create

1. **Service Overview Dashboard**
   - Request rate, error rate, response time
   - Business metrics (donations, payments)
   - System metrics (CPU, memory, JVM)

2. **Business Metrics Dashboard**
   - Donation receipts created
   - Payment success/failure rates
   - Email/SMS sent counts

3. **Performance Dashboard**
   - Response time percentiles
   - Database operation timing
   - External service calls

### Example Dashboard Queries

```json
{
  "panels": [
    {
      "title": "Donation Receipts Created",
      "targets": [
        {
          "expr": "rate(donation_receipt_created_total[5m])",
          "legendFormat": "receipts/sec"
        }
      ]
    },
    {
      "title": "Payment Success Rate",
      "targets": [
        {
          "expr": "rate(payment_success_total[5m]) / (rate(payment_success_total[5m]) + rate(payment_failure_total[5m])) * 100",
          "legendFormat": "success rate %"
        }
      ]
    }
  ]
}
```

## Alerting Rules

### Example Prometheus Alert Rules

```yaml
groups:
- name: donation-receipt-service
  rules:
  - alert: HighErrorRate
    expr: rate(api_errors_total[5m]) / rate(api_requests_total[5m]) > 0.05
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }}%"

  - alert: SlowResponseTime
    expr: histogram_quantile(0.95, rate(http_server_duration_bucket[5m])) > 2
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Slow response time detected"
      description: "95th percentile response time is {{ $value }}s"

  - alert: PaymentFailureRate
    expr: rate(payment_failure_total[5m]) / (rate(payment_success_total[5m]) + rate(payment_failure_total[5m])) > 0.1
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "High payment failure rate"
      description: "Payment failure rate is {{ $value }}%"
```

## Best Practices

### 1. Sampling Strategy
- **Development**: 100% sampling for debugging
- **Production**: 10-20% sampling for cost control
- **High-traffic services**: Implement adaptive sampling

### 2. Span Naming
- Use descriptive operation names
- Follow consistent naming conventions
- Include service name in span names

### 3. Attributes
- Add business context (user ID, request ID)
- Include performance indicators
- Avoid sensitive data in attributes

### 4. Error Handling
- Always set span status on errors
- Include error details in span attributes
- Use span events for important state changes

### 5. Performance
- Keep span attributes minimal
- Use batch processing for high-volume traces
- Monitor agent overhead

## Troubleshooting

### Common Issues

1. **No traces appearing**:
   - Check OpenTelemetry configuration
   - Verify collector is running and accessible
   - Check network connectivity

2. **High memory usage**:
   - Adjust batch processing settings
   - Reduce sampling rate in production
   - Increase resource limits

3. **Slow trace ingestion**:
   - Increase batch size
   - Reduce batch delay
   - Scale collector horizontally

### Debug Commands

```bash
# Check application logs
kubectl logs -l app=donation-receipt-service -f

# Test OTLP endpoint
kubectl port-forward svc/prometheus-agent 4317:4317
# Then use otel-collector-contrib to send test traces

# Check metrics endpoint
curl http://localhost:8092/pheart/actuator/prometheus

# Check health endpoint
curl http://localhost:8092/pheart/actuator/health
```

## Future Enhancements

1. **Trace Correlation**:
   - Add correlation IDs to logs
   - Implement log-trace correlation
   - Add business transaction tracking

2. **Advanced Sampling**:
   - Implement adaptive sampling
   - Add sampling rules based on attributes
   - Implement head-based sampling

3. **Performance Optimization**:
   - Implement span batching
   - Add span compression
   - Optimize attribute storage

4. **Integration**:
   - Add Jaeger UI integration
   - Implement trace export to external systems
   - Add trace analytics and reporting

## Conclusion

This observability setup provides comprehensive monitoring for the PureHeart Donation Receipt Service using industry-standard tools and practices. The implementation is based on proven patterns and provides:

- **Automatic instrumentation** via OpenTelemetry Java Agent
- **Custom business metrics** via Micrometer
- **Structured logging** for better analysis
- **Distributed tracing** for request flow analysis
- **Scalable architecture** for production deployment

The setup is ready for both local development and production deployment in Kubernetes environments. 