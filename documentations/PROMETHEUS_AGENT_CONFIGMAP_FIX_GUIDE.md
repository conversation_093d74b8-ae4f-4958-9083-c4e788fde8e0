# Prometheus Agent ConfigMap Fix Guide

## Problem Summary
When deploying Prometheus Agent using Helm charts, the ConfigMap often gets corrupted with:
- **Duplicate `global:` sections** causing YAML parsing errors
- **Wrong external IP addresses** for central Prometheus remote_write
- **Chart defaults overriding custom configuration**

## Root Cause
The `prometheus-community/prometheus` Helm chart merges default configuration with custom values, leading to invalid YAML and stale IP addresses.

## Solution: Direct YAML Deployment

### Step 1: Create Clean ConfigMap and Deployment

Create `prometheus-agent-clean.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-agent-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      evaluation_interval: 30s
      external_labels:
        cluster: YOUR_CLUSTER_NAME
        project: YOUR_PROJECT_NAME
    remote_write:
      - url: http://YOUR_CENTRAL_PROMETHEUS_IP:9090/api/v1/write
        queue_config:
          max_samples_per_send: 1000
          max_shards: 200
          capacity: 2500
    scrape_configs:
      - job_name: YOUR_APP_NAME-direct
        metrics_path: /YOUR_METRICS_PATH
        scrape_interval: 30s
        static_configs:
          - targets: ['YOUR_SERVICE.YOUR_NAMESPACE.svc.cluster.local:YOUR_PORT']
        relabel_configs:
          - source_labels: [__address__]
            target_label: app
            replacement: YOUR_APP_NAME
          - source_labels: [__address__]
            target_label: project
            replacement: YOUR_PROJECT_NAME
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus-agent
  namespace: monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus-agent
rules:
- apiGroups: [""]
  resources: [nodes, nodes/proxy, services, endpoints, pods]
  verbs: ["get", "list", "watch"]
- apiGroups: [extensions]
  resources: [ingresses]
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus-agent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-agent
subjects:
- kind: ServiceAccount
  name: prometheus-agent
  namespace: monitoring
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus-agent
  namespace: monitoring
  labels:
    app: prometheus-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus-agent
  template:
    metadata:
      labels:
        app: prometheus-agent
    spec:
      serviceAccountName: prometheus-agent
      containers:
      - name: prometheus-agent
        image: prom/prometheus:v3.5.0
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.enable-lifecycle'
          - '--web.listen-address=0.0.0.0:9090'
          - '--log.level=info'
        ports:
        - containerPort: 9090
          name: web
        resources:
          limits:
            cpu: 200m
            memory: 200Mi
          requests:
            cpu: 50m
            memory: 100Mi
        volumeMounts:
        - name: config-volume
          mountPath: /etc/prometheus
        - name: storage-volume
          mountPath: /prometheus
      volumes:
      - name: config-volume
        configMap:
          name: prometheus-agent-config
      - name: storage-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-agent
  namespace: monitoring
  labels:
    app: prometheus-agent
spec:
  ports:
  - port: 9090
    targetPort: 9090
    name: web
  selector:
    app: prometheus-agent
```

### Step 2: Customize Configuration

Replace these placeholders:
- `YOUR_CLUSTER_NAME`: e.g., `dev-staging`, `production`
- `YOUR_PROJECT_NAME`: e.g., `donation-receipt-backend`
- `YOUR_CENTRAL_PROMETHEUS_IP`: Get from central cluster: `kubectl get svc -n monitoring | findstr prometheus`
- `YOUR_APP_NAME`: Your application name
- `YOUR_METRICS_PATH`: e.g., `/actuator/prometheus`, `/metrics`
- `YOUR_SERVICE.YOUR_NAMESPACE.svc.cluster.local:YOUR_PORT`: Your app's service endpoint

### Step 3: Deploy

```bash
# Delete any existing Helm-managed Prometheus agent
helm uninstall prometheus-agent -n monitoring

# Apply the clean configuration
kubectl apply -f prometheus-agent-clean.yaml

# Verify deployment
kubectl get pods -n monitoring -l app=prometheus-agent
kubectl logs -l app=prometheus-agent -n monitoring
```

### Step 4: Verify Configuration

```bash
# Check ConfigMap has correct IP
kubectl get configmap prometheus-agent-config -n monitoring -o yaml

# Check pod is using correct config
kubectl exec -it POD_NAME -n monitoring -- cat /etc/prometheus/prometheus.yml

# Port-forward to check targets
kubectl port-forward POD_NAME -n monitoring 9091:9090
# Then visit http://localhost:9091/targets
```

### Step 5: Troubleshooting

**If pod fails to start:**
```bash
kubectl logs POD_NAME -n monitoring
```

**Common issues:**
- Wrong Prometheus image version
- Invalid YAML in ConfigMap
- Missing RBAC permissions
- Wrong central Prometheus IP

**To update configuration:**
```bash
# Delete old ConfigMap
kubectl delete configmap prometheus-agent-config -n monitoring

# Apply updated configuration
kubectl apply -f prometheus-agent-clean.yaml

# Restart pods
kubectl delete pod -l app=prometheus-agent -n monitoring
```

## Why This Works
- **No Helm chart conflicts**: Direct YAML deployment
- **Clean configuration**: Single global section, no merging
- **Explicit IP control**: Manual IP management prevents stale references
- **Standard Prometheus**: Uses official image with correct arguments

## Multi-Cluster Deployment
1. Use this same YAML in each cluster
2. Update `cluster` label and central Prometheus IP for each cluster
3. Customize scrape configs for each cluster's applications
