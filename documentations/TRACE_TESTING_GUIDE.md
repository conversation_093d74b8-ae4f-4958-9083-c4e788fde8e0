# PureHeart Tracing - Testing Guide

## Overview

This guide provides step-by-step instructions to test the distributed tracing implementation in PureHeart and verify traces in Grafana.

## Configuration Summary

Your PureHeart project is now configured to:

✅ **Send traces to Tempo Agent**: `http://tempo-agent.monitoring.svc.cluster.local:4317`  
✅ **Forward to main Tempo**: `***************:4317`  
✅ **Visualize in Grafana**: Connected Tempo datasource  
✅ **Create custom spans**: Enhanced TracingUtil with actual OpenTelemetry spans  
✅ **Verify configuration**: OpenTelemetryUtil for startup checks  

## Testing Steps

### 1. Deploy and Start the Application

```bash
# Build the application
./gradlew clean build -x test

# Deploy to Kubernetes (or run locally)
kubectl apply -f your-deployment.yaml
```

**Expected startup logs:**
```
🔍 OpenTelemetryUtil initialized for donation-receipt-service
📡 Checking OpenTelemetry configuration...
✅ OpenTelemetry API is available
✅ OpenTelemetry SDK is available
✅ OTLP Exporter is available
✅ OpenTelemetry Java Agent detected
✅ OTLP Endpoint configured: http://tempo-agent.monitoring.svc.cluster.local:4317
🎯 OpenTelemetry setup verification complete
📊 Traces will be sent to Tempo Agent in monitoring namespace
🔗 Expected flow: Application → Tempo Agent → Tempo → Grafana
```

### 2. Test Basic Health Check

```bash
curl -X GET "http://your-app-url/pheart/observability/health"
```

**Expected response:**
```json
{
  "status": "UP",
  "observability": "enabled",
  "tracing": "enabled",
  "metrics": "enabled",
  "service": "donation-receipt-service",
  "version": "1.0.0",
  "opentelemetry.api": "available",
  "timestamp": 1704067200000
}
```

### 3. Generate Test Traces

#### Simple Donation Test
```bash
curl -X POST "http://your-app-url/pheart/observability/test-donation-receipt" \
  -d "donorId=TEST_DONOR_001&amount=500.00"
```

#### Comprehensive Trace Test
```bash
curl -X POST "http://your-app-url/pheart/observability/test-comprehensive-trace"
```

#### Business Scenario Tests
```bash
# Test donation scenario
curl -X POST "http://your-app-url/pheart/observability/test-business-scenario" \
  -d "scenario=donation&donorType=individual&amount=1000"

# Test payment scenario  
curl -X POST "http://your-app-url/pheart/observability/test-business-scenario" \
  -d "scenario=payment&donorType=corporate&amount=5000"

# Test email scenario
curl -X POST "http://your-app-url/pheart/observability/test-business-scenario" \
  -d "scenario=email&donorType=individual&amount=250"
```

### 4. Verify Traces in Grafana

#### Step 1: Access Grafana
- Open your Grafana instance
- Navigate to **Explore** (compass icon)

#### Step 2: Select Tempo Data Source
- Choose **Tempo** from the data source dropdown

#### Step 3: Search for Traces

**Option A: Service Search**
- Service Name: `donation-receipt-service`
- Operation Name: Leave blank or try:
  - `comprehensive_trace_test`
  - `business_scenario_test`
  - `observability_health_check`

**Option B: TraceQL Queries**
```traceql
# Find all traces from donation service
{.service.name="donation-receipt-service"}

# Find comprehensive test traces
{.service.name="donation-receipt-service" && .name="comprehensive_trace_test"}

# Find business scenario traces
{.service.name="donation-receipt-service" && .name="business_scenario_test"}

# Find traces with specific attributes
{.service.name="donation-receipt-service" && .test.type="comprehensive"}

# Find traces with donation processing
{.service.name="donation-receipt-service" && .donation.amount=~".*"}
```

**Option C: Time Range Search**
- Set time range to **Last 15 minutes** or **Last 1 hour**
- Click **Run Query**

### 5. Expected Trace Structure

When you view a trace in Grafana, you should see:

#### Comprehensive Test Trace Structure:
```
🔹 comprehensive_trace_test (root span)
  ├── 🔹 processDonationReceipt (child span)
  ├── 🔹 simulateDatabaseOperation (donation_receipts, SELECT)
  ├── 🔹 simulateDatabaseOperation (donors, UPDATE)  
  ├── 🔹 simulateHttpClientOperation (payment API)
  └── 🔹 simulateHttpClientOperation (email API)
```

#### Expected Span Attributes:
- `test.id`: UUID
- `test.type`: "comprehensive"
- `donation.amount`: "1000.0"
- `donation.currency`: "USD"
- `donor.type`: "individual"
- `operation.type`: "method|database|http_client"

#### Expected Events:
- `test.initialization.completed`
- `donation.processing.started`
- `donation.processing.completed`
- `database.operations.started`
- `database.operations.completed`
- `external.calls.started`
- `external.calls.completed`
- `test.completed.successfully`

### 6. Troubleshooting

#### If No Traces Appear:

**Check 1: Tempo Agent Logs**
```bash
kubectl logs -n monitoring -l app=tempo-agent -f
```
Expected: No errors, traces being forwarded

**Check 2: Application Logs**  
```bash
kubectl logs -l app=your-pureheart-app -f | grep -i opentelemetry
```
Expected: OpenTelemetry initialization logs

**Check 3: Tempo Main Service**
```bash
# Check if tempo is receiving traces
curl http://***************:3200/api/search?limit=10
```

**Check 4: Network Connectivity**
```bash
# From your application pod
kubectl exec -it your-pod-name -- curl http://tempo-agent.monitoring.svc.cluster.local:4317
```

#### Common Issues:

1. **"No data" in Grafana**
   - Check time range (use last 15 minutes)
   - Verify service name: `donation-receipt-service`
   - Try TraceQL: `{.service.name="donation-receipt-service"}`

2. **Traces appear but no custom spans**
   - Check TracingUtil is properly injected
   - Verify OpenTelemetry API imports

3. **Java Agent not detected**
   - Ensure Dockerfile includes `-javaagent:` parameter
   - Check container startup command

### 7. Performance Testing

Generate load to see multiple traces:

```bash
# Generate 10 traces quickly
for i in {1..10}; do
  curl -X POST "http://your-app-url/pheart/observability/test-comprehensive-trace" &
done
wait

# Generate traces with different scenarios
scenarios=("donation" "payment" "email")
for scenario in "${scenarios[@]}"; do
  for i in {1..5}; do
    curl -X POST "http://your-app-url/pheart/observability/test-business-scenario" \
      -d "scenario=$scenario&amount=$((RANDOM % 5000 + 100))" &
  done
done
wait
```

### 8. Monitoring Metrics

Check application metrics:
```bash
curl http://your-app-url/pheart/actuator/prometheus | grep -E "(donation_receipt|otel)"
```

Expected metrics:
- `donation_receipt_created_total`
- `donation_receipt_processing_duration`
- OpenTelemetry internal metrics

## Success Criteria

✅ **Startup**: OpenTelemetry verification logs appear  
✅ **Health**: `/observability/health` returns successful response  
✅ **Traces**: Test endpoints generate traces visible in Grafana  
✅ **Spans**: Custom spans appear with correct attributes and events  
✅ **Flow**: Traces show complete request flow through services  
✅ **Performance**: No significant application performance impact  

## Next Steps

Once testing is successful:

1. **Remove test endpoints** from production deployments
2. **Integrate tracing** into actual business logic
3. **Create Grafana dashboards** for trace visualization
4. **Set up alerting** for trace ingestion failures
5. **Monitor performance** and adjust sampling rates

## Sample Dashboard Queries

For creating Grafana dashboards:

```promql
# Trace volume
rate(donation_receipt_created_total[5m])

# Error rate from traces
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

# Response time percentiles  
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
```

Your PureHeart application is now fully configured for distributed tracing similar to your AI Spring Backend project! 🎉
