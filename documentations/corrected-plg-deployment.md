# Corrected PLG (Prometheus, Loki, Grafana, Tempo) Deployment Guide

## Overview
This document contains the correct YAML configurations that were successfully deployed and tested on **August 8, 2025**. These configurations resolve the issues encountered with the initial PLG setup.

## Architecture
- **Central Monitoring Cluster**: Runs Prometheus, Loki, Grafana, and Tempo
- **Dev-Staging Cluster**: Runs Prometheus Agent, Tempo Agent, and Promtail to send data to central monitoring

---

## 1. Central Monitoring Cluster Configurations

### 1.1 Prometheus (kube-prom-values.yaml)
**Location**: `C:\Users\<USER>\DigitalOceanSetupRelatedFiles\kube-prom-values.yaml`

```yaml
alertmanager:
  enabled: true
  alertmanagerSpec:
    resources:
      limits:
        cpu: 50m
        memory: 50Mi
      requests:
        cpu: 25m
        memory: 25Mi

grafana:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  service:
    type: LoadBalancer
  # Disable default dashboards
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: 'default'
          orgId: 1
          folder: ''
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards/default
  dashboards:
    default: {} # Empty to disable default dashboards

kubeStateMetrics:
  enabled: false
nodeExporter:
  enabled: false
prometheusOperator:
  enabled: true
kubeletService:
  enabled: false
kubeControllerManager:
  enabled: false
kubeScheduler:
  enabled: false
kubeEtcd:
  enabled: false
kubeProxy:
  enabled: false
coreDns:
  enabled: false
defaultRules:
  create: false

prometheus:
  service:
    type: LoadBalancer
  prometheusSpec:
    maximumStartupDurationSeconds: 300
    serviceMonitorSelectorNilUsesHelmValues: false
    enableRemoteWriteReceiver: true # This allows receiving data from agents
    resources:
      limits:
        cpu: 200m
        memory: 200Mi
      requests:
        cpu: 50m
        memory: 100Mi
    retention: 1d
    replicas: 1
    # Important: Do not add scrapeConfigs: [] here as it overrides enableRemoteWriteReceiver

promtail:
  enabled: false
```

**Deployment Command**:
```bash
helm upgrade kube-prometheus-stack prometheus-community/kube-prometheus-stack \
  -f "C:\Users\<USER>\DigitalOceanSetupRelatedFiles\kube-prom-values.yaml" \
  -n monitoring --set crds.create=true
```

### 1.2 Loki (loki-values-fixed.yaml)
**Location**: `C:\gitops-argocd-apps\k8\loki-values-fixed.yaml`

```yaml
deploymentMode: SingleBinary

loki:
  commonConfig:
    replication_factor: 1
  storage:
    type: 'filesystem'
    filesystem:
      chunks_directory: /var/loki/chunks
      rules_directory: /var/loki/rules
  schemaConfig:
    configs:
      - from: "2024-01-01"
        store: tsdb
        object_store: filesystem
        schema: v13
        index:
          prefix: loki_index_
          period: 24h

singleBinary:
  replicas: 1
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage

# Disable distributed components
backend:
  replicas: 0
read:
  replicas: 0
write:
  replicas: 0
gateway:
  enabled: false

monitoring:
  selfMonitoring:
    enabled: false
  lokiCanary:
    enabled: false

test:
  enabled: false

service:
  type: LoadBalancer
  port: 3100
```

**Deployment Commands**:
```bash
# Uninstall if exists
helm uninstall loki -n monitoring

# Install with corrected values
helm install loki grafana/loki -f "C:\gitops-argocd-apps\k8\loki-values-fixed.yaml" -n monitoring
```

### 1.3 Tempo (tempo-values-fixed.yaml)
**Location**: `C:\gitops-argocd-apps\k8\tempo-values-fixed.yaml`

```yaml
tempo:
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi
  service:
    type: LoadBalancer
    port: 3200
  config: |
    server:
      http_listen_port: 3200
    distributor:
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:4317
            http:
              endpoint: 0.0.0.0:4318
    ingester:
      max_block_bytes: 1_000_000
      max_block_duration: 5m
    compactor:
      compaction:
        block_retention: 24h
    storage:
      trace:
        backend: local
        local:
          path: /var/tempo/traces
```

**Deployment Command**:
```bash
helm upgrade tempo grafana/tempo -f "C:\gitops-argocd-apps\k8\tempo-values-fixed.yaml" -n monitoring
```

---

## 2. Dev-Staging Cluster Agent Configurations

### 2.1 Prometheus Agent (Direct YAML Deployment)
**Location**: `C:\gitops-argocd-apps\monitoring\pureheart-plg\prometheus-agent-direct.yaml`

**⚠️ Important Note**: Use direct YAML deployment instead of Helm chart due to config merging issues.

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-agent-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      scrape_timeout: 10s
      evaluation_interval: 30s
      external_labels:
        cluster: dev-staging
        project: donation-receipt-backend
    
    remote_write:
      - url: http://**************:9090/api/v1/write
        queue_config:
          max_samples_per_send: 1000
          max_shards: 200
          capacity: 2500
    
    scrape_configs:
      # Donation Receipt Backend - Kubernetes Service Discovery
      - job_name: donation-receipt-backend
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - donation-receipt-backend-dev
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: donation-receipt-backend
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            regex: (.+)
            target_label: __metrics_path__
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?:\\d+)?;(\\d+)
            replacement: $1:$2
            target_label: __address__
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
          - source_labels: [__meta_kubernetes_pod_container_name]
            action: replace
            target_label: kubernetes_container_name
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: replace
            target_label: app
      
      # Donation Receipt Backend - Direct Service
      - job_name: donation-receipt-backend-direct
        metrics_path: /pheart/actuator/prometheus
        scrape_interval: 30s
        static_configs:
          - targets: ['donation-receipt-backend-service-dev.donation-receipt-backend-dev.svc.cluster.local:8080']
        relabel_configs:
          - source_labels: [__address__]
            target_label: app
            replacement: donation-receipt-backend
          - source_labels: [__address__]
            target_label: project
            replacement: pureheart
      
      # Kube-state-metrics for cluster monitoring
      - job_name: 'kube-state-metrics'
        static_configs:
          - targets: ['kube-state-metrics.kube-system.svc.cluster.local:8080']
        metrics_path: /metrics
        scrape_interval: 30s
      
      # Node-exporter for node metrics
      - job_name: 'node-exporter'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - monitoring
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
            action: keep
            regex: prometheus-node-exporter
          - source_labels: [__meta_kubernetes_pod_container_port_number]
            action: keep
            regex: "9100"
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus-agent
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus-agent
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus-agent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-agent
subjects:
- kind: ServiceAccount
  name: prometheus-agent
  namespace: monitoring

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus-agent
  namespace: monitoring
  labels:
    app: prometheus-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus-agent
  template:
    metadata:
      labels:
        app: prometheus-agent
    spec:
      serviceAccountName: prometheus-agent
      containers:
      - name: prometheus-agent
        image: prom/prometheus:v3.5.0
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.enable-lifecycle'
          - '--web.listen-address=0.0.0.0:9090'
          - '--log.level=info'
        ports:
        - containerPort: 9090
          name: web
        resources:
          limits:
            cpu: 200m
            memory: 200Mi
          requests:
            cpu: 50m
            memory: 100Mi
        volumeMounts:
        - name: config-volume
          mountPath: /etc/prometheus
        - name: storage-volume
          mountPath: /prometheus
      volumes:
      - name: config-volume
        configMap:
          name: prometheus-agent-config
      - name: storage-volume
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-agent
  namespace: monitoring
  labels:
    app: prometheus-agent
spec:
  ports:
  - port: 9090
    targetPort: 9090
    name: web
  selector:
    app: prometheus-agent
```

**Deployment Command**:
```bash
kubectl apply -f monitoring/pureheart-plg/prometheus-agent-direct.yaml
```

### 2.2 Tempo Agent (Minimal Direct YAML)
**Location**: `C:\gitops-argocd-apps\monitoring\pureheart-plg\tempo-agent-minimal.yaml`

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tempo-agent-config
  namespace: monitoring
data:
  tempo-agent.yaml: |
    server:
      http_listen_port: 3200
    distributor:
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:4317
            http:
              endpoint: 0.0.0.0:4318
    storage:
      trace:
        backend: local
        local:
          path: /var/tempo/traces

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tempo-agent
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tempo-agent
rules:
- apiGroups: [""]
  resources:
  - pods
  - services
  - endpoints
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tempo-agent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tempo-agent
subjects:
- kind: ServiceAccount
  name: tempo-agent
  namespace: monitoring

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tempo-agent
  namespace: monitoring
  labels:
    app: tempo-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tempo-agent
  template:
    metadata:
      labels:
        app: tempo-agent
    spec:
      serviceAccountName: tempo-agent
      containers:
      - name: tempo-agent
        image: grafana/tempo:2.3.1
        args:
          - -config.file=/etc/tempo/tempo-agent.yaml
        ports:
        - containerPort: 3200
          name: http
        - containerPort: 4317
          name: otlp-grpc
        - containerPort: 4318
          name: otlp-http
        resources:
          limits:
            cpu: 50m
            memory: 64Mi
          requests:
            cpu: 25m
            memory: 32Mi
        volumeMounts:
        - name: config-volume
          mountPath: /etc/tempo
        - name: storage-volume
          mountPath: /var/tempo
      volumes:
      - name: config-volume
        configMap:
          name: tempo-agent-config
      - name: storage-volume
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: tempo-agent
  namespace: monitoring
  labels:
    app: tempo-agent
spec:
  ports:
  - port: 3200
    targetPort: 3200
    name: http
  - port: 4317
    targetPort: 4317
    name: otlp-grpc
  - port: 4318
    targetPort: 4318
    name: otlp-http
  selector:
    app: tempo-agent
```

**Deployment Command**:
```bash
kubectl apply -f monitoring/pureheart-plg/tempo-agent-minimal.yaml
```

### 2.3 Promtail (Working Configuration)
**Location**: `C:\gitops-argocd-apps\monitoring\pureheart-plg\promtail-values.yaml`

```yaml
enabled: true
clients:
  - url: http://loki.monitoring.svc.cluster.local:3100/loki/api/v1/push
config:
  file: |
    clients:
      - url: http://loki.monitoring.svc.cluster.local:3100/loki/api/v1/push
```

**Deployment Command**:
```bash
helm upgrade promtail grafana/promtail -f monitoring/pureheart-plg/promtail-values.yaml -n monitoring
```

---

## 3. Application OpenTelemetry Configuration

### 3.1 Backend Deployment Configuration
**Location**: `C:\gitops-argocd-apps\deployments\donation-receipt-backend\overlays\dev\deployment.yaml`

**Key OpenTelemetry Environment Variables**:
```yaml
env:
  # OpenTelemetry Environment Variables
  - name: OTEL_SERVICE_NAME
    value: "donation-receipt-service"  # ⚠️ Use this in TraceQL queries
  - name: OTEL_TRACES_EXPORTER
    value: "otlp"
  - name: OTEL_METRICS_EXPORTER
    value: "none"
  - name: OTEL_LOGS_EXPORTER
    value: "none"
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: "http://tempo-agent.monitoring.svc.cluster.local:4317"
  - name: OTEL_EXPORTER_OTLP_PROTOCOL
    value: "grpc"
  - name: OTEL_TRACES_SAMPLER
    value: "always_on"
  - name: OTEL_TRACES_SAMPLER_ARG
    value: "1.0"
  - name: OTEL_RESOURCE_ATTRIBUTES
    value: "service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=dev"

# Prometheus Annotations
annotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/pheart/actuator/prometheus"
```

---

## 4. Verification Commands

### 4.1 Check Pod Status
```bash
# Central monitoring cluster
kubectl get pods -n monitoring

# Dev-staging cluster  
kubectl get pods -n monitoring
kubectl get pods -n donation-receipt-backend-dev
```

### 4.2 Get External IPs
```bash
kubectl get svc -n monitoring
```

### 4.3 Check Logs
```bash
# Prometheus agent
kubectl logs -l app=prometheus-agent -n monitoring

# Tempo agent
kubectl logs -l app=tempo-agent -n monitoring

# Promtail
kubectl logs -l app=promtail -n monitoring
```

---

## 5. Grafana Data Sources Configuration

### 5.1 External IPs (Update with your actual IPs)
- **Prometheus**: `http://**************:9090`
- **Loki**: `http://[LOKI_EXTERNAL_IP]:3100` (needs external IP fix)
- **Tempo**: `http://[TEMPO_EXTERNAL_IP]:3200`

### 5.2 TraceQL Query Examples
```traceql
# Correct service name (as configured in OTEL_SERVICE_NAME)
{resource.service.name="donation-receipt-service"}

# Filter by environment
{resource.service.name="donation-receipt-service" && resource.deployment.environment="dev"}

# Filter by operation
{resource.service.name="donation-receipt-service" && span.name="HTTP GET"}
```

### 5.3 Prometheus Query Examples
```promql
# Application metrics
up{job="donation-receipt-backend"}
http_server_requests_seconds_count{job="donation-receipt-backend"}
jvm_memory_used_bytes{job="donation-receipt-backend"}

# Remote write check
prometheus_remote_storage_samples_total
```

---

## 6. Troubleshooting Notes

### 6.1 Common Issues Resolved
1. **Prometheus Agent Config**: Helm chart caused duplicate `global:` sections → Fixed with direct YAML
2. **Loki Deployment Mode**: Distributed mode failed without object storage → Fixed with SingleBinary mode
3. **Tempo Agent Crashes**: Memory limits too low + config issues → Fixed with minimal direct YAML
4. **Service Name Mismatch**: TraceQL used wrong service name → Use `donation-receipt-service`
5. **Remote Write URL**: Stale ConfigMap with old IP → Updated to current IP `**************`

### 6.2 Key Success Factors
- Use direct YAML deployments for agents to avoid Helm chart config merging issues
- Ensure `enableRemoteWriteReceiver: true` in central Prometheus
- Use filesystem storage for single-binary Loki deployment
- Match service names between OTEL config and TraceQL queries
- Keep resource limits appropriate for DigitalOcean cluster capacity

---

## 7. Deployment Summary

### 7.1 Central Monitoring Cluster Steps
```bash
# 1. Install Prometheus + Grafana
helm upgrade kube-prometheus-stack prometheus-community/kube-prometheus-stack \
  -f kube-prom-values.yaml -n monitoring --set crds.create=true

# 2. Install Loki (SingleBinary mode)
helm install loki grafana/loki -f loki-values-fixed.yaml -n monitoring

# 3. Install Tempo
helm upgrade tempo grafana/tempo -f tempo-values-fixed.yaml -n monitoring
```

### 7.2 Dev-Staging Cluster Steps
```bash
# 1. Deploy Prometheus Agent (direct YAML)
kubectl apply -f monitoring/pureheart-plg/prometheus-agent-direct.yaml

# 2. Deploy Tempo Agent (minimal direct YAML)
kubectl apply -f monitoring/pureheart-plg/tempo-agent-minimal.yaml

# 3. Deploy Promtail
helm upgrade promtail grafana/promtail -f monitoring/pureheart-plg/promtail-values.yaml -n monitoring
```

---

**Document Created**: August 8, 2025  
**Status**: ✅ Prometheus Agent Working, ✅ Metrics Flowing, ⚠️ Loki External IP Needed, ⚠️ Tempo Agent Needs Stabilization
