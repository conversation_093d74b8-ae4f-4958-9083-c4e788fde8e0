# DigitalOcean Spaces Migration Guide

## Overview

This document provides a comprehensive guide for migrating from MinIO to DigitalOcean Spaces in the Spring Boot application. The implementation maintains the existing `IFileStoreService` interface, ensuring zero breaking changes to consuming services.

## Architecture

### Current Implementation
- **Service Layer**: `IFileStoreService` → `FileStoreService`
- **Repository Layer**: `IFileStoreClient` → `MinioFileStoreClient` OR `DOSpacesFileStoreClient`
- **Configuration**: Conditional beans based on `filestore.provider` property

### Key Components

1. **DOSpacesFileStoreClient**: S3-compatible implementation using AWS SDK
2. **DOSpacesConfig**: Configuration for DigitalOcean Spaces client
3. **Conditional Loading**: `@ConditionalOnProperty` for provider switching

## Configuration

### Environment Variables (Production)

Set these environment variables for production deployment:

```bash
# Required for DigitalOcean Spaces
DO_SPACES_ACCESS_KEY=your_access_key_id
DO_SPACES_SECRET_KEY=your_secret_access_key
DO_SPACES_ENDPOINT=https://your-space.region.digitaloceanspaces.com
DO_SPACES_REGION=your_region

# Provider selection
FILESTORE_PROVIDER=digitalocean
```

### Application Properties

#### Development (application-dev.properties)
```properties
# Switch to DigitalOcean Spaces
filestore.provider=digitalocean

# DigitalOcean Spaces Configuration
filestore.digitalocean.access-key=${DO_SPACES_ACCESS_KEY:ch-pure-heart-dev-access-key}
filestore.digitalocean.secret-key=${DO_SPACES_SECRET_KEY:your_secret_key}
filestore.digitalocean.endpoint=${DO_SPACES_ENDPOINT:https://chidhagni-ph.blr1.digitaloceanspaces.com}
filestore.digitalocean.region=${DO_SPACES_REGION:blr1}
```

#### Production (application-prod.properties)
```properties
# Use DigitalOcean Spaces in production
filestore.provider=${FILESTORE_PROVIDER:digitalocean}

# All credentials from environment variables
filestore.digitalocean.access-key=${DO_SPACES_ACCESS_KEY}
filestore.digitalocean.secret-key=${DO_SPACES_SECRET_KEY}
filestore.digitalocean.endpoint=${DO_SPACES_ENDPOINT:https://chidhagni-ph.blr1.digitaloceanspaces.com}
filestore.digitalocean.region=${DO_SPACES_REGION:blr1}
```

## DigitalOcean Spaces Setup

### 1. Create Space
1. Log into DigitalOcean Control Panel
2. Navigate to Spaces Object Storage
3. Create a new Space:
   - **Name**: `chidhagni-ph`
   - **Region**: `blr1` (Bangalore)
   - **CDN**: Enable if needed for public access

### 2. Generate API Keys
1. Go to API section in DigitalOcean Control Panel
2. Generate new Spaces access keys:
   - **Access Key ID**: `DO801ZPRMHPG6ND9NWT6`
   - **Secret Access Key**: `OMWmZgZdJEVCrNSDSLlTfCSFM+Oun1GImUUt7zSWMrY`

### 3. Configure CORS (if needed)
```json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
      "AllowedHeaders": ["*"],
      "MaxAgeSeconds": 3000
    }
  ]
}
```

## Switching Between Providers

### To MinIO (Default)
```properties
filestore.provider=minio
```

### To DigitalOcean Spaces
```properties
filestore.provider=digitalocean
```

### Runtime Switching
The application automatically loads the appropriate implementation based on the `filestore.provider` property. No code changes required.

## Testing

### Unit Tests
```bash
./gradlew test --tests "*DOSpacesFileStoreClientUTest"
```

### Integration Tests
```bash
# Test with MinIO (default)
./gradlew integrationTest -Dtest.integration.filestore=true

# Test with DigitalOcean Spaces
./gradlew integrationTest \
  -Dtest.integration.filestore=true \
  -Dfilestore.provider=digitalocean \
  -DDO_SPACES_ACCESS_KEY=your_key \
  -DDO_SPACES_SECRET_KEY=your_secret
```

## Security Best Practices

### 1. Credential Management
- **Never** commit credentials to version control
- Use environment variables for all sensitive data
- Rotate access keys regularly
- Use different keys for different environments

### 2. Access Control
- Create separate access keys for dev/staging/prod
- Use minimal required permissions
- Monitor access logs regularly

### 3. Network Security
- Use HTTPS endpoints only
- Configure proper CORS policies
- Consider VPC endpoints for production

## Error Handling

The implementation includes comprehensive error handling:

- **Bucket Not Found**: Clear error message with setup instructions
- **File Not Found**: Graceful handling with appropriate logging
- **Network Issues**: Retry logic and timeout configuration
- **Permission Errors**: Detailed error messages for troubleshooting

## Performance Considerations

### 1. Connection Pooling
The AWS SDK automatically handles connection pooling and reuse.

### 2. Multipart Uploads
Large files (>5MB) automatically use multipart uploads for better performance.

### 3. Regional Optimization
Choose the region closest to your application servers for optimal performance.

## Monitoring and Logging

### Application Logs
```properties
# Enable detailed logging for file operations
logging.level.com.chidhagni.filestore=DEBUG
logging.level.software.amazon.awssdk.request=DEBUG
```

### Metrics to Monitor
- Upload/download success rates
- Response times
- Error rates by operation type
- Storage usage and costs

## Troubleshooting

### Common Issues

1. **Bucket Access Denied**
   - Verify access keys are correct
   - Check bucket permissions
   - Ensure region is correct

2. **File Not Found**
   - Verify object key format
   - Check if file was actually uploaded
   - Confirm bucket name is correct

3. **Connection Timeouts**
   - Check network connectivity
   - Verify endpoint URL
   - Consider increasing timeout values

### Debug Mode
Enable debug logging to troubleshoot issues:
```properties
logging.level.com.chidhagni.filestore=DEBUG
logging.level.software.amazon.awssdk=DEBUG
```

## Migration Checklist

- [ ] Add AWS SDK dependencies to build.gradle
- [ ] Create DigitalOcean Spaces configuration
- [ ] Implement DOSpacesFileStoreClient
- [ ] Add conditional configuration logic
- [ ] Update application properties for all environments
- [ ] Set up environment variables for production
- [ ] Create and configure DigitalOcean Space
- [ ] Generate API access keys
- [ ] Run unit tests
- [ ] Run integration tests
- [ ] Test provider switching
- [ ] Update deployment scripts with new environment variables
- [ ] Monitor application logs after deployment
- [ ] Verify file operations work correctly
- [ ] Plan data migration from MinIO to DigitalOcean Spaces (if needed)

## Cost Comparison

### DigitalOcean Spaces vs MinIO
- **DigitalOcean Spaces**: $5/month for 250GB + $0.02/GB for additional storage
- **MinIO Self-hosted**: Server costs + maintenance overhead
- **Bandwidth**: DigitalOcean includes 1TB outbound transfer

### Cost Optimization Tips
- Enable CDN for frequently accessed files
- Use lifecycle policies for old files
- Monitor usage with DigitalOcean metrics

## Support

For issues or questions:
1. Check application logs for detailed error messages
2. Verify configuration and environment variables
3. Test with integration tests
4. Review DigitalOcean Spaces documentation
5. Contact development team for assistance
