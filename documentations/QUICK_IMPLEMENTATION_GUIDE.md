# Quick PLG Implementation Guide for New Projects

## 🚀 5-Minute Setup Checklist

### 1. Application Changes (5 minutes)

#### Update `build.gradle`
```gradle
dependencies {
    // Add these lines
    implementation platform('io.opentelemetry:opentelemetry-bom:1.32.0')
    implementation 'io.opentelemetry:opentelemetry-api'
    implementation 'io.opentelemetry:opentelemetry-sdk'
    implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
    implementation 'io.micrometer:micrometer-tracing-bridge-otel'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
}
```

#### Update `Dockerfile`
```dockerfile
FROM eclipse-temurin:21-jre
WORKDIR /app
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /otel/opentelemetry-javaagent.jar
COPY build/libs/*.jar /app/app.jar
ENTRYPOINT ["java", "-javaagent:/otel/opentelemetry-javaagent.jar", "-jar", "/app/app.jar"]
```

#### Update `application-dev.properties`
```properties
# === OpenTelemetry Configuration ===
otel.traces.exporter=otlp
otel.metrics.exporter=none
otel.logs.exporter=none
otel.exporter.otlp.endpoint=http://tempo-agent.monitoring.svc.cluster.local:4317
otel.exporter.otlp.protocol=grpc
otel.resource.attributes=service.name=YOUR-SERVICE-NAME,service.version=1.0.0,deployment.environment=dev
otel.traces.sampler=always_on
otel.traces.sampler.arg=1.0

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
```

#### Update Deployment YAML
```yaml
metadata:
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/actuator/prometheus"
spec:
  template:
    spec:
      containers:
      - name: your-app
        env:
        - name: OTEL_SERVICE_NAME
          value: "YOUR-SERVICE-NAME"
        - name: OTEL_TRACES_EXPORTER
          value: "otlp"
        - name: OTEL_METRICS_EXPORTER
          value: "none"
        - name: OTEL_LOGS_EXPORTER
          value: "none"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://tempo-agent.monitoring.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_TRACES_SAMPLER
          value: "always_on"
        - name: OTEL_TRACES_SAMPLER_ARG
          value: "1.0"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=YOUR-SERVICE-NAME,service.version=1.0.0,deployment.environment=dev"
```

### 2. Monitoring Stack (Already Deployed)

Your monitoring stack is already deployed in the monitoring cluster. You just need to update agent configurations.

### 3. Update Agent Configurations (5 minutes)

#### Update Prometheus Agent Config
**File:** `prometheus-agent-config.yaml`
```yaml
# Add your app to scrape_configs
scrape_configs:
  - job_name: 'YOUR-APP-NAME'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: YOUR-APP-NAME
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      # ... rest of relabel configs
```

#### Update Promtail Config
**File:** `promtail-values.yaml`
```yaml
relabel_configs:
  - source_labels: [__meta_kubernetes_pod_label_app]
    action: keep
    regex: "YOUR-APP-NAME"
```

### 4. Deploy Changes (2 minutes)

```bash
# Update prometheus-agent config
kubectl apply -f prometheus-agent-config.yaml

# Update promtail config
helm upgrade promtail grafana/promtail -f promtail-values.yaml -n monitoring

# Deploy your application
kubectl apply -f your-deployment.yaml
```

### 5. Verify (3 minutes)

```bash
# Check if app appears in Prometheus targets
curl "http://************:9090/api/v1/targets" | jq '.data.activeTargets[] | select(.labels.job == "YOUR-APP-NAME")'

# Test Grafana queries
# Prometheus: up{job="YOUR-APP-NAME"}
# Loki: {app="YOUR-APP-NAME"}
# Tempo: {service.name="YOUR-SERVICE-NAME"}
```

## 🔧 Template Files

### Application Properties Template
```properties
# Replace YOUR-SERVICE-NAME with your actual service name
otel.resource.attributes=service.name=YOUR-SERVICE-NAME,service.version=1.0.0,deployment.environment=dev
```

### Deployment Template
```yaml
metadata:
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/actuator/prometheus"
spec:
  template:
    spec:
      containers:
      - name: your-app
        env:
        - name: OTEL_SERVICE_NAME
          value: "YOUR-SERVICE-NAME"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://tempo-agent.monitoring.svc.cluster.local:4317"
```

### Agent Configuration Template
```yaml
# Prometheus Agent scrape config
- job_name: 'YOUR-APP-NAME'
  kubernetes_sd_configs:
    - role: pod
  relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app]
      action: keep
      regex: YOUR-APP-NAME
```

## 🎯 What You Get

After implementing this setup, you'll have:

1. **Automatic Metrics Collection**: JVM metrics, HTTP metrics, custom business metrics
2. **Distributed Tracing**: Automatic trace generation for HTTP requests, database calls
3. **Log Aggregation**: All application logs collected and searchable
4. **Grafana Dashboards**: Visualize metrics, logs, and traces
5. **Alerting**: Set up alerts based on metrics and logs

## 🚨 Common Issues & Quick Fixes

### Issue: Application not appearing in Prometheus targets
**Fix:** Check that your deployment has the correct `app` label and Prometheus annotations

### Issue: RBAC errors in prometheus-agent logs
**Fix:** Apply the RBAC configuration from the main guide

### Issue: OTLP export errors
**Fix:** Ensure `OTEL_METRICS_EXPORTER=none` and `OTEL_LOGS_EXPORTER=none` are set

### Issue: Cross-cluster communication errors
**Fix:** Use LoadBalancer IPs instead of internal service names

## 📊 Verification Commands

```bash
# Check application logs
kubectl logs YOUR-APP-POD -n YOUR-NAMESPACE

# Check prometheus-agent logs
kubectl logs prometheus-agent-POD -n monitoring

# Test application metrics endpoint
curl http://YOUR-APP-URL/actuator/prometheus

# Check Prometheus targets
curl "http://************:9090/api/v1/targets"

# Test Grafana queries
# Prometheus: up{job="YOUR-APP-NAME"}
# Loki: {app="YOUR-APP-NAME"}
# Tempo: {service.name="YOUR-SERVICE-NAME"}
```

## 🎉 Success Criteria

Your PLG setup is working when:

1. ✅ Application appears in Prometheus targets
2. ✅ Grafana queries return data
3. ✅ No RBAC errors in agent logs
4. ✅ Application logs visible in Loki
5. ✅ Traces visible in Tempo

---

**Total Time:** ~15 minutes for a new project
**Reuse:** Monitoring stack is shared across all projects 