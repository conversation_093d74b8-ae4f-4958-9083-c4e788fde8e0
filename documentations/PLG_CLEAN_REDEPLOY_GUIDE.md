# Multi-Cluster PLG Stack: Clean & Redeploy Guide (DigitalOcean)

## Overview
This guide walks you through **fully cleaning and redeploying** the PLG stack (Prometheus, Loki, <PERSON><PERSON>, Te<PERSON>) in a multi-cluster DigitalOcean Kubernetes setup:
- **Monitoring Cluster:** <PERSON>met<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (central)
- **Dev-Staging Cluster:** Prometheus Agent, Tempo Agent, Promtail (per-app)

---

## 1. Clean Up All PLG Components

### 1.1. Delete Helm Releases (Both Clusters)
Replace `<release>` and `<namespace>` as needed:
```sh
# List all Helm releases
helm list -A

# Delete releases in monitoring cluster
helm uninstall kube-prometheus-stack -n monitoring
helm uninstall loki -n monitoring
helm uninstall tempo -n monitoring
helm uninstall grafana -n monitoring  # if separate

# Delete releases in dev-staging cluster
helm uninstall prometheus-agent -n monitoring
helm uninstall tempo-agent -n monitoring
helm uninstall promtail -n monitoring
```

### 1.2. Delete CRDs (Both Clusters)
```sh
kubectl delete crd alertmanagers.monitoring.coreos.com
kubectl delete crd podmonitors.monitoring.coreos.com
kubectl delete crd probes.monitoring.coreos.com
kubectl delete crd prometheuses.monitoring.coreos.com
kubectl delete crd prometheusrules.monitoring.coreos.com
kubectl delete crd servicemonitors.monitoring.coreos.com
kubectl delete crd thanosrulers.monitoring.coreos.com
```

### 1.3. Delete Any Leftover Resources
```sh
kubectl delete ns monitoring --ignore-not-found
kubectl get pvc -A | grep monitoring | awk '{print $2, $1}' | xargs -n2 sh -c 'kubectl delete pvc $0 -n $1'
```

---

## 2. Verify All Resources Are Deleted
```sh
kubectl get all -A | grep -E 'prometheus|grafana|loki|tempo|promtail'
kubectl get crd | grep monitoring.coreos.com
```
All should be gone before proceeding.

---

## 3. Deploy Monitoring Cluster (Central)

### 3.1. Add Helm Repos
```sh
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add grafana https://grafana.github.io/helm-charts
helm repo update
```

### 3.2. Install/Upgrade CRDs (Manual Step)
Download from [Prometheus Operator v0.83.0 CRDs](https://github.com/prometheus-operator/prometheus-operator/tree/v0.83.0/example/prometheus-operator-crd/monitoring.coreos.com) and apply:
```sh
kubectl apply -f <path-to-downloaded-crds-folder>
```

### 3.3. Deploy kube-prometheus-stack (Prometheus, Grafana, Alertmanager)
Create `kube-prom-values.yaml`:
```yaml
prometheus:
  prometheusSpec:
    enableRemoteWriteReceiver: true
    retention: 1d
    replicas: 1
    resources:
      limits:
        cpu: 200m
        memory: 200Mi
      requests:
        cpu: 50m
        memory: 100Mi
  service:
    type: LoadBalancer

grafana:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  service:
    type: LoadBalancer

alertmanager:
  enabled: true
  alertmanagerSpec:
    resources:
      limits:
        cpu: 50m
        memory: 50Mi
      requests:
        cpu: 25m
        memory: 25Mi
```
Deploy:
```sh
helm install kube-prometheus-stack prometheus-community/kube-prometheus-stack -n monitoring --create-namespace -f kube-prom-values.yaml
```

### 3.4. Deploy Loki (Single Binary)
Create `loki-values.yaml` (see Loki docs for minimal config):
```yaml
deploymentMode: SingleBinary
loki:
  storage:
    type: 'filesystem'
    filesystem:
      chunks_directory: /var/loki/chunks
      rules_directory: /var/loki/rules
  schemaConfig:
    configs:
      - from: "2024-01-01"
        store: tsdb
        object_store: filesystem
        schema: v13
        index:
          prefix: loki_index_
          period: 24h
singleBinary:
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
service:
  type: LoadBalancer
  port: 3100
```
Deploy:
```sh
helm install loki grafana/loki -n monitoring -f loki-values.yaml
```

### 3.5. Deploy Tempo
Create `tempo-values.yaml` (minimal):
```yaml
tempo:
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi
  service:
    type: LoadBalancer
    port: 3200
  config: |
    server:
      http_listen_port: 3200
    distributor:
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:4317
            http:
              endpoint: 0.0.0.0:4318
    ingester:
      max_block_bytes: 1000000
      max_block_duration: 5m
    compactor:
      compaction:
        block_retention: 24h
    storage:
      trace:
        backend: local
        local:
          path: /var/tempo/traces
```
Deploy:
```sh
helm install tempo grafana/tempo -n monitoring -f tempo-values.yaml
```

---

## 4. Deploy Dev-Staging Cluster (Per-App Agents)

### 4.1. Prometheus Agent
Create `prometheus-agent-values.yaml`:
```yaml
agent:
  enabled: true
  config:
    global:
      scrape_interval: 30s
      evaluation_interval: 30s
      external_labels:
        cluster: "dev-staging"
        project: "your-app"
    remote_write:
      - url: "http://<central-prometheus-lb>:9090/api/v1/write"
    scrape_configs:
      # Add your scrape configs here
```
Deploy:
```sh
helm install prometheus-agent prometheus-community/prometheus -n monitoring -f prometheus-agent-values.yaml
```

### 4.2. Tempo Agent
Create `tempo-agent-values.yaml` (minimal):
```yaml
resources:
  limits:
    cpu: 10m
    memory: 32Mi
  requests:
    cpu: 10m
    memory: 32Mi
```
Deploy:
```sh
helm install tempo-agent grafana/tempo -n monitoring -f tempo-agent-values.yaml
```

### 4.3. Promtail
Create `promtail-values.yaml` (minimal):
```yaml
resources:
  limits:
    cpu: 10m
    memory: 32Mi
  requests:
    cpu: 10m
    memory: 32Mi
```
Deploy:
```sh
helm install promtail grafana/promtail -n monitoring -f promtail-values.yaml
```

---

## 5. Verify Connectivity and Data Flow
- Check Prometheus UI (`/targets`, `/config`) for remote_write_receiver.
- Check Grafana for metrics, logs, and traces.
- Use `kubectl logs` to debug agent pods.

---

## 6. DigitalOcean Multi-Cluster Tips
- Use `doctl kubernetes cluster kubeconfig save <cluster-id>` to switch contexts.
- Use unique namespaces per environment.
- Use DigitalOcean block storage for persistence.

---

## 7. Troubleshooting
- If remote_write_receiver is missing, re-apply CRDs and restart operator pod.
- If metrics/logs/traces are missing, check agent logs and network connectivity.
- Always upgrade CRDs manually before Helm upgrades.

---

**This guide is copy-paste friendly and covers a full clean redeploy for multi-cluster PLG on DigitalOcean.**

