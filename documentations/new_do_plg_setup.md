# Complete DigitalOcean PLG (<PERSON>met<PERSON>, <PERSON>, <PERSON><PERSON>) Setup Guide

## 📚 **Table of Contents**
1. [What is PLG and Why We Need It](#what-is-plg)
2. [Architecture Overview](#architecture-overview)
3. [Prerequisites](#prerequisites)
4. [Multi-Cluster Setup Strategy](#multi-cluster-setup-strategy)
5. [Step-by-Step Implementation](#step-by-step-implementation)
6. [Configuration Files Explained](#configuration-files-explained)
7. [Verification and Testing](#verification-and-testing)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Best Practices](#best-practices)
10. [Scaling and Maintenance](#scaling-and-maintenance)

---

## 🤔 **What is PLG and Why We Need It?**

### **What is PLG?**
PLG stands for **Prometheus, Loki, Grafana** - a powerful observability stack:

- **📊 Prometheus**: Collects and stores **metrics** (CPU usage, memory, API response times, business metrics)
- **📝 Loki**: Collects and stores **logs** (application logs, error logs, debug information)
- **📈 Grafana**: **Visualizes** metrics and logs in beautiful dashboards and alerts

### **Why Do We Need Observability?**
Without proper monitoring, you're flying blind! Observability helps you:

1. **🔍 Debug Issues Faster**: See exactly what happened when something broke
2. **⚡ Performance Monitoring**: Track how fast your APIs are responding
3. **💰 Cost Optimization**: Monitor resource usage to optimize DigitalOcean costs
4. **🚨 Proactive Alerts**: Get notified before users notice problems
5. **📊 Business Insights**: Track donation receipts created, user registrations, etc.

---

## 🏗️ **Architecture Overview**

### **Our Multi-Cluster Architecture**

We're using **4 separate Kubernetes clusters** on DigitalOcean for optimal resource management and security:

```
┌─────────────────────────────────────────────────────────────────┐
│                    DigitalOcean Kubernetes Clusters             │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────┐    ┌─────────────────────────────────┐  │
│  │ k8s-monitoring      │    │ k8s-dev-staging-cluster        │  │
│  │ (Central PLG)       │    │ (Applications + Agents)        │  │
│  │                     │◄───┤                                 │  │
│  │ • Prometheus        │    │ • donation-receipt-backend     │  │
│  │ • Grafana          │    │ • Prometheus Agent             │  │
│  │ • Loki             │    │ • Promtail (log collector)     │  │
│  │ • Tempo            │    │ • Tempo Agent (trace collector)│  │
│  │                     │    │                                 │  │
│  └─────────────────────┘    └─────────────────────────────────┘  │
│                                                                 │
│  ┌─────────────────────┐    ┌─────────────────────────────────┐  │
│  │ k8s-argocd-arc      │    │ k8s-sonar-semgrep-cluster     │  │
│  │ (GitOps)            │    │ (Security Tools)               │  │
│  │                     │    │                                 │  │
│  │ • ArgoCD           │    │ • SonarQube                    │  │
│  │ • Arc Runner       │    │ • Semgrep                      │  │
│  │                     │    │                                 │  │
│  └─────────────────────┘    └─────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### **Data Flow**
1. **Application** generates metrics, logs, and traces
2. **Agents** (Prometheus Agent, Promtail, Tempo Agent) collect data
3. **Agents send data** to Central Monitoring cluster
4. **Grafana** creates dashboards from the centralized data

---

## 📋 **Prerequisites**

### **Required Tools**
- `kubectl` (Kubernetes CLI)
- `helm` (Package manager for Kubernetes)
- `doctl` (DigitalOcean CLI)
- Docker (for building application images)

### **DigitalOcean Setup**
1. **Kubernetes Clusters** (4 clusters as shown above)
2. **Container Registry** for storing Docker images
3. **Load Balancers** (automatically provisioned)
4. **Block Storage** for persistent data

### **Local Development**
- Windows with PowerShell
- Access to all 4 Kubernetes clusters
- Git repository for configuration files

---

## 🚀 **Multi-Cluster Setup Strategy**

### **Why Multi-Cluster?**
1. **🔒 Security Isolation**: Monitoring tools separated from applications
2. **💰 Cost Optimization**: Different resource requirements per cluster
3. **🔧 Maintenance**: Update monitoring without affecting applications
4. **📈 Scalability**: Scale monitoring and applications independently

### **Cluster Responsibilities**

| Cluster | Purpose | Components | Resource Requirements |
|---------|---------|------------|---------------------|
| `k8s-monitoring-cluster` | Central Observability | Prometheus, Grafana, Loki, Tempo | Medium CPU, High Storage |
| `k8s-dev-staging-cluster` | Applications + Agents | Apps, Prometheus Agent, Promtail, Tempo Agent | High CPU, Medium Storage |
| `k8s-argocd-arc-cluster` | GitOps & CI/CD | ArgoCD, GitHub Runners | Low CPU, Low Storage |
| `k8s-sonar-semgrep-cluster` | Security Scanning | SonarQube, Semgrep | Medium CPU, Medium Storage |

---

## 🎯 **Step-by-Step Implementation**

### **Phase 1: Prepare Application for Observability**

#### **1.1 Update Application Dependencies**

**File:** `C:\PureHeart\build.gradle`

Add these dependencies:
```gradle
dependencies {
    // ... existing dependencies ...
    
    // Spring AOP for logging aspects
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    
    // Observability dependencies
    implementation 'io.micrometer:micrometer-registry-prometheus'
    
    // OpenTelemetry Dependencies with BOM
    implementation platform('io.opentelemetry:opentelemetry-bom:1.35.0')
    implementation 'io.opentelemetry:opentelemetry-api'
    implementation 'io.opentelemetry:opentelemetry-sdk'
    implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
    implementation 'io.opentelemetry:opentelemetry-sdk-extension-autoconfigure'
    
    // Micrometer OpenTelemetry bridge
    implementation 'io.micrometer:micrometer-tracing-bridge-otel'
    
    // Structured logging
    implementation 'net.logstash.logback:logstash-logback-encoder:7.4'
}
```

#### **1.2 Configure Application Properties**

**File:** `C:\PureHeart\src\main\resources\application-dev.properties`

Add observability configuration:
```properties
# === Application Configuration ===
spring.application.name=donation-receipt-service
server.servlet.context-path=/pheart/
server.port=8080

# === Actuator Configuration (Health & Metrics) ===
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true

# === Metrics Configuration ===
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
management.metrics.tags.environment=dev

# === OpenTelemetry Configuration (Traces) ===
otel.traces.exporter=otlp
otel.metrics.exporter=none
otel.logs.exporter=none

# OTLP Exporter Configuration
otel.exporter.otlp.endpoint=http://tempo-agent.monitoring.svc.cluster.local:4317
otel.exporter.otlp.protocol=grpc

# Resource attributes for service identification
otel.resource.attributes=service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=dev

# Sampling configuration (100% sampling for development)
otel.traces.sampler=always_on
otel.traces.sampler.arg=1.0

# === Logging Configuration ===
logging.config=classpath:logback-dev.xml
logging.level.com.chidhagni.donationreceipt.donationhead=INFO
logging.level.com.chidhagni.donationreceipt.donationreceipts=INFO
logging.level.com.chidhagni.donationreceipt.donors=INFO
logging.level.com.chidhagni.donationreceipt.donorgroups=INFO
logging.level.com.chidhagni.donationreceipt.donorsimport=INFO
```

#### **1.3 Update Dockerfile for OpenTelemetry**

**File:** `C:\PureHeart\Dockerfile`

```dockerfile
FROM eclipse-temurin:21-jre

WORKDIR /app

# Download the OpenTelemetry Java agent
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /otel/opentelemetry-javaagent.jar

# Copy your Spring Boot JAR
COPY build/libs/*.jar /app/app.jar

# Create logs directory
RUN mkdir -p /app/logs

# Use the agent in the entrypoint with proper memory settings
ENTRYPOINT ["java", "-javaagent:/otel/opentelemetry-javaagent.jar", "-Xms256m", "-Xmx512m", "-jar", "/app/app.jar"]
```

#### **1.4 Update Kubernetes Deployment**

**File:** `C:\gitops-argocd-apps\deployments\donation-receipt-backend\overlays\dev\deployment.yaml`

Add Prometheus annotations and OpenTelemetry environment variables:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: donation-receipt-backend
spec:
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/pheart/actuator/prometheus"
    spec:
      containers:
      - name: donation-receipt-backend
        env:
        # ... existing environment variables ...
        
        # OpenTelemetry Environment Variables
        - name: OTEL_SERVICE_NAME
          value: "donation-receipt-service"
        - name: OTEL_TRACES_EXPORTER
          value: "otlp"
        - name: OTEL_METRICS_EXPORTER
          value: "none"
        - name: OTEL_LOGS_EXPORTER
          value: "none"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://tempo-agent.monitoring.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_TRACES_SAMPLER
          value: "always_on"
        - name: OTEL_TRACES_SAMPLER_ARG
          value: "1.0"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=dev"
```

### **Phase 2: Deploy Central Monitoring Stack**

#### **2.1 Switch to Monitoring Cluster**
```powershell
kubectl config use-context do-blr1-k8s-monitoring-cluster
```

#### **2.2 Create Prometheus Configuration**

**File:** `C:\Users\<USER>\DigitalOceanSetupRelatedFiles\kube-prom-values.yaml`

```yaml
# Optimized for DigitalOcean free credits
alertmanager:
  enabled: true
  alertmanagerSpec:
    resources:
      limits:
        cpu: 50m
        memory: 50Mi
      requests:
        cpu: 25m
        memory: 25Mi

grafana:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  service:
    type: LoadBalancer
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: 'default'
          orgId: 1
          folder: ''
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards/default
  dashboards:
    default: {}  # Empty to disable default dashboards

# Disable unnecessary components to save resources
kubeStateMetrics:
  enabled: false
nodeExporter:
  enabled: false
prometheusOperator:
  enabled: true
  kubeletService:
    enabled: false
  kubeControllerManager:
    enabled: false
  kubeScheduler:
    enabled: false
  kubeEtcd:
    enabled: false
  kubeProxy:
    enabled: false
  coreDns:
    enabled: false
defaultRules:
  create: false

prometheus:
  service:
    type: LoadBalancer
  prometheusSpec:
    maximumStartupDurationSeconds: 300
    serviceMonitorSelectorNilUsesHelmValues: false
    enableRemoteWriteReceiver: true  # Allow receiving data from agents
    resources:
      limits:
        cpu: 200m
        memory: 200Mi
      requests:
        cpu: 50m
        memory: 100Mi
    retention: 1d
    replicas: 1
    scrapeConfigs: []

promtail:
  enabled: false
```

#### **2.3 Deploy Prometheus Stack**
```powershell
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install kube-prometheus-stack prometheus-community/kube-prometheus-stack -f C:\Users\<USER>\DigitalOceanSetupRelatedFiles\kube-prom-values.yaml -n monitoring --create-namespace
```

#### **2.4 Create Loki Configuration**

**File:** `C:\Users\<USER>\DigitalOceanSetupRelatedFiles\loki-values.yaml`

```yaml
# Single Binary mode for resource efficiency
deploymentMode: SingleBinary

loki:
  commonConfig:
    replication_factor: 1
  storage:
    type: 'filesystem'
    filesystem:
      chunks_directory: /var/loki/chunks
      rules_directory: /var/loki/rules
  schemaConfig:
    configs:
      - from: "2024-01-01"
        store: tsdb
        object_store: filesystem
        schema: v13
        index:
          prefix: loki_index_
          period: 24h

singleBinary:
  replicas: 1
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage

# Disable distributed components to save resources
backend:
  replicas: 0
read:
  replicas: 0
write:
  replicas: 0

gateway:
  enabled: false
monitoring:
  selfMonitoring:
    enabled: false
  lokiCanary:
    enabled: false
test:
  enabled: false

service:
  type: LoadBalancer
  port: 3100
```

#### **2.5 Deploy Loki**
```powershell
helm repo add grafana https://grafana.github.io/helm-charts
helm install loki grafana/loki -f C:\Users\<USER>\DigitalOceanSetupRelatedFiles\loki-values.yaml -n monitoring
```

#### **2.6 Create Tempo Configuration**

**File:** `C:\Users\<USER>\DigitalOceanSetupRelatedFiles\tempo-values.yaml`

```yaml
tempo:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi
  service:
    type: LoadBalancer
    port: 3200
  config: |
    server:
      http_listen_port: 3200
    distributor:
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:4317
            http:
              endpoint: 0.0.0.0:4318
    ingester:
      max_block_bytes: 1_000_000
      max_block_duration: 5m
    compactor:
      compaction:
        block_retention: 24h
    storage:
      trace:
        backend: local
        local:
          path: /var/tempo/traces
```

#### **2.7 Deploy Tempo**
```powershell
helm install tempo grafana/tempo -f C:\Users\<USER>\DigitalOceanSetupRelatedFiles\tempo-values.yaml -n monitoring
```

### **Phase 3: Deploy Agent Stack (Dev Cluster)**

#### **3.1 Switch to Dev Cluster**
```powershell
kubectl config use-context do-blr1-k8s-dev-staging-cluster
```

#### **3.2 Create Prometheus Agent Configuration**

**File:** `C:\gitops-argocd-apps\monitoring\pureheart-plg\prometheus-agent-values.yaml`

This file contains:
- **RBAC permissions** for discovering pods and services
- **ConfigMap** with scraping configuration 
- **Deployment** with resource limits optimized for DigitalOcean
- **Service** for internal communication

Key configuration sections:
```yaml
# RBAC Configuration
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus-agent
rules:
- apiGroups: [""]
  resources: ["nodes", "services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]

# Prometheus Agent Configuration
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      external_labels:
        cluster: "pureheart-dev"
        project: "donation-receipt-backend"
    
    # Send data to central Prometheus
    remote_write:
      - url: "http://************:9090/api/v1/write"
        queue_config:
          max_samples_per_send: 1000
          max_shards: 200
          capacity: 2500
    
    scrape_configs:
      # Scrape donation-receipt-backend
      - job_name: 'donation-receipt-backend'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names: ["donation-receipt-backend-dev"]
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: donation-receipt-backend
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
```

#### **3.3 Create Promtail Configuration**

**File:** `C:\gitops-argocd-apps\monitoring\pureheart-plg\promtail-values.yaml`

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: promtail-config
  namespace: monitoring
data:
  promtail.yml: |
    server:
      log_level: info
      http_listen_port: 3101
      grpc_listen_port: 0

    clients:
      - url: http://*************:3100/loki/api/v1/push
        timeout: 10s

    positions:
      filename: /run/promtail/positions.yaml

    scrape_configs:
      # Collect logs from all applications
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        pipeline_stages:
          - docker: {}
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_container_name]
            target_label: container
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_app]
            target_label: app

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: promtail
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: promtail
  template:
    metadata:
      labels:
        app: promtail
    spec:
      serviceAccountName: promtail
      containers:
      - name: promtail
        image: grafana/promtail:2.6.1
        args:
        - -config.file=/etc/promtail/promtail.yml
        ports:
        - containerPort: 3101
          name: http
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - name: config
          mountPath: /etc/promtail
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: positions
          mountPath: /run/promtail
      volumes:
      - name: config
        configMap:
          name: promtail-config
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: positions
        emptyDir: {}
```

#### **3.4 Create Tempo Agent Configuration**

**File:** `C:\gitops-argocd-apps\monitoring\pureheart-plg\tempo-agent-values.yaml`

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tempo-agent-config
  namespace: monitoring
data:
  tempo.yaml: |
    server:
      http_listen_port: 3200
      grpc_listen_port: 9095

    distributor:
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:4317
            http:
              endpoint: 0.0.0.0:4318

    ingester:
      max_block_bytes: 100_000_000
      max_block_duration: 5m

    storage:
      trace:
        backend: local
        local:
          path: /tmp/tempo/blocks

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tempo-agent
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tempo-agent
  template:
    metadata:
      labels:
        app: tempo-agent
    spec:
      containers:
      - name: tempo
        image: grafana/tempo:2.8.1
        args:
        - -config.file=/etc/tempo/tempo.yaml
        ports:
        - containerPort: 3200
          name: http
        - containerPort: 4317
          name: otlp-grpc
        - containerPort: 4318
          name: otlp-http
        resources:
          requests:
            cpu: 10m
            memory: 32Mi
        volumeMounts:
        - name: config
          mountPath: /etc/tempo
        - name: storage
          mountPath: /tmp/tempo
      volumes:
      - name: config
        configMap:
          name: tempo-agent-config
      - name: storage
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: tempo-agent
  namespace: monitoring
spec:
  selector:
    app: tempo-agent
  ports:
  - name: http
    port: 3200
    targetPort: 3200
  - name: otlp-grpc
    port: 4317
    targetPort: 4317
  - name: otlp-http
    port: 4318
    targetPort: 4318
  type: ClusterIP
```

#### **3.5 Deploy All Agents**
```powershell
# Apply RBAC
kubectl apply -f C:\gitops-argocd-apps\monitoring\pureheart-plg\prometheus-agent-rbac-values.yaml

# Deploy Prometheus Agent
kubectl apply -f C:\gitops-argocd-apps\monitoring\pureheart-plg\prometheus-agent-values.yaml

# Deploy Promtail
kubectl apply -f C:\gitops-argocd-apps\monitoring\pureheart-plg\promtail-values.yaml

# Deploy Tempo Agent
kubectl apply -f C:\gitops-argocd-apps\monitoring\pureheart-plg\tempo-agent-values.yaml
```

### **Phase 4: Deploy Application with Observability**

#### **4.1 Build and Push Application**
```powershell
cd C:\PureHeart
./gradlew clean build -x test
docker build -t registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend:latest .
docker push registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-backend:latest
```

#### **4.2 Deploy via ArgoCD**
The application will automatically deploy through your GitOps pipeline.

---

## 📊 **Configuration Files Explained**

### **File Structure Overview**

```
📁 Project Structure
├── 📁 C:\PureHeart (Application Code)
│   ├── 📄 build.gradle (Dependencies)
│   ├── 📄 Dockerfile (Container Definition)
│   ├── 📁 src/main/resources/
│   │   ├── 📄 application-dev.properties (App Config)
│   │   └── 📄 logback-dev.xml (Logging Config)
│   └── 📁 src/main/java/com/chidhagni/config/
│       ├── 📄 SwaggerConfig.java (API Docs)
│       └── 📄 LoggingAspect.java (AOP Logging)
│
├── 📁 C:\gitops-argocd-apps (Kubernetes Configs)
│   └── 📁 monitoring/pureheart-plg/
│       ├── 📄 prometheus-agent-values.yaml (Metrics Collection)
│       ├── 📄 promtail-values.yaml (Log Collection)
│       ├── 📄 tempo-agent-values.yaml (Trace Collection)
│       └── 📄 prometheus-agent-rbac-values.yaml (Permissions)
│
└── 📁 C:\Users\<USER>\DigitalOceanSetupRelatedFiles (Central Monitoring)
    ├── 📄 kube-prom-values.yaml (Prometheus + Grafana)
    ├── 📄 loki-values.yaml (Log Storage)
    └── 📄 tempo-values.yaml (Trace Storage)
```

### **Key Configuration Concepts**

#### **1. Resource Limits (Why They Matter)**
```yaml
resources:
  limits:
    cpu: 200m      # Maximum CPU (0.2 cores)
    memory: 200Mi  # Maximum Memory (200 MB)
  requests:
    cpu: 50m       # Guaranteed CPU (0.05 cores)
    memory: 100Mi  # Guaranteed Memory (100 MB)
```

**For Beginners:**
- **Requests**: Kubernetes guarantees this amount of resources
- **Limits**: Maximum resources the container can use
- **Why Important**: Prevents one container from consuming all cluster resources

#### **2. Service Discovery (How Components Find Each Other)**
```yaml
# Internal DNS names in Kubernetes
otel.exporter.otlp.endpoint=http://tempo-agent.monitoring.svc.cluster.local:4317
clients:
  - url: http://*************:3100/loki/api/v1/push  # External LoadBalancer IP
```

**For Beginners:**
- **Internal Services**: Use DNS names like `service.namespace.svc.cluster.local`
- **External Services**: Use LoadBalancer IPs for cross-cluster communication
- **Ports**: Each service exposes specific ports (3100=Loki, 9090=Prometheus, 4317=Tempo)

#### **3. Data Flow Configuration**
```yaml
# Prometheus Agent sends metrics to Central Prometheus
remote_write:
  - url: "http://************:9090/api/v1/write"

# Promtail sends logs to Central Loki  
clients:
  - url: http://*************:3100/loki/api/v1/push

# Application sends traces to Tempo Agent
otel.exporter.otlp.endpoint=http://tempo-agent.monitoring.svc.cluster.local:4317
```

---

## ✅ **Verification and Testing**

### **Step 1: Check All Pods Are Running**

#### **Monitoring Cluster**
```powershell
kubectl config use-context do-blr1-k8s-monitoring-cluster
kubectl get pods -n monitoring

# Expected output:
# prometheus-kube-prometheus-stack-prometheus-0       2/2     Running
# kube-prometheus-stack-grafana-xxx                   3/3     Running  
# loki-0                                              1/1     Running
# tempo-0                                             1/1     Running
```

#### **Dev Cluster**
```powershell
kubectl config use-context do-blr1-k8s-dev-staging-cluster
kubectl get pods -n monitoring

# Expected output:
# prometheus-agent-xxx                                1/1     Running
# promtail-xxx                                        1/1     Running
# tempo-agent-xxx                                     1/1     Running
```

### **Step 2: Test Application Endpoints**

#### **Health Check**
```powershell
Invoke-WebRequest -Uri "http://**************:8080/pheart/actuator/health" -Method GET
# Should return: HTTP 200 with health status
```

#### **Metrics Endpoint**
```powershell
Invoke-WebRequest -Uri "http://**************:8080/pheart/actuator/prometheus" -Method GET
# Should return: HTTP 200 with Prometheus metrics (many lines of metrics data)
```

#### **Swagger API Documentation**
```
http://**************:8080/pheart/swagger-ui/index.html
# Should load Swagger UI without "Failed to load remote configuration" error
```

### **Step 3: Verify Data Collection**

#### **Prometheus Targets**
```powershell
Invoke-WebRequest -Uri "http://************:9090/api/v1/targets" -Method GET
# Should return: JSON with active targets including donation-receipt-backend
```

#### **Grafana Access**
```
http://************:80
# Login: admin/admin (change on first login)
```

### **Step 4: Create Test Dashboard**

1. **Access Grafana**: `http://************:80`
2. **Add Data Sources**:
   - **Prometheus**: `http://prometheus-kube-prometheus-stack-prometheus.monitoring.svc.cluster.local:9090`
   - **Loki**: `http://loki.monitoring.svc.cluster.local:3100/`
   - **Tempo**: `http://tempo.monitoring.svc.cluster.local:3200`

3. **Test Queries**:
   - **Metrics**: `up{app="donation-receipt-backend"}`
   - **Logs**: `{app="donation-receipt-backend"}`
   - **HTTP Requests**: `http_server_requests_seconds_count{job="donation-receipt-backend"}`

---

## 🛠️ **Troubleshooting Guide**

### **Common Issues and Solutions**

#### **1. Pods in Pending State**
**Problem**: `kubectl get pods` shows pods stuck in "Pending"
**Cause**: Insufficient resources in cluster
**Solution**:
```powershell
# Check resource usage
kubectl describe pod <pod-name> -n monitoring

# Look for events like "Insufficient cpu" or "Insufficient memory"
# Reduce resource requests in values files
```

#### **2. Application Metrics Not Appearing**
**Problem**: Prometheus shows no data for application
**Debugging Steps**:
```powershell
# Check if application metrics endpoint works
curl http://**************:8080/pheart/actuator/prometheus

# Check if Prometheus agent is scraping
kubectl logs prometheus-agent-xxx -n monitoring

# Check Prometheus targets
curl "http://************:9090/api/v1/targets"
```

#### **3. Logs Not Appearing in Loki**
**Problem**: No logs visible in Grafana
**Debugging Steps**:
```powershell
# Check Promtail logs
kubectl logs promtail-xxx -n monitoring

# Check if Promtail can access log files
kubectl exec promtail-xxx -n monitoring -- ls -la /var/log/pods/

# Verify Loki is receiving data
curl "http://*************:3100/loki/api/v1/label"
```

#### **4. Traces Not Appearing**
**Problem**: No traces in Grafana
**Debugging Steps**:
```powershell
# Check Tempo agent logs
kubectl logs tempo-agent-xxx -n monitoring

# Verify application is sending traces
# Look for OpenTelemetry logs in application logs
kubectl logs donation-receipt-backend-xxx -n donation-receipt-backend-dev

# Test trace endpoint
curl -X POST "http://**************:8080/pheart/observability/test-donation-receipt?donorId=test&amount=100"
```

#### **5. High Resource Usage**
**Problem**: DigitalOcean costs increasing
**Solution**:
```yaml
# Reduce resource limits in all values files
resources:
  limits:
    cpu: 50m      # Reduce from 200m
    memory: 128Mi # Reduce from 200Mi
  requests:
    cpu: 25m      # Reduce from 100m
    memory: 64Mi  # Reduce from 128Mi
```

### **Resource Optimization Tips**

1. **Monitor Usage**:
   ```powershell
   kubectl top pods -n monitoring
   kubectl top nodes
   ```

2. **Adjust Retention Periods**:
   ```yaml
   # In kube-prom-values.yaml
   retention: 1d  # Keep only 1 day of metrics
   
   # In loki-values.yaml
   limits_config:
     retention_period: 24h  # Keep only 24 hours of logs
   ```

3. **Use Single Binary Mode**: Already configured for Loki and Tempo

---

## 🏆 **Best Practices**

### **Security Best Practices**

1. **RBAC (Role-Based Access Control)**:
   - Always use dedicated service accounts
   - Grant minimal required permissions
   - Regularly review and rotate credentials

2. **Network Security**:
   - Use internal DNS names within clusters
   - Expose only necessary services via LoadBalancer
   - Consider using Ingress for external access

3. **Data Security**:
   - Configure log retention policies
   - Mask sensitive data in logs
   - Use HTTPS for external communications

### **Performance Best Practices**

1. **Resource Management**:
   - Always set resource requests and limits
   - Monitor actual usage and adjust accordingly
   - Use horizontal pod autoscaling for variable loads

2. **Data Collection Efficiency**:
   - Use appropriate scrape intervals (30s is usually sufficient)
   - Implement metric filtering to reduce data volume
   - Use sampling for high-volume trace data

3. **Storage Optimization**:
   - Set appropriate retention periods
   - Use compressed storage when available
   - Regularly clean up old data

### **Operational Best Practices**

1. **Monitoring the Monitoring**:
   - Set up alerts for PLG stack health
   - Monitor resource usage of monitoring components
   - Have backup and recovery procedures

2. **Documentation**:
   - Document all custom configurations
   - Maintain runbooks for common issues
   - Keep this documentation updated

3. **Testing**:
   - Regularly test all monitoring components
   - Verify alert functionality
   - Practice disaster recovery procedures

---

## 📈 **Scaling and Maintenance**

### **Scaling Strategy**

#### **When to Scale Up**
- **Metrics**: High CPU/memory usage consistently
- **Queries**: Slow dashboard loading
- **Data Loss**: Missing metrics or logs

#### **How to Scale**

1. **Vertical Scaling** (Increase resources):
   ```yaml
   resources:
     limits:
       cpu: 500m      # Increase from 200m
       memory: 1Gi    # Increase from 200Mi
   ```

2. **Horizontal Scaling** (More replicas):
   ```yaml
   spec:
     replicas: 2      # Increase from 1
   ```

### **Maintenance Tasks**

#### **Weekly Tasks**
- Review resource usage and costs
- Check for failed pods or services
- Verify data collection is working

#### **Monthly Tasks**
- Update Helm charts to latest versions
- Review and adjust retention policies
- Clean up old persistent volumes

#### **Quarterly Tasks**
- Review and update security configurations
- Performance testing and optimization
- Disaster recovery testing

### **Upgrade Procedures**

1. **Preparation**:
   - Backup all configurations
   - Test upgrades in development environment
   - Plan maintenance window

2. **Execution**:
   ```powershell
   # Update Helm repositories
   helm repo update
   
   # Upgrade components one by one
   helm upgrade kube-prometheus-stack prometheus-community/kube-prometheus-stack -f kube-prom-values.yaml -n monitoring
   helm upgrade loki grafana/loki -f loki-values.yaml -n monitoring
   helm upgrade tempo grafana/tempo -f tempo-values.yaml -n monitoring
   ```

3. **Verification**:
   - Check all pods are running
   - Verify data collection continues
   - Test dashboard functionality

---

## 🎯 **Success Metrics**

### **Technical Success Indicators**

- ✅ All PLG components running with < 1% downtime
- ✅ Application metrics collected with < 30-second delay  
- ✅ Logs searchable within 1 minute of generation
- ✅ Traces visible within 30 seconds of generation
- ✅ Dashboard queries return in < 5 seconds
- ✅ Resource usage stays within DigitalOcean budget

### **Business Success Indicators**

- 📊 **Reduced Mean Time to Resolution (MTTR)**: From hours to minutes
- 🔍 **Proactive Issue Detection**: Catch issues before users report them
- 💰 **Cost Visibility**: Track resource usage and optimize costs
- 📈 **Business Insights**: Monitor donation receipts, user activity, performance trends

### **Operational Success Indicators**

- 🚀 **Faster Debugging**: Developers can quickly identify issues
- 🔒 **Better Security**: Monitor authentication and authorization events
- 📋 **Compliance**: Maintain audit logs for regulatory requirements
- 🎯 **Performance Optimization**: Data-driven decisions for improvements

---

## 🎉 **Congratulations!**

You now have a **complete, production-ready PLG observability stack** running on DigitalOcean Kubernetes!

### **What You've Accomplished**

1. ✅ **Built a scalable multi-cluster architecture**
2. ✅ **Implemented comprehensive monitoring for your donation receipt application**
3. ✅ **Optimized for cost efficiency on DigitalOcean free credits**
4. ✅ **Created a foundation for future applications**
5. ✅ **Gained deep understanding of modern observability practices**

### **Next Steps**

1. **Create Custom Dashboards**: Build specific dashboards for your business metrics
2. **Set Up Alerts**: Configure Prometheus alerts for critical conditions
3. **Expand Coverage**: Add monitoring to other applications
4. **Optimize Further**: Fine-tune based on actual usage patterns
5. **Share Knowledge**: Use this setup as a template for other projects

### **Getting Help**

- **Documentation**: Refer back to this guide for troubleshooting
- **Community**: Join Prometheus, Grafana, and Kubernetes communities
- **DigitalOcean**: Use DigitalOcean documentation for cluster-specific issues

**Remember**: Observability is a journey, not a destination. Keep iterating and improving based on your needs!

---

> **💡 Pro Tip**: Bookmark this documentation and keep it updated as you make changes to your setup. Future you will thank present you for the detailed documentation!
