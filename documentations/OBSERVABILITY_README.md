# Observability Setup for Donation Receipt Service

This document describes the observability setup for the Donation Receipt Service, including metrics, tracing, and logging configurations.

## Overview

The application is configured to send:
- **Metrics** to Prometheus via Micrometer
- **Traces** to Tempo via OpenTelemetry Java Agent
- **Logs** to Loki via structured logging

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │ Prometheus      │    │ Grafana         │
│   Cluster       │───▶│ Agent           │───▶│ Dashboard       │
│                 │    │                 │    │                 │
│ - Spring Boot   │    │ - Metrics       │    │ - Visualization │
│ - Java Agent    │    │ - Traces        │    │ - Alerts        │
│ - Structured    │    │ - Logs          │    │                 │
│   Logging       │    └─────────────────┘    └─────────────────┘
└─────────────────┘              │
                                 │
                                 ▼
                        ┌─────────────────┐
                        │ Observability   │
                        │ Stack Cluster   │
                        │                 │
                        │ - Prometheus    │
                        │ - <PERSON><PERSON>       │
                        │ - Loki          │
                        │ - Tempo         │
                        └─────────────────┘
```

## Configuration Files

### 1. Application Properties

#### Development (`application-dev.properties`)
```properties
# Observability Configuration
spring.application.name=donation-receipt-service

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
management.metrics.tags.environment=dev

# Tracing Configuration
management.tracing.sampling.probability=1.0
management.zipkin.tracing.endpoint=http://prometheus-agent:9411/api/v2/spans
management.otlp.traces.endpoint=http://prometheus-agent:4317
management.otlp.metrics.endpoint=http://prometheus-agent:4317
```

#### Production (`application-prod.properties`)
```properties
# Observability Configuration
spring.application.name=donation-receipt-service

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
management.metrics.tags.environment=prod

# Tracing Configuration
management.tracing.sampling.probability=0.1
management.zipkin.tracing.endpoint=http://prometheus-agent:9411/api/v2/spans
management.otlp.traces.endpoint=http://prometheus-agent:4317
management.otlp.metrics.endpoint=http://prometheus-agent:4317
```

### 2. Logging Configuration (`logback-spring.xml`)

The application uses structured logging with JSON format in production:

```xml
<springProfile name="prod">
    <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp/>
                <logLevel/>
                <loggerName/>
                <message/>
                <mdc/>
                <stackTrace/>
            </providers>
        </encoder>
    </appender>
</springProfile>
```

### 3. Kubernetes Deployment

The application is deployed with the OpenTelemetry Java Agent:

```yaml
containers:
- name: donation-receipt-service
  image: donation-receipt-service:latest
  env:
  - name: OTEL_SERVICE_NAME
    value: "donation-receipt-service"
  - name: OTEL_TRACES_EXPORTER
    value: "otlp"
  - name: OTEL_METRICS_EXPORTER
    value: "otlp"
  - name: OTEL_LOGS_EXPORTER
    value: "otlp"
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: "http://prometheus-agent:4317"
```

## Available Metrics

### Custom Business Metrics

1. **Donation Receipt Metrics**
   - `donation_receipt_created_total` - Total number of donation receipts created
   - `donation_receipt_processing_duration` - Time taken to process donation receipt

2. **Payment Metrics**
   - `payment_success_total` - Total number of successful payments
   - `payment_failure_total` - Total number of failed payments
   - `payment_processing_duration` - Time taken to process payment

3. **Communication Metrics**
   - `email_sent_total` - Total number of emails sent
   - `sms_sent_total` - Total number of SMS sent

4. **API Metrics**
   - `api_requests_total` - Total number of API requests
   - `api_errors_total` - Total number of API errors
   - `http_server_duration` - HTTP request duration with method, URI, and status tags

### System Metrics

- JVM metrics (memory, GC, threads)
- Process metrics (CPU, memory usage)
- System metrics (disk, network)

## Using Metrics in Code

### 1. Injecting Metrics

```java
@Autowired
private Counter donationReceiptCounter;

@Autowired
private Timer donationReceiptProcessingTimer;

@Autowired
private MeterRegistry meterRegistry;
```

### 2. Recording Metrics

```java
// Increment counter
donationReceiptCounter.increment();

// Record timing
Timer.Sample sample = Timer.start(meterRegistry);
try {
    // Your business logic
} finally {
    sample.stop(donationReceiptProcessingTimer);
}

// Record custom gauge
meterRegistry.gauge("custom_metric_name", value);
```

### 3. Using @Timed Annotation

```java
@Timed(value = "donation.receipt.create", description = "Time taken to create donation receipt")
public void createDonationReceipt(String donorId, double amount) {
    // Your business logic
}
```

### 4. Using @Observed Annotation

```java
@Observed(name = "observability.service", contextualName = "observability-operations")
public class ObservabilityService {
    // Your service methods
}
```

## Prometheus Queries

### Useful PromQL Queries

1. **Request Rate**
   ```promql
   rate(http_server_duration_count[5m])
   ```

2. **Error Rate**
   ```promql
   rate(api_errors_total[5m])
   ```

3. **Response Time Percentiles**
   ```promql
   histogram_quantile(0.95, rate(http_server_duration_bucket[5m]))
   ```

4. **Donation Receipt Creation Rate**
   ```promql
   rate(donation_receipt_created_total[5m])
   ```

5. **Payment Success Rate**
   ```promql
   rate(payment_success_total[5m]) / (rate(payment_success_total[5m]) + rate(payment_failure_total[5m]))
   ```

## Grafana Dashboards

### Recommended Dashboards

1. **Application Overview Dashboard**
   - Request rate, error rate, response time
   - Business metrics (donations, payments)
   - System metrics (CPU, memory, JVM)

2. **Business Metrics Dashboard**
   - Donation receipt creation trends
   - Payment success/failure rates
   - Email/SMS sending statistics

3. **Infrastructure Dashboard**
   - Pod health and resource usage
   - Database connection metrics
   - External service dependencies

## Alerts

### Recommended Alert Rules

1. **High Error Rate**
   ```yaml
   - alert: HighErrorRate
     expr: rate(api_errors_total[5m]) > 0.1
     for: 2m
     labels:
       severity: warning
     annotations:
       summary: "High error rate detected"
   ```

2. **High Response Time**
   ```yaml
   - alert: HighResponseTime
     expr: histogram_quantile(0.95, rate(http_server_duration_bucket[5m])) > 2
     for: 2m
     labels:
       severity: warning
     annotations:
       summary: "High response time detected"
   ```

3. **Payment Failure Rate**
   ```yaml
   - alert: HighPaymentFailureRate
     expr: rate(payment_failure_total[5m]) / (rate(payment_success_total[5m]) + rate(payment_failure_total[5m])) > 0.05
     for: 5m
     labels:
       severity: critical
     annotations:
       summary: "High payment failure rate detected"
   ```

## Deployment Steps

### 1. Build the Application

```bash
./gradlew clean build -Pprofile=prod
```

### 2. Build Docker Image

```bash
docker build -f Dockerfile.observability -t donation-receipt-service:latest .
```

### 3. Deploy to Kubernetes

```bash
kubectl apply -f k8s/donation-receipt-deployment.yaml
kubectl apply -f k8s/prometheus-servicemonitor.yaml
```

### 4. Verify Deployment

```bash
# Check pods
kubectl get pods -l app=donation-receipt-service

# Check metrics endpoint
kubectl port-forward svc/donation-receipt-service 8092:8092
curl http://localhost:8092/pheart/actuator/prometheus
```

## Troubleshooting

### Common Issues

1. **Metrics not appearing in Prometheus**
   - Check ServiceMonitor configuration
   - Verify Prometheus can reach the application
   - Check application logs for errors

2. **Traces not appearing in Tempo**
   - Verify OpenTelemetry Java Agent is loaded
   - Check OTLP endpoint configuration
   - Verify sampling probability settings

3. **Logs not appearing in Loki**
   - Check logback configuration
   - Verify Loki can reach the application
   - Check log format compatibility

### Debug Commands

```bash
# Check application health
curl http://localhost:8092/pheart/actuator/health

# Check metrics endpoint
curl http://localhost:8092/pheart/actuator/prometheus

# Check application info
curl http://localhost:8092/pheart/actuator/info

# View application logs
kubectl logs -f deployment/donation-receipt-service
```

## Performance Considerations

1. **Sampling**: Use appropriate sampling rates (1.0 for dev, 0.1 for prod)
2. **Metrics Cardinality**: Avoid high cardinality labels
3. **Log Volume**: Use appropriate log levels
4. **Resource Usage**: Monitor agent resource consumption

## Security Considerations

1. **Endpoint Security**: Secure actuator endpoints in production
2. **Authentication**: Use proper authentication for observability endpoints
3. **Network Security**: Use network policies to restrict access
4. **Data Privacy**: Ensure sensitive data is not logged or traced 