# Complete PLG (Prometheus, <PERSON>, <PERSON>ana) Setup Guide

## 📋 Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Prerequisites](#prerequisites)
3. [Step-by-Step Implementation](#step-by-step-implementation)
4. [Application Configuration](#application-configuration)
5. [Monitoring Stack Deployment](#monitoring-stack-deployment)
6. [Agent Configuration](#agent-configuration)
7. [Troubleshooting](#troubleshooting)
8. [Implementation for New Projects](#implementation-for-new-projects)

## 🏗️ Architecture Overview

### Multi-Cluster Setup
```
┌─────────────────────────────────────────────────────────────────┐
│                    Kubernetes Clusters                         │
├─────────────────────────────────────────────────────────────────┤
│  Monitoring Cluster (Central)    │  Dev/Staging Cluster       │
│  ┌─────────────────────────────┐ │  ┌─────────────────────────┐ │
│  │ • Prometheus (Central)      │ │  │ • Prometheus Agent      │ │
│  │ • <PERSON>ana                   │ │  │ • Promtail              │ │
│  │ • Loki                      │ │  │ • Tempo Agent           │ │
│  │ • Tempo                     │ │  │ • Application Pods      │ │
│  └─────────────────────────────┘ │  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Data Flow
1. **Application** → **Tempo Agent** → **Central Tempo** (Traces)
2. **Application** → **Prometheus Agent** → **Central Prometheus** (Metrics)
3. **Application** → **Promtail** → **Central Loki** (Logs)
4. **Central Monitoring** → **Grafana** (Visualization)

## 📋 Prerequisites

### Required Tools
- `kubectl` configured for multiple clusters
- `helm` (v3.x)
- `doctl` (DigitalOcean CLI)
- Docker for building images

### Cluster Setup
```bash
# List your clusters
doctl kubernetes cluster list

# Expected clusters:
# - k8s-dev-staging-cluster (application + agents)
# - k8s-monitoring-cluster (central monitoring)
# - k8s-argocd-arc-cluster (GitOps)
# - k8s-sonar-semgrep-cluster (security tools)
```

## 🚀 Step-by-Step Implementation

### Phase 1: Application Configuration

#### 1.1 Enable OpenTelemetry Dependencies
**File:** `build.gradle`
```gradle
dependencies {
    // OpenTelemetry BOM
    implementation platform('io.opentelemetry:opentelemetry-bom:1.32.0')
    
    // Core OpenTelemetry
    implementation 'io.opentelemetry:opentelemetry-api'
    implementation 'io.opentelemetry:opentelemetry-sdk'
    implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
    
    // Micrometer Integration
    implementation 'io.micrometer:micrometer-tracing-bridge-otel'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    
    // Spring Boot Actuator
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
}
```

#### 1.2 Configure Application Properties
**File:** `src/main/resources/application-dev.properties`
```properties
# === OpenTelemetry Configuration ===
otel.traces.exporter=otlp
otel.metrics.exporter=none
otel.logs.exporter=none
otel.exporter.otlp.endpoint=http://tempo-agent.monitoring.svc.cluster.local:4317
otel.exporter.otlp.protocol=grpc
otel.resource.attributes=service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=dev
otel.traces.sampler=always_on
otel.traces.sampler.arg=1.0
otel.bsp.schedule.delay=1000
otel.bsp.max.queue.size=2048
otel.bsp.max.export.batch.size=512
otel.bsp.export.timeout=30000

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true

# Metrics Configuration
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
management.metrics.tags.environment=dev
```

#### 1.3 Update Dockerfile with Java Agent
**File:** `Dockerfile`
```dockerfile
FROM eclipse-temurin:21-jre

WORKDIR /app

# Download the OpenTelemetry Java agent
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /otel/opentelemetry-javaagent.jar

# Copy your Spring Boot JAR
COPY build/libs/*.jar /app/app.jar

# Use the agent in the entrypoint
ENTRYPOINT ["java", "-javaagent:/otel/opentelemetry-javaagent.jar", "-jar", "/app/app.jar"]
```

#### 1.4 Enable Configuration Classes
**File:** `src/main/java/com/chidhagni/utils/TracingUtil.java`
```java
@Component // Enable this annotation
@Slf4j
public class TracingUtil {
    // ... existing methods
}
```

**File:** `src/main/java/com/chidhagni/config/MetricsConfig.java`
```java
@Configuration // Enable this annotation
public class MetricsConfig {
    // ... existing bean definitions
}
```

#### 1.5 Create Observability Service
**File:** `src/main/java/com/chidhagni/donationreceipt/services/ObservabilityService.java`
```java
@Service
@Slf4j
public class ObservabilityService {
    @Autowired
    private TracingUtil tracingUtil;
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    @Autowired
    private Counter donationReceiptCounter;
    
    @Autowired
    private Timer donationReceiptProcessingTimer;

    public String processDonationReceipt(String donorId, double amount) {
        return tracingUtil.traceMethod("processDonationReceipt", () -> {
            tracingUtil.setAttribute("donor.id", donorId);
            tracingUtil.setAttribute("donation.amount", String.valueOf(amount));
            tracingUtil.addEvent("donation_receipt.processing.started");
            
            Timer.Sample sample = Timer.start(meterRegistry);
            try {
                Thread.sleep(100);
                donationReceiptCounter.increment();
                tracingUtil.addEvent("donation_receipt.processing.completed");
                return "Receipt processed successfully for donor: " + donorId;
            } catch (InterruptedException e) {
                tracingUtil.addEvent("donation_receipt.processing.error", "error.message", e.getMessage());
                throw new RuntimeException("Processing interrupted", e);
            } finally {
                sample.stop(donationReceiptProcessingTimer);
            }
        });
    }
}
```

#### 1.6 Create Test Controller
**File:** `src/main/java/com/chidhagni/donationreceipt/controller/ObservabilityTestController.java`
```java
@RestController
@RequestMapping("/pheart/observability")
@Slf4j
public class ObservabilityTestController {
    @Autowired
    private ObservabilityService observabilityService;

    @PostMapping("/test-donation-receipt")
    public ResponseEntity<Map<String, Object>> testDonationReceipt(
            @RequestParam String donorId, @RequestParam double amount) {
        String result = observabilityService.processDonationReceipt(donorId, amount);
        return ResponseEntity.ok(Map.of("result", result, "timestamp", System.currentTimeMillis()));
    }
}
```

### Phase 2: Kubernetes Deployment Configuration

#### 2.1 Update Deployment with Prometheus Annotations
**File:** `deployment.yaml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: donation-receipt-backend-dev
spec:
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/pheart/actuator/prometheus"
    spec:
      containers:
      - name: donation-receipt-backend
        env:
        # ... existing env vars
        - name: OTEL_SERVICE_NAME
          value: "donation-receipt-service"
        - name: OTEL_TRACES_EXPORTER
          value: "otlp"
        - name: OTEL_METRICS_EXPORTER
          value: "none"
        - name: OTEL_LOGS_EXPORTER
          value: "none"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://tempo-agent.monitoring.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_TRACES_SAMPLER
          value: "always_on"
        - name: OTEL_TRACES_SAMPLER_ARG
          value: "1.0"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=donation-receipt-service,service.version=1.0.0,deployment.environment=dev"
```

### Phase 3: Central Monitoring Stack

#### 3.1 Deploy Central Monitoring (Monitoring Cluster)
**Switch to monitoring cluster:**
```bash
kubectl config use-context do-blr1-k8s-monitoring-cluster
```

**File:** `kube-prom-values.yaml`
```yaml
alertmanager:
  enabled: true
  alertmanagerSpec:
    resources:
      limits:
        cpu: 50m
        memory: 50Mi
      requests:
        cpu: 25m
        memory: 25Mi

grafana:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  service:
    type: LoadBalancer
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: 'default'
          orgId: 1
          folder: ''
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards/default
  dashboards:
    default: {} # Empty to disable default dashboards

kubeStateMetrics:
  enabled: false
nodeExporter:
  enabled: false
prometheusOperator:
  enabled: true
kubeletService:
  enabled: false
kubeControllerManager:
  enabled: false
kubeScheduler:
  enabled: false
kubeEtcd:
  enabled: false
kubeProxy:
  enabled: false
coreDns:
  enabled: false
defaultRules:
  create: false

prometheus:
  service:
    type: LoadBalancer
  prometheusSpec:
    maximumStartupDurationSeconds: 300
    serviceMonitorSelectorNilUsesHelmValues: false
    resources:
      limits:
        cpu: 200m
        memory: 200Mi
      requests:
        cpu: 50m
        memory: 100Mi
    retention: 1d
    replicas: 1
    scrapeConfigs: []
promtail:
  enabled: false
```

**Deploy Prometheus Stack:**
```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install kube-prometheus-stack prometheus-community/kube-prometheus-stack -f kube-prom-values.yaml -n monitoring --create-namespace
```

**File:** `loki-values.yaml`
```yaml
loki:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  replication_factor: 1
  memberlist:
    enabled: false
  service:
    type: LoadBalancer
    port: 3100
  labels:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: loki
promtail:
  enabled: false
```

**Deploy Loki:**
```bash
helm repo add grafana https://grafana.github.io/helm-charts
helm install loki grafana/loki -f loki-values.yaml -n monitoring
```

**File:** `tempo-values.yaml`
```yaml
tempo:
  enabled: true
  persistence:
    enabled: true
    size: 2Gi
    storageClassName: do-block-storage
  resources:
    limits:
      cpu: 200m
      memory: 200Mi
    requests:
      cpu: 50m
      memory: 100Mi
  service:
    type: LoadBalancer
    port: 3200
  labels:
    app.kubernetes.io/name: tempo
    app.kubernetes.io/instance: tempo
```

**Deploy Tempo:**
```bash
helm install tempo grafana/tempo -f tempo-values.yaml -n monitoring
```

### Phase 4: Agent Configuration (Dev/Staging Cluster)

#### 4.1 Switch to Dev/Staging Cluster
```bash
kubectl config use-context do-blr1-k8s-dev-staging-cluster
```

#### 4.2 Create RBAC for Prometheus Agent
**File:** `prometheus-agent-rbac.yaml`
```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus-agent
  namespace: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus-agent
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus-agent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-agent
subjects:
- kind: ServiceAccount
  name: prometheus-agent
  namespace: monitoring
```

#### 4.3 Deploy Prometheus Agent
**File:** `prometheus-agent-deployment.yaml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus-agent
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus-agent
  template:
    metadata:
      labels:
        app: prometheus-agent
    spec:
      serviceAccountName: prometheus-agent
      containers:
      - name: prometheus
        image: quay.io/prometheus/prometheus:v3.5.0
        args:
        - --config.file=/etc/prometheus/prometheus.yml
        - --web.console.libraries=/etc/prometheus/console_libraries
        - --web.console.templates=/etc/prometheus/consoles
        - --web.enable-lifecycle
        - --web.enable-admin-api
        - --agent
        ports:
        - containerPort: 9090
        resources:
          limits:
            cpu: 100m
            memory: 100Mi
          requests:
            cpu: 50m
            memory: 50Mi
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
      volumes:
      - name: config
        configMap:
          name: prometheus-agent-config
```

**File:** `prometheus-agent-config.yaml`
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-agent-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 30s
      evaluation_interval: 30s
    remote_write:
      - url: "http://************:9090/api/v1/write" # Central Prometheus LB IP
    scrape_configs:
      - job_name: 'donation-receipt-backend'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: donation-receipt-backend
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_app]
            target_label: app
```

**Deploy Prometheus Agent:**
```bash
kubectl apply -f prometheus-agent-rbac.yaml
kubectl apply -f prometheus-agent-deployment.yaml
kubectl apply -f prometheus-agent-config.yaml
```

#### 4.4 Deploy Promtail
**File:** `promtail-values.yaml`
```yaml
promtail:
  enabled: true
  config:
    server:
      log_level: info
      http_listen_port: 3101
      grpc_listen_port: 0
    clients:
      - url: http://*************:3100/loki/api/v1/push # Central Loki LB IP
    positions:
      filename: /run/promtail/positions.yaml
    scrape_configs:
      - job_name: app-logs
        kubernetes_sd_configs:
          - role: pod
        pipeline_stages:
          - docker: {}
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: "donation-receipt-backend"
          - source_labels: [__meta_kubernetes_pod_container_name]
            target_label: container
          - source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_app]
            target_label: app
```

**Deploy Promtail:**
```bash
helm install promtail grafana/promtail -f promtail-values.yaml -n monitoring
```

#### 4.5 Deploy Tempo Agent
**File:** `tempo-agent-values.yaml`
```yaml
tempo:
  enabled: true
  mode: agent
  resources:
    limits:
      cpu: 100m
      memory: 100Mi
    requests:
      cpu: 50m
      memory: 50Mi
  agent:
    enabled: true
    config:
      receivers:
        otlp:
          protocols:
            grpc:
              endpoint: 0.0.0.0:4317
            http:
              endpoint: 0.0.0.0:4318
      remote_write:
        - endpoint: http://**************:3200 # Central Tempo LB IP
  service:
    enabled: true
    type: ClusterIP
    ports:
      - name: grpc
        port: 4317
        targetPort: 4317
```

**Deploy Tempo Agent:**
```bash
helm install tempo-agent grafana/tempo -f tempo-agent-values.yaml -n monitoring
```

### Phase 5: Grafana Configuration

#### 5.1 Get LoadBalancer IPs
```bash
# Switch to monitoring cluster
kubectl config use-context do-blr1-k8s-monitoring-cluster

# Get service IPs
kubectl get svc -n monitoring
```

#### 5.2 Configure Grafana Data Sources
Access Grafana UI and add these data sources:

**Prometheus:**
- URL: `http://************:9090` (Central Prometheus LB IP)

**Loki:**
- URL: `http://*************:3100` (Central Loki LB IP)

**Tempo:**
- URL: `http://**************:3200` (Central Tempo LB IP)

#### 5.3 Delete Default Dashboards
```bash
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-alertmanager-overview
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-apiserver
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-cluster-total
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-controller-manager
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-grafana-overview
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-kubelet
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-namespace-by-pod
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-namespace-by-workload
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-node-cluster-rsrc-use
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-node-rsrc-use
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-persistentvolumesusage
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-pod-total
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-prometheus-overview
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-proxy
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-scheduler
kubectl delete configmap -n monitoring kube-prometheus-stack-grafana-dashboard-workload-total
```

### Phase 6: Application Deployment

#### 6.1 Deploy Application via ArgoCD
```bash
# Switch to ArgoCD cluster
kubectl config use-context do-blr1-k8s-argocd-arc-cluster

# Trigger sync
kubectl patch application donation-receipt-backend-dev -n argocd --type=merge -p='{"spec":{"syncPolicy":{"automated":{"prune":true,"selfHeal":true}}}}'
```

## 🔍 Verification Commands

### Check Application Status
```bash
# Switch to dev/staging cluster
kubectl config use-context do-blr1-k8s-dev-staging-cluster

# Check application logs
kubectl logs donation-receipt-backend-dev-<pod-id> -n donation-receipt-backend-dev

# Test application endpoint
curl -X POST "http://your-app-url/pheart/observability/test-donation-receipt?donorId=test123&amount=100.0"
```

### Check Prometheus Targets
```bash
# Check if application appears in targets
curl "http://************:9090/api/v1/targets" | jq '.data.activeTargets[] | select(.labels.job == "donation-receipt-backend")'
```

### Check Prometheus Agent
```bash
# Check prometheus-agent logs
kubectl logs prometheus-agent-<pod-id> -n monitoring

# Check prometheus-agent config
kubectl get configmap prometheus-agent-config -n monitoring -o yaml
```

### Test Grafana Queries

**Prometheus:**
```
up{job="donation-receipt-backend"}
```

**Loki:**
```
{app="donation-receipt-backend"}
```

**Tempo:**
```
{service.name="donation-receipt-service"}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. RBAC Permission Errors
**Error:** `forbidden: User "system:serviceaccount:monitoring:default" cannot list resource "pods"`
**Solution:** Apply the RBAC configuration provided above.

#### 2. Application Not Scraped
**Error:** Application doesn't appear in Prometheus targets
**Solution:** 
- Verify Prometheus annotations are present
- Check prometheus-agent configuration
- Ensure RBAC permissions are correct

#### 3. OTLP Export Errors
**Error:** `Failed to export logs/metrics. Server responded with UNIMPLEMENTED`
**Solution:** Set `OTEL_METRICS_EXPORTER=none` and `OTEL_LOGS_EXPORTER=none` in application environment variables.

#### 4. Cross-Cluster Communication Issues
**Error:** `UnknownHostException` for service names
**Solution:** Use LoadBalancer IPs instead of internal service names for cross-cluster communication.

#### 5. Default Dashboards Still Appearing
**Solution:** Delete existing configmaps and update Helm values to prevent future creation.

## 📊 Monitoring URLs

### Access URLs
- **Grafana:** `http://<grafana-lb-ip>:3000`
- **Prometheus:** `http://************:9090`
- **Loki:** `http://*************:3100`
- **Tempo:** `http://**************:3200`

### Default Credentials
- **Grafana:** admin/admin (change on first login)

## 🔄 Implementation for New Projects

### Quick Setup Checklist

#### 1. Application Changes
- [ ] Add OpenTelemetry dependencies to `build.gradle`
- [ ] Configure `application-dev.properties` with OTel settings
- [ ] Update `Dockerfile` with Java Agent
- [ ] Enable `@Component` and `@Configuration` annotations
- [ ] Add Prometheus annotations to deployment
- [ ] Add OTel environment variables to deployment

#### 2. Monitoring Stack
- [ ] Deploy central monitoring (Prometheus, Grafana, Loki, Tempo)
- [ ] Deploy agents (Prometheus Agent, Promtail, Tempo Agent)
- [ ] Configure RBAC for agents
- [ ] Set up Grafana data sources
- [ ] Remove default dashboards

#### 3. Verification
- [ ] Test application metrics endpoint
- [ ] Verify Prometheus targets
- [ ] Test Grafana queries
- [ ] Check application logs and traces

### Template Files for New Projects

#### Application Properties Template
```properties
# Replace with your service name
otel.resource.attributes=service.name=your-service-name,service.version=1.0.0,deployment.environment=dev
```

#### Deployment Template
```yaml
metadata:
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/actuator/prometheus"
spec:
  template:
    spec:
      containers:
      - name: your-app
        env:
        - name: OTEL_SERVICE_NAME
          value: "your-service-name"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://tempo-agent.monitoring.svc.cluster.local:4317"
```

#### Agent Configuration Template
```yaml
# Prometheus Agent scrape config
- job_name: 'your-app-name'
  kubernetes_sd_configs:
    - role: pod
  relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app]
      action: keep
      regex: your-app-name
```

### Migration Steps for Existing Projects

1. **Backup current configuration**
2. **Add OpenTelemetry dependencies gradually**
3. **Test in development environment first**
4. **Deploy monitoring stack separately**
5. **Update application deployment**
6. **Verify data collection**
7. **Create custom dashboards**

## 📈 Best Practices

### Performance
- Use sampling for high-traffic applications
- Configure appropriate retention periods
- Monitor resource usage of agents

### Security
- Use RBAC for all service accounts
- Secure Grafana with proper authentication
- Use HTTPS for external access

### Maintenance
- Regular updates of monitoring stack
- Monitor disk usage for logs and metrics
- Backup Grafana dashboards and configurations

### Scaling
- Use multiple replicas for agents
- Configure horizontal pod autoscaling
- Monitor agent performance

## 🎯 Success Metrics

### Technical Metrics
- Application appears in Prometheus targets
- Traces visible in Tempo
- Logs visible in Loki
- No RBAC errors in agent logs
- Grafana queries return data

### Business Metrics
- Reduced MTTR (Mean Time To Resolution)
- Improved application visibility
- Better debugging capabilities
- Proactive monitoring alerts

---

**Note:** This guide assumes a multi-cluster Kubernetes setup with DigitalOcean. Adjust IP addresses, service names, and cluster contexts according to your specific environment. 