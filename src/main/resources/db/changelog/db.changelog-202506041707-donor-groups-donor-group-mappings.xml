<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-************-create-donor-groups.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

                CREATE TABLE  donor_groups (
                id UUID PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description VARCHAR(255),
                filters JSON<PERSON>,
                created_by <PERSON><PERSON><PERSON>,
                updated_by <PERSON><PERSON><PERSON>,
                created_on TIMESTAMP NOT NULL,
                updated_on TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT TRUE
);
            ]]>
        </sql>
    </changeSet>

    <changeSet id="db.changelog-************-create-donor-groups-mappings.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
               CREATE TABLE  donor_group_mapping (
                id UUID PRIMARY KEY,
                donor_id UUID NOT NULL,
                group_id UUID NOT NULL,
                created_on TIMESTAMP,
                updated_on TIMESTAMP,
                created_by UUID,
                updated_by UUID,
                is_active BOOLEAN DEFAULT TRUE,
                CONSTRAINT fk_donor FOREIGN KEY (donor_id) REFERENCES donors(id) ON DELETE CASCADE,
                CONSTRAINT fk_group FOREIGN KEY (group_id) REFERENCES donor_groups(id) ON DELETE CASCADE
            );

            ]]>
        </sql>
    </changeSet>

    <changeSet id="db.changelog-************-create-donor-groups-indexes" author="Grok">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                -- GIN index on filters (JSONB) for efficient JSONB queries
                CREATE INDEX idx_donor_groups_filters ON donor_groups USING GIN (filters);

                -- Partial index on is_active for active groups
                CREATE INDEX idx_donor_groups_is_active ON donor_groups (is_active) WHERE is_active = TRUE;

                -- B-tree index on created_on for sorting/filtering
                CREATE INDEX idx_donor_groups_created_on ON donor_groups (created_on);
            ]]>
        </sql>
    </changeSet>


    <changeSet id="db.changelog-************-create-donor-group-mappings-indexes" author="Grok">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                -- Index on donor_id for lookups and JOINs
                CREATE INDEX idx_donor_group_mapping_donor_id ON donor_group_mapping (donor_id);

                -- Index on group_id for lookups and JOINs
                CREATE INDEX idx_donor_group_mapping_group_id ON donor_group_mapping (group_id);


                -- B-tree index on created_on for sorting/filtering
                CREATE INDEX idx_donor_group_mapping_created_on ON donor_group_mapping (created_on);
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog>