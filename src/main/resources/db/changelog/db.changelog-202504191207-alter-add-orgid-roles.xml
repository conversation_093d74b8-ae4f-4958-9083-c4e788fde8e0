<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-donation-receipt-derived-tables.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

            ALTER TABLE roles ADD COLUMN org_id UUID;

            ALTER TABLE roles
            ADD CONSTRAINT fk_roles_org
            FOREIGN KEY (org_id)
            REFERENCES organisation(id)
            ON DELETE CASCADE;

           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>