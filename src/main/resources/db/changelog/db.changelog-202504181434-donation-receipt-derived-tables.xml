<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-donation-receipt-derived-tables.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
            CREATE TABLE document_repo (
                id uuid NOT NULL,
                category uuid NOT NULL,
                sub_category uuid,
                sender jsonb,
                recipients jsonb,
                "path" varchar(255),
                file_date timestamp,
                created_by uuid NOT NULL,
                updated_by uuid,
                created_on timestamp NOT NULL,
                updated_on timestamp,
                remarks varchar(255),
                tags jsonb,
                is_active bool DEFAULT true,
                CONSTRAINT document_repo_id_pk PRIMARY KEY (id),
                CONSTRAINT documents_repo_category_fk FOREIGN KEY (category) REFERENCES public.resource(id),
                CONSTRAINT documents_repo_sub_category_fk FOREIGN KEY (sub_category) REFERENCES public.resource(id)
            );


            CREATE TABLE individual_sessions (
                id uuid NOT NULL,
                individual_id uuid NOT NULL,
                accesstoken varchar NOT NULL,
                accesstoken_expirytime timestamp NOT NULL,
                accesstoken_generated_on timestamp NOT NULL,
                refreshtoken varchar NOT NULL,
                refreshtoken_expirytime timestamp NOT NULL,
                refreshtoken_generated_on timestamp NOT NULL,
                ipaddress varchar NULL,
                CONSTRAINT individual_sessions_pkey PRIMARY KEY (id),
                CONSTRAINT individual_sessions_ind_fk FOREIGN KEY (individual_id) REFERENCES individual(id)
                );


            CREATE TABLE public.individual_permission (
                id uuid NOT NULL,
                individual_id uuid NOT NULL,
                permissions jsonb NOT NULL,
                created_on timestamp,
                created_by uuid,
                updated_on timestamp,
                updated_by uuid,
                remarks varchar(512),
                org_id uuid,
                CONSTRAINT individual_permission_pk PRIMARY KEY (id),
                CONSTRAINT individual_permission_individual_fk FOREIGN KEY (individual_id) REFERENCES public.individual(id),
                CONSTRAINT individual_permission_organisation_fk FOREIGN KEY (org_id) REFERENCES public.organisation(id)
            );
            CREATE TABLE public.individual_role (
                id uuid NOT NULL,
                individual_id uuid NOT NULL,
                role_id uuid NOT NULL,
                created_on timestamp NOT NULL,
                updated_on timestamp NOT NULL,
                created_by uuid,
                updated_by uuid,
                org_id uuid,
                CONSTRAINT individual_role_id_pk PRIMARY KEY (id),
                CONSTRAINT individual_role_individual_id_fk FOREIGN KEY (individual_id) REFERENCES public.individual(id),
                CONSTRAINT individual_role_organisation_fk FOREIGN KEY (org_id) REFERENCES public.organisation(id),
                CONSTRAINT individual_role_role_id_fk FOREIGN KEY (role_id) REFERENCES public.roles(id)
            );

            CREATE TABLE public.donation_heads (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description VARCHAR(255),
            org_id UUID NOT NULL,
            created_on TIMESTAMP,
            created_by UUID,
            updated_on TIMESTAMP,
            updated_by UUID,
            CONSTRAINT donation_heads_organisation_fk FOREIGN KEY (org_id) REFERENCES public.organisation(id)
);

        CREATE TABLE donation_receipts (
            id UUID PRIMARY KEY,
            receipt_no VARCHAR(100) ,
            org_id UUID NOT NULL,
            meta_data JSONB,
            donation_type_id UUID NOT NULL,
            donation_head_id UUID NOT NULL,
            receipt_date DATE NOT NULL,
            created_on TIMESTAMP,
            created_by UUID  ,
            updated_on TIMESTAMP,
            updated_by UUID,
            CONSTRAINT donation_receipt_org_fk FOREIGN KEY (org_id) REFERENCES organisation(id)
        );



 ]]>
        </sql>
    </changeSet>
</databaseChangeLog>