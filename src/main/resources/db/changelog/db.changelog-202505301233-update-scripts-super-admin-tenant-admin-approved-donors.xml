<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-update-roles-superadmin-tenant-admin-approve-donors.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

UPDATE public.roles SET "name"='Super Admin', description='', parent_role_id=NULL, is_active=true, created_on='2024-05-07 13:58:01.942', updated_on='2025-03-15 10:16:31.368', created_by='abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, updated_by='abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, permissions='[{"name": "Left_Menu", "type": "MENU", "children": [{"name": "Dashboard", "type": "PAGE", "children": [{"name": "Total_Tenants_registered_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Total_donations_received_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Tenanats_SignUps_Overview_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Total_Donations_Growth_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "Donation_Distribution_Pie_Chart_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 1}, {"name": "Profile", "type": "PAGE", "children": [{"name": "Basic_Profile", "type": "SECTION", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Mobile_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Trust_Information", "type": "SECTION", "children": [{"name": "Trust_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Pan_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "80G_Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "State", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "address", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "pin_code", "type": "", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 2}, {"name": "Documents", "type": "SECTION", "children": [{"name": "Upload_Logo", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 1}, {"name": "Upload_80G_Page_1", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}, {"name": "Upload_80G_Page_2", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 3}, {"name": "Member", "type": "SECTION", "children": [{"name": "Add_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 1}, {"name": "Member_DataGrid_Table", "type": "", "children": [{"name": "Edit_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Preview", "type": "SECTION", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 0, "displayNumber": 2}, {"name": "Tenants", "type": "PAGE", "children": [{"name": "Tenants_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 3}, {"name": "Donation_Head", "type": "PAGE", "children": [{"name": "Donation_Head_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [{"name": "Tenant_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [{"name": "Tenant_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Donation_Receipts", "type": "PAGE", "children": [{"name": "Donation_Receipts_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 5}, {"name": "Documents", "type": "PAGE", "children": [{"name": "Documents_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 0, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 0, "displayNumber": 3}], "permissions": 0, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 0, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 0, "displayNumber": 4}], "permissions": 0, "displayNumber": 6}, {"name": "Reports", "type": "PAGE", "children": [{"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Export_To_Excel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Total_Tenants_registered_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Total_donations_received_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Tenanats_SignUps_Overview_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Total_Donations_Growth_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "Donation_Distribution_Pie_Chart_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 7}, {"name": "Receipt_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 8}, {"name": "Head_Wise_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 9}, {"name": "Donor_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 10}, {"name": "Donation_Type_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 11}, {"name": "Payment_Mode_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 12}], "permissions": 0, "displayNumber": 7}, {"name": "Donors", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 8}, {"name": "Approve_Donors", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 9}], "permissions": 15, "displayNumber": 1}, {"name": "Top_Menu", "type": "MENU", "children": [{"name": "User_Management", "type": "MODULE", "children": [{"name": "Users", "type": "PAGE", "children": [{"name": "Edit", "type": "", "children": [{"name": "User_Details", "type": "", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Close", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Delete", "type": "", "children": [{"name": "Yes_Or_NO", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Roles", "type": "PAGE", "children": [{"name": "Add_New_User_Role", "type": "", "children": [{"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "DataAccess", "type": "", "children": [{"name": "Select_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Business_Line", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Select_Parent_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Cancel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Edit", "type": "", "children": [{"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "DataAccess", "type": "", "children": [{"name": "Select_Service_Providers", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Societies_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Business_Line", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Select_Parent_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Cancel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [{"name": "Yes_Or_NO", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Static_Data", "type": "MODULE", "children": [{"name": "System_Constants", "type": "PAGE", "children": [{"name": "State", "type": "TAB", "children": [{"name": "State_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "State_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 3}, {"name": "Donation_Type", "type": "TAB", "children": [{"name": "Donation_Type_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Donation_Type_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 2}, {"name": "Payment_Mode", "type": "TAB", "children": [{"name": "Payment_Mode_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Payment_Mode_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}]'::jsonb, org_id=NULL WHERE id='f4a9c1b2-8e74-4cd7-9a67-3c1f1e9e8f24'::uuid;

UPDATE public.roles SET "name"='Tenant Admin', description='Has full system access', parent_role_id=NULL, is_active=true, created_on='2024-05-07 13:58:01.942', updated_on='2025-03-15 10:16:31.368', created_by='abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, updated_by='abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, permissions='[{"name": "Left_Menu", "type": "MENU", "children": [{"name": "Dashboard", "type": "PAGE", "children": [{"name": "Total_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Unique_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Monthly_Donations_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Donations_By_Head_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}], "permissions": 15, "displayNumber": 1}, {"name": "Profile", "type": "PAGE", "children": [{"name": "Basic_Profile", "type": "SECTION", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Mobile_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Trust_Information", "type": "SECTION", "children": [{"name": "Trust_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Pan_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "80G_Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "State", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "address", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "pin_code", "type": "", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 2}, {"name": "Documents", "type": "SECTION", "children": [{"name": "Upload_Logo", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 1}, {"name": "Upload_80G_Page_1", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}, {"name": "Upload_80G_Page_2", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 3}, {"name": "Member", "type": "SECTION", "children": [{"name": "Add_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 1}, {"name": "Member_DataGrid_Table", "type": "", "children": [{"name": "Edit_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Preview", "type": "SECTION", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 2}, {"name": "Donation_Head", "type": "PAGE", "children": [{"name": "Donation_Head_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [{"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [{"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 3}, {"name": "Donation_Receipts", "type": "PAGE", "children": [{"name": "Donation_Receipts_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Documents", "type": "PAGE", "children": [{"name": "Documents_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 0, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 0, "displayNumber": 3}], "permissions": 0, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 0, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 0, "displayNumber": 4}], "permissions": 0, "displayNumber": 5}, {"name": "Reports", "type": "PAGE", "children": [{"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Export_To_Excel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Total_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Unique_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "Monthly_Donations_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 7}, {"name": "Donations_By_Head_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 8}, {"name": "Receipt_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 9}, {"name": "Head_Wise_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 10}, {"name": "Donor_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 11}, {"name": "Donation_Type_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 12}, {"name": "Payment_Mode_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 13}], "permissions": 0, "displayNumber": 6}, {"name": "Donors", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 7}, {"name": "Approve_Donors", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 8}, {"name": "Import_History", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 9}], "permissions": 15, "displayNumber": 1}, {"name": "Top_Menu", "type": "MENU", "children": [{"name": "User_Management", "type": "MODULE", "children": [{"name": "Users", "type": "PAGE", "children": [{"name": "Edit", "type": "", "children": [{"name": "User_Details", "type": "", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Close", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [{"name": "Yes_Or_NO", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Roles", "type": "PAGE", "children": [{"name": "Add_New_User_Role", "type": "", "children": [{"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "DataAccess", "type": "", "children": [{"name": "Select_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Business_Line", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Select_Parent_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Cancel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Edit", "type": "", "children": [{"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "DataAccess", "type": "", "children": [{"name": "Select_Service_Providers", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Societies_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Business_Line", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Select_Parent_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Cancel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [{"name": "Yes_Or_NO", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 1}]'::jsonb, org_id=NULL WHERE id='c11e1f0a-cd55-41e6-92ec-8e7c41f72d67'::uuid;

UPDATE public.roles SET "name"='Tenant Member', description='Left Menu access', parent_role_id=NULL, is_active=true, created_on='2024-05-07 13:58:01.942', updated_on='2025-03-15 10:16:31.368', created_by='abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, updated_by='abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, permissions='[{"name": "Left_Menu", "type": "MENU", "children": [{"name": "Dashboard", "type": "PAGE", "children": [{"name": "Total_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Unique_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Monthly_Donations_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Donations_By_Head_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}], "permissions": 15, "displayNumber": 1}, {"name": "Profile", "type": "PAGE", "children": [{"name": "Basic_Profile", "type": "SECTION", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Mobile_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Trust_Information", "type": "SECTION", "children": [{"name": "Trust_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Pan_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "80G_Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "State", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "address", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "pin_code", "type": "", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 2}, {"name": "Documents", "type": "SECTION", "children": [{"name": "Upload_Logo", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 1}, {"name": "Upload_80G_Page_1", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}, {"name": "Upload_80G_Page_2", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 3}, {"name": "Member", "type": "SECTION", "children": [{"name": "Member_DataGrid_Table", "type": "", "children": [{"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Preview", "type": "SECTION", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 2}, {"name": "Donation_Head", "type": "PAGE", "children": [{"name": "Donation_Head_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [{"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [{"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Donation_Receipts", "type": "PAGE", "children": [{"name": "Donation_Receipts_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 5}, {"name": "Documents", "type": "PAGE", "children": [{"name": "Documents_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 0, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 0, "displayNumber": 3}], "permissions": 0, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 0, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 0, "displayNumber": 4}], "permissions": 0, "displayNumber": 6}, {"name": "Reports", "type": "PAGE", "children": [{"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Export_To_Excel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Total_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Unique_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Receipt_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 1}, {"name": "Head_Wise_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}, {"name": "Donor_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}, {"name": "Donation_Type_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 4}, {"name": "Payment_Mode_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 6}, {"name": "Import_History", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 1}]'::jsonb, org_id=NULL WHERE id='f5c03c6e-3e4f-45f2-b06f-207a59f0e312'::uuid;

           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>