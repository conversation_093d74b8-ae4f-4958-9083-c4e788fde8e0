<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <!-- Insert admin records for local environment only -->
    <changeSet id="db.changelog-202508141200-insert-admin-records-local.xml" author="Ashraf" context="local">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

                    INSERT INTO public.individual_verification_audit (
                        id, contact_type, contact_value, activation_link, activation_link_created_at,
                        activation_link_expires_at, activation_link_verified_at, ip_address, role_id,
                        verification_status, is_active, created_by, updated_by, created_on, updated_on,
                        meta_data, org_id, otp_code, otp_created_at, otp_expires_at, otp_verified_at
                    )
                    VALUES(
                        'abcdefab-cdef-1234-5678-abcdefabcdef'::uuid, 'EMAIL', '<EMAIL>', true,
                        '2025-03-14 09:00:00.000', '2025-03-15 09:00:00.000', NULL, '127.0.0.1',
                        'f4a9c1b2-8e74-4cd7-9a67-3c1f1e9e8f24'::uuid, 'VERIFIED', true,
                        'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, NULL, '2025-03-14 09:00:00.000',
                        '2025-03-14 09:05:00.000', '{}'::jsonb, NULL, NULL, NULL, NULL, NULL
                    )
                    ON CONFLICT DO NOTHING;

                    INSERT INTO public.individual (
                        id, "name", email, mobile_number, "password", social_login_provider,
                        social_login_provider_id, social_login_provider_image_url, meta_data, is_active,
                        created_by, updated_by, created_on, updated_on
                    )
                    VALUES(
                        'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, 'Super Admin', '<EMAIL>',
                        NULL, '$2a$10$WvXyOQ7LLRmdHRzkTirwGew2uLQb611PFTTJBLlO4bejJGLTK.9z.', NULL, NULL,
                        NULL, NULL, true, NULL, NULL, '2024-05-07 13:58:01.942', '2025-03-15 10:16:31.368'
                    )
                    ON CONFLICT DO NOTHING;

                    INSERT INTO public.organisation (
                        id, "name", category, meta_data, is_active, created_by, updated_by, created_on, updated_on
                    )
                    VALUES(
                        '5a4d79f1-9c67-4b4f-92b5-fbc6b0cc94f8'::uuid, 'CHIDHAGNI', 'SUPER_ADMIN', '{}'::jsonb,
                        true, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid,
                        '2024-05-07 13:58:01.942', '2025-03-15 10:16:31.368'
                    )
                    ON CONFLICT DO NOTHING;

                    INSERT INTO public.individual_role (
                        id, individual_id, role_id, created_on, updated_on, created_by, updated_by, org_id
                    )
                    VALUES(
                        'a4a9c1b2-8e74-4cd7-9a67-3c1f1e9e8f24'::uuid, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid,
                        'f4a9c1b2-8e74-4cd7-9a67-3c1f1e9e8f24'::uuid, '2025-04-21 08:20:55.436',
                        '2025-04-21 08:20:55.436', NULL, NULL, '5a4d79f1-9c67-4b4f-92b5-fbc6b0cc94f8'::uuid
                    )
                    ON CONFLICT DO NOTHING;

                    INSERT INTO public.individual_permission (id, individual_id, permissions, created_on, created_by, updated_on, updated_by, remarks, org_id) VALUES('78c69505-9c06-4f23-8693-b2c6c1df4796'::uuid, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, '[{"name": "Left_Menu", "type": "MENU", "children": [{"name": "Dashboard", "type": "PAGE", "children": [{"name": "Total_Tenants_registered_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Total_donations_received_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Tenanats_SignUps_Overview_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Total_Donations_Growth_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "Donation_Distribution_Pie_Chart_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 1}, {"name": "Profile", "type": "PAGE", "children": [{"name": "Basic_Profile", "type": "SECTION", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Mobile_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Trust_Information", "type": "SECTION", "children": [{"name": "Trust_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Pan_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "80G_Registration_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "State", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "address", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "pin_code", "type": "", "children": [], "permissions": 15, "displayNumber": 7}], "permissions": 15, "displayNumber": 2}, {"name": "Documents", "type": "SECTION", "children": [{"name": "Upload_Logo", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 1}, {"name": "Upload_80G_Page_1", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}, {"name": "Upload_80G_Page_2", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 3}, {"name": "Member", "type": "SECTION", "children": [{"name": "Add_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 1}, {"name": "Member_DataGrid_Table", "type": "", "children": [{"name": "Edit_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Preview", "type": "SECTION", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 0, "displayNumber": 2}, {"name": "Tenants", "type": "PAGE", "children": [{"name": "Tenants_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 3}, {"name": "Donation_Head", "type": "PAGE", "children": [{"name": "Donation_Head_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [{"name": "Tenant_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [{"name": "Tenant_Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Donation_Head", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Description", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 4}, {"name": "Donation_Receipts", "type": "PAGE", "children": [{"name": "Donation_Receipts_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 5}, {"name": "Documents", "type": "PAGE", "children": [{"name": "Documents_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 0, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 0, "displayNumber": 3}], "permissions": 0, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 0, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 0, "displayNumber": 4}], "permissions": 0, "displayNumber": 6}, {"name": "Reports", "type": "PAGE", "children": [{"name": "Advanced_Search", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Export_To_Excel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Total_Tenants_registered_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Total_donations_received_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Tenanats_SignUps_Overview_Bar_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 5}, {"name": "Total_Donations_Growth_Line_Graph_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 6}, {"name": "Donation_Distribution_Pie_Chart_Widget", "type": "", "children": [], "permissions": 15, "displayNumber": 7}, {"name": "Receipt_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 8}, {"name": "Head_Wise_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 9}, {"name": "Donor_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 10}, {"name": "Donation_Type_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 11}, {"name": "Payment_Mode_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 12}], "permissions": 0, "displayNumber": 7}, {"name": "Donors", "type": "PAGE", "children": [], "permissions": 15, "displayNumber": 8}], "permissions": 15, "displayNumber": 1}, {"name": "Top_Menu", "type": "MENU", "children": [{"name": "User_Management", "type": "MODULE", "children": [{"name": "Users", "type": "PAGE", "children": [{"name": "Edit", "type": "", "children": [{"name": "User_Details", "type": "", "children": [{"name": "Name", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Close", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Delete", "type": "", "children": [{"name": "Yes_Or_NO", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Roles", "type": "PAGE", "children": [{"name": "Add_New_User_Role", "type": "", "children": [{"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "DataAccess", "type": "", "children": [{"name": "Select_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Role_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Business_Line", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Select_Parent_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Cancel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Edit", "type": "", "children": [{"name": "Site_Map_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "DataAccess", "type": "", "children": [{"name": "Select_Service_Providers", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Societies_Data", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "Business_Line", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 1}, {"name": "User_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Select_Parent_Role", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Submit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Cancel", "type": "", "children": [], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [{"name": "Yes_Or_NO", "type": "", "children": [], "permissions": 15, "displayNumber": 1}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}, {"name": "Static_Data", "type": "MODULE", "children": [{"name": "System_Constants", "type": "PAGE", "children": [{"name": "State", "type": "TAB", "children": [{"name": "State_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "State_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 5}], "permissions": 15, "displayNumber": 3}, {"name": "Donation_Type", "type": "TAB", "children": [{"name": "Donation_Type_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Donation_Type_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 2}, {"name": "Payment_Mode", "type": "TAB", "children": [{"name": "Payment_Mode_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 15, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 15, "displayNumber": 2}, {"name": "Payment_Mode_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 3}, {"name": "Search_Field", "type": "", "children": [], "permissions": 15, "displayNumber": 4}], "permissions": 15, "displayNumber": 3}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 2}], "permissions": 15, "displayNumber": 1}]'::jsonb, '2025-04-21 08:19:57.648', NULL, '2025-04-21 08:19:57.648', NULL, 'Initial permission setup', '5a4d79f1-9c67-4b4f-92b5-fbc6b0cc94f8'::uuid)
                    ON CONFLICT DO NOTHING;

                    INSERT INTO public.individual_password_reset_audit (id, individual_id, email, reset_link, reset_link_requested_at, reset_link_expires_at, reset_completed_at, reset_status, is_active, created_by, updated_by) VALUES('e495d654-f649-4410-94c4-36b6cd91e620'::uuid, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, '<EMAIL>', '', '2025-03-14 09:00:00.000', '2025-03-14 10:00:00.000', NULL, 'PENDING', true, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, NULL)
                    ON CONFLICT DO NOTHING;
            ]]>
        </sql>
    </changeSet>



</databaseChangeLog>
