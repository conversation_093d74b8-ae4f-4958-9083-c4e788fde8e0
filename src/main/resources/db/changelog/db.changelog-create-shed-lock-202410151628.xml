<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="202410151628_create_shed_lock_if_not_exist" author="siva">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                 CREATE TABLE IF NOT EXISTS shedlock (
                      name VARCHAR(128),
                      lock_until TIMESTAMP(3) NULL,
                      locked_at TIMESTAMP(3) NULL,
                      locked_by VARCHAR(255),
                      PRIMARY KEY (name)
                );
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>