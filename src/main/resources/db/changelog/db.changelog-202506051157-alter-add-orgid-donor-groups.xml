<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-donor-import-alter-script-panNo.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
            ALTER TABLE donor_groups ADD COLUMN org_id UUID;
            ALTER TABLE donor_groups
            ADD CONSTRAINT fk_donor_groups_org
            FOREIGN KEY (org_id)
            REFERENCES organisation(id)
            ON DELETE CASCADE;
           ]]>
        </sql>
    </changeSet>

    <changeSet id="db.changelog-************-remove-unique-constraint-name" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
           ALTER TABLE donor_groups DROP CONSTRAINT IF EXISTS donor_groups_name_key;
           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>