<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-donor-type-donor-role-insert-scripts.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

INSERT INTO public.list_names (id, "name", is_active, created_by, updated_by, created_on, updated_on, is_statistics) VALUES('b23ce94f-1c3c-40fd-bf76-66ad66ed8916'::uuid, 'Donor Type', true, '26c777ac-78a3-11ee-b962-0242ac120002'::uuid, '26c777ac-78a3-11ee-b962-0242ac120002'::uuid, '2025-04-18 04:39:41.417', '2025-04-18 04:39:41.417', NULL);
INSERT INTO public.list_values (id, "name", list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('48c21c80-ccdf-47e7-9264-3e6a9f64efcd'::uuid, 'Individual', 'b23ce94f-1c3c-40fd-bf76-66ad66ed8916'::uuid, true, '26c777ac-78a3-11ee-b962-0242ac120002'::uuid, '26c777ac-78a3-11ee-b962-0242ac120002'::uuid, '2024-04-18 03:39:41.417', '2024-04-18 03:39:41.417');
INSERT INTO public.list_values (id, "name", list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('a4a48b76-f25e-44bc-a81d-d5634f5b5b86'::uuid, 'Entity', 'b23ce94f-1c3c-40fd-bf76-66ad66ed8916'::uuid, true, '26c777ac-78a3-11ee-b962-0242ac120002'::uuid, '26c777ac-78a3-11ee-b962-0242ac120002'::uuid, '2024-04-18 03:39:41.417', '2024-04-18 03:39:41.417');


INSERT INTO public.list_names (id, name, is_active, created_by, updated_by, created_on, updated_on) VALUES('f3a58d9d-caf1-45eb-90b5-205e1b0c701d','Referral Source',TRUE,'26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-12 04:39:41.417', TIMESTAMP '2025-05-12 04:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_values (id, name, list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('3e9a4f9b-9a9d-4533-b8ef-b147f5244035','Social Media','f3a58d9d-caf1-45eb-90b5-205e1b0c701d','TRUE','26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-12 03:39:41.417', TIMESTAMP '2025-05-12 03:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_values (id, name, list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('b62447a2-c89a-4d4a-a9f0-8f87e2c4fc3c','Event','f3a58d9d-caf1-45eb-90b5-205e1b0c701d','TRUE','26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-12 03:39:41.417', TIMESTAMP '2025-05-12 03:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_values (id, name, list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('ffb5e36e-dc38-4d03-9f53-0db90fcbb352','Any Other','f3a58d9d-caf1-45eb-90b5-205e1b0c701d','TRUE','26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-12 03:39:41.417', TIMESTAMP '2025-05-12 03:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_values (id, name, list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('17a8a88d-f5f4-4e8d-82e6-40c83fbd1d10','Friend','f3a58d9d-caf1-45eb-90b5-205e1b0c701d','TRUE','26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-12 03:39:41.417', TIMESTAMP '2025-05-12 03:39:41.417') ON CONFLICT DO NOTHING;


           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>