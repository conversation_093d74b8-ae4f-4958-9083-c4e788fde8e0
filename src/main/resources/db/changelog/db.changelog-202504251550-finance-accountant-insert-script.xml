<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-finance-accountant-insert-script.xml" author="Shashank">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

INSERT INTO roles
(id, "name", description, parent_role_id, is_active, created_on, updated_on, created_by, updated_by, permissions, org_id)
VALUES('ccac9452-763f-4d62-81b3-8b4584de3b38'::uuid, 'Finance Accountant', 'can access and view', 'f5c03c6e-3e4f-45f2-b06f-207a59f0e312'::uuid, true, '2024-05-07 13:58:01.942', '2025-03-15 10:16:31.368', 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, 'abc72613-a04c-4690-a28f-98f0efa7ab10'::uuid, '[{"name": "Left_Menu", "type": "MENU", "children": [{"name": "Dashboard", "type": "PAGE", "children": [{"name": "Total_Donations_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Unique_Donations_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 4}, {"name": "Monthly_Donations_Bar_Graph_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 5}, {"name": "Donations_By_Head_Line_Graph_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 6}], "permissions": 1, "displayNumber": 1}, {"name": "Profile", "type": "PAGE", "children": [{"name": "Basic_Profile", "type": "SECTION", "children": [{"name": "Name", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Email", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Mobile_Number", "type": "", "children": [], "permissions": 1, "displayNumber": 3}], "permissions": 1, "displayNumber": 1}, {"name": "Trust_Information", "type": "SECTION", "children": [{"name": "Trust_Name", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Registration_Number", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Pan_Number", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "80G_Registration_Number", "type": "", "children": [], "permissions": 1, "displayNumber": 4}, {"name": "State", "type": "", "children": [], "permissions": 1, "displayNumber": 5}, {"name": "address", "type": "", "children": [], "permissions": 1, "displayNumber": 6}, {"name": "pin_code", "type": "", "children": [], "permissions": 1, "displayNumber": 7}], "permissions": 1, "displayNumber": 2}, {"name": "Documents", "type": "SECTION", "children": [{"name": "Upload_Logo", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 1}, {"name": "Upload_80G_Page_1", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 2}, {"name": "Upload_80G_Page_2", "type": "SUB_SECTION", "children": [{"name": "Upload", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 3}], "permissions": 1, "displayNumber": 3}, {"name": "Member", "type": "SECTION", "children": [{"name": "Add_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 1, "displayNumber": 4}], "permissions": 1, "displayNumber": 1}, {"name": "Member_DataGrid_Table", "type": "", "children": [{"name": "Edit_Member", "type": "BUTTON", "children": [{"name": "Member_Name", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Contact_Number", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Email", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "Role", "type": "", "children": [], "permissions": 1, "displayNumber": 4}], "permissions": 1, "displayNumber": 2}, {"name": "Delete", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "View", "type": "", "children": [], "permissions": 1, "displayNumber": 4}], "permissions": 1, "displayNumber": 4}], "permissions": 1, "displayNumber": 4}, {"name": "Preview", "type": "SECTION", "children": [], "permissions": 1, "displayNumber": 5}], "permissions": 1, "displayNumber": 2}, {"name": "Donation_Head", "type": "PAGE", "children": [{"name": "Donation_Head_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [{"name": "Donation_Head", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Description", "type": "", "children": [], "permissions": 1, "displayNumber": 2}], "permissions": 1, "displayNumber": 1}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "View", "type": "", "children": [], "permissions": 1, "displayNumber": 3}], "permissions": 1, "displayNumber": 1}, {"name": "Add", "type": "", "children": [{"name": "Donation_Head", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Description", "type": "", "children": [], "permissions": 1, "displayNumber": 2}], "permissions": 1, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 1, "displayNumber": 4}], "permissions": 1, "displayNumber": 4}, {"name": "Donation_Receipts", "type": "PAGE", "children": [{"name": "Donation_Receipts_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 1, "displayNumber": 3}], "permissions": 1, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 1, "displayNumber": 4}], "permissions": 1, "displayNumber": 5}, {"name": "Documents", "type": "PAGE", "children": [{"name": "Documents_DataGrid_Table", "type": "", "children": [{"name": "Edit", "type": "", "children": [], "permissions": 0, "displayNumber": 1}, {"name": "View", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Activate_Or_Deactivate", "type": "", "children": [], "permissions": 0, "displayNumber": 3}], "permissions": 0, "displayNumber": 1}, {"name": "Add", "type": "", "children": [], "permissions": 0, "displayNumber": 2}, {"name": "Advanced_Search", "type": "", "children": [], "permissions": 0, "displayNumber": 3}, {"name": "Main_Search_Field", "type": "", "children": [], "permissions": 0, "displayNumber": 4}], "permissions": 0, "displayNumber": 6}, {"name": "Reports", "type": "PAGE", "children": [{"name": "Advanced_Search", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Export_To_Excel", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Total_Donations_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 1}, {"name": "Unique_Donations_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 2}, {"name": "Last_30Days_Donations_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 3}, {"name": "Average_Donation_Widget", "type": "", "children": [], "permissions": 1, "displayNumber": 4}, {"name": "Receipt_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 1}, {"name": "Head_Wise_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 2}, {"name": "Donor_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 3}, {"name": "Donation_Type_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 4}, {"name": "Payment_Mode_Report_Tab", "type": "TAB", "children": [{"name": "DataGrid_Table", "type": "", "children": [], "permissions": 1, "displayNumber": 1}], "permissions": 1, "displayNumber": 5}], "permissions": 1, "displayNumber": 6}], "permissions": 15, "displayNumber": 1}]'::jsonb, NULL);


           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>