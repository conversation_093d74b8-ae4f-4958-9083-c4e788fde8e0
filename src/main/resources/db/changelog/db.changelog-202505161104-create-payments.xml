<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-************-create-payments.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
           CREATE TABLE Payments (
    id UUID PRIMARY KEY,
    amount DECIMAL(15,2) NOT NULL,
    payment_status VARCHAR(10),
    razorpay_payment_method VARCHAR(50),
    order_id VARCHAR(255),
    payment_date TIMESTAMP NOT NULL ,
    donation_receipt_id uuid NULL,
    is_active BOOLEAN DEFAULT TRUE,
    payment_type UUID,
    created_by UUID,
    updated_by UUID,
    created_on TIMESTAMP NOT NULL,
    updated_on TIMESTAMP NOT NULL,
    razorpay_payment_id VARCHAR(255),
    failure_description VARCHAR(500),
    FOREIGN KEY (donation_receipt_id) REFERENCES donation_Receipts(id)
  );
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog>