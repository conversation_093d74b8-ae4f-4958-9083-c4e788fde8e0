<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-202504221017-alter-drop-createdby-updatedby-contactus" author="Ashraf">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

              ALTER TABLE contact_us DROP COLUMN IF EXISTS created_by;
              ALTER TABLE contact_us DROP COLUMN IF EXISTS updated_by;

           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>