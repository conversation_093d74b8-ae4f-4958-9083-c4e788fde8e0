<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="import-batch.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
           CREATE TABLE import_batch (
            id UUID PRIMARY KEY,
            tenant_org_id UUID NOT NULL,
            category VARCHAR(50) NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            total_records INTEGER DEFAULT 0,
            processed_records INTEGER DEFAULT 0,
            status VARCHAR(20),
            created_on TIMESTAMP ,
            updated_on TIMESTAMP,
            created_by UUID,
            updated_by UUID,
            is_active BOOLEAN DEFAULT TRUE,
            FOREIGN KEY (tenant_org_id) REFERENCES organisation(id)
);

            ]]>
        </sql>
    </changeSet>


    <changeSet id="import_staging.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
         CREATE TABLE import_staging (
            id UUID PRIMARY KEY,
            import_batch_id UUID NOT NULL REFERENCES import_batch(id) ON DELETE CASCADE,
            category VARCHAR(50) NOT NULL,
            name VARCHAR(255),
            mobile_number VARCHAR(15),
            email VARCHAR(255),
            pan_no VARCHAR(20),
            meta_data JSONB,
            status VARCHAR(20),
            created_on TIMESTAMP ,
            updated_on TIMESTAMP,
            created_by UUID,
            updated_by UUID,
            is_active BOOLEAN DEFAULT TRUE

);
            ]]>
        </sql>
    </changeSet>

    <changeSet id="import_staging_and_import_batch_indexex.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
        -- Index on tenant_org_id for filtering batches by tenant (NGO)
        CREATE INDEX idx_import_batch_tenant_org_id ON public.import_batch (tenant_org_id);

        -- Index on category for filtering by entity type (e.g., 'donor')
        CREATE INDEX idx_import_batch_category ON public.import_batch (category);

        -- Index on status for filtering by batch status
        CREATE INDEX idx_import_batch_status ON public.import_batch (status);

        -- Index on created_on for sorting or filtering by creation date
        CREATE INDEX idx_import_batch_created_on ON public.import_batch (created_on);


        -- Index on import_batch_id for filtering by batch
        CREATE INDEX idx_import_staging_batch_id ON public.import_staging (import_batch_id);

        -- Index on category for filtering by entity type
        CREATE INDEX idx_import_staging_category ON public.import_staging (category);

        -- Index on status for filtering by validation status
        CREATE INDEX idx_import_staging_status ON public.import_staging (status);

        -- Index on email for validation (e.g., checking duplicates)
        CREATE INDEX idx_import_staging_email ON public.import_staging (email);

        -- Index on pan_no for validation (e.g., checking duplicates)
        CREATE INDEX idx_import_staging_pan_no ON public.import_staging (pan_no);

        -- Index on meta_data JSONB fields (e.g., state, pinCode)
        CREATE INDEX idx_import_staging_metadata ON public.import_staging USING GIN ((meta_data));
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog>