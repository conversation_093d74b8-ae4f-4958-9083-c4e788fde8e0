<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-alter-add-meta-data-ind-verifcation.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

            ALTER TABLE individual_verification_audit ADD COLUMN meta_data jsonb;

            ALTER TABLE individual_verification_audit RENAME COLUMN role TO role_id;


           ALTER TABLE individual_verification_audit
            ALTER COLUMN role_id TYPE UUID
            USING role_id::UUID;

           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>