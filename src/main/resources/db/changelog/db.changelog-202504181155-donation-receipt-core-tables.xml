<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-donation-receipt-core-tables.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

            CREATE TABLE public.individual (
            id uuid NOT NULL,
            first_name varchar(100) NOT NULL,
            last_name varchar(100) NULL,
            email varchar(100) NULL,
            mobile_number varchar(100) NULL,
            "password" varchar(100) NULL,
            social_login_provider varchar(50) NULL,
            social_login_provider_id varchar(255) NULL,
            social_login_provider_image_url varchar(255) NULL,
            meta_data jsonb NULL,
            is_active bool NULL DEFAULT true,
            created_by uuid NULL,
            updated_by uuid NULL,
            created_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT individual_email_unique UNIQUE (email),
            CONSTRAINT individual_pkey PRIMARY KEY (id)
            );

          CREATE TABLE public.individual_password_reset_audit (
            id uuid NOT NULL,
            individual_id uuid NOT NULL,
            email varchar(200) NOT NULL,
            reset_link varchar(200),
            reset_link_requested_at timestamp,
            reset_link_expires_at timestamp,
            reset_completed_at timestamp,
            reset_status varchar(50),
            is_active bool,
            created_by uuid,
            updated_by uuid,
            CONSTRAINT individual_password_reset_audit_pkey PRIMARY KEY (id),
            CONSTRAINT individual_password_reset_audit_individual_id_fkey
            FOREIGN KEY (individual_id) REFERENCES public.individual(id)
            );

            CREATE TABLE public.individual_verification_audit (
            id uuid NOT NULL,
            contact_type varchar(50) NOT NULL,
            contact_value varchar(255) NOT NULL,
            activation_link Boolean default false,
            activation_link_created_at timestamp NULL,
            activation_link_expires_at timestamp NULL,
            activation_link_verified_at timestamp NULL,
            ip_address varchar(40) NULL,
            "role" varchar(40) NULL,
            verification_status varchar(50) NULL,
            is_active bool NULL,
            created_by uuid NULL,
            updated_by uuid NULL,
            created_on timestamp NULL,
            updated_on timestamp NULL,
            CONSTRAINT individual_verification_audit_pkey PRIMARY KEY (id)
            );


            CREATE TABLE public.organisation (
            id uuid NOT NULL,
            "name" varchar(255) NOT NULL,
            category varchar(100) NOT NULL,
            meta_data jsonb NULL,
            is_active bool NULL DEFAULT true,
            created_by uuid NULL,
            updated_by uuid NULL,
            created_on timestamp NOT NULL,
            updated_on timestamp NOT NULL,
            CONSTRAINT organisation_pkey PRIMARY KEY (id)
            );

            CREATE TABLE public.list_names (
            id uuid NOT NULL,
            "name" varchar(100) NULL,
            is_active bool NULL DEFAULT true,
            created_by uuid NULL,
            updated_by uuid NULL,
            created_on timestamp NOT NULL,
            updated_on timestamp NOT NULL,
            is_statistics bool NULL,
            CONSTRAINT list_names_id_pk PRIMARY KEY (id),
            CONSTRAINT list_names_name_key UNIQUE (name)
            );


            CREATE TABLE public.list_values (
            id uuid NOT NULL,
            "name" varchar(255),
            list_names_id uuid NOT NULL,
            is_active bool DEFAULT true,
            created_by uuid,
            updated_by uuid,
            created_on timestamp NOT NULL,
            updated_on timestamp NOT NULL,
            CONSTRAINT list_values_id_pk PRIMARY KEY (id),
            CONSTRAINT fk_list_names FOREIGN KEY (list_names_id) REFERENCES public.list_names(id)
          );


            CREATE TABLE public.contact_us (
            id uuid NOT NULL,
            name varchar(255) NULL,
            email varchar(255) NULL,
            contact_number varchar(20) NULL,
            message text NULL,
            ip_address varchar(40) NULL,
            created_on timestamp NOT NULL,
            updated_on timestamp NOT NULL,
            created_by uuid NULL,
            updated_by uuid NULL,
            CONSTRAINT contact_us_pkey PRIMARY KEY (id)
            );




            CREATE TABLE public.resource (
            id uuid NOT NULL,
            "name" varchar(100) NOT NULL,
            description varchar(255) NULL,
            "type" varchar(50) NOT NULL,
            parent_resource_id uuid NULL,
            validations jsonb NULL,
            is_active bool NOT NULL DEFAULT true,
            created_on timestamp NOT NULL,
            updated_on timestamp NOT NULL,
            created_by uuid NULL,
            updated_by uuid NULL,
            CONSTRAINT resource_id_pk PRIMARY KEY (id),
            CONSTRAINT resource_parent_resource_fk FOREIGN KEY (parent_resource_id) REFERENCES public.resource(id)
            );


            CREATE TABLE public.roles (
            id uuid NOT NULL,
            "name" varchar(100) NOT NULL,
            description varchar(255) NULL,
            parent_role_id uuid NULL,
            is_active bool NULL DEFAULT true,
            created_on timestamp NOT NULL,
            updated_on timestamp NOT NULL,
            created_by uuid NULL,
            updated_by uuid NULL,
            permissions jsonb NULL,
            CONSTRAINT roles_id_pk PRIMARY KEY (id),
            CONSTRAINT roles_name_unique UNIQUE (name),
            CONSTRAINT roles_parent_role_fk FOREIGN KEY (parent_role_id) REFERENCES public.roles(id)
            );
         ]]>
        </sql>
    </changeSet>
</databaseChangeLog>