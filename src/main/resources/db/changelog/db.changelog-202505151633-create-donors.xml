<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-create-donors.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
            CREATE TABLE donors (
                id uuid NOT NULL,
                tenant_org_id UUID,
                name varchar (100),
                email varchar(100),
                mobile_number varchar(100) NULL,
                pan_no varchar(20),
                meta_data jsonb NULL,
                is_active bool NULL DEFAULT true,
                created_by uuid NULL,
                updated_by uuid NULL,
                created_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_on timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT donors_id_pk PRIMARY KEY (id),
                CONSTRAINT tenant_org_id_fk FOREIGN KEY (tenant_org_id) REFERENCES organisation(id)
            );

]]>
        </sql>
    </changeSet>
</databaseChangeLog>