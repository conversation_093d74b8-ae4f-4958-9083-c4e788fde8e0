<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-202505161221-alter-otp-in-ind-ver.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
            ALTER TABLE individual_verification_audit
            ADD COLUMN otp_code VARCHAR(10),
            ADD COLUMN otp_created_at TIMESTAMP,
            ADD COLUMN otp_expires_at TIMESTAMP,
            ADD COLUMN otp_verified_at TIMESTAMP
           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>