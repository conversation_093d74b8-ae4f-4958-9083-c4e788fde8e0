<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-alter-add-active-donation-heads-receipts-add-orgid-ind-ver.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[

            ALTER TABLE donation_heads ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
            ALTER TABLE donation_receipts ADD COLUMN is_active BOOLEAN DEFAULT TRUE;

            ALTER TABLE individual_verification_audit ADD COLUMN org_id UUID ;
            ALTER TABLE  individual_verification_audit
            ADD CONSTRAINT fk_org_id_ind_verification
            FOREIGN KEY (org_id)
            REFERENCES organisation(id)
            ON DELETE CASCADE;

           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>