<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-202506201719-payment-type-scripts.xml" author="saipriya">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
           INSERT INTO public.list_names (id, name, is_active, created_by, updated_by, created_on, updated_on) VALUES('f47a1c89-42a3-4621-bd5e-9b8ad65c9cf0','Payment Type',TRUE,'26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-13 04:39:41.417', TIMESTAMP '2025-05-13 04:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_values (id, name, list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('12beeb52-7d7e-4cd5-96f3-5879a8f2e64b','Online','f47a1c89-42a3-4621-bd5e-9b8ad65c9cf0','TRUE','26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-13 03:39:41.417', TIMESTAMP '2025-05-13 03:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_values (id, name, list_names_id, is_active, created_by, updated_by, created_on, updated_on) VALUES('8a7c6f1c-f3a4-4f8b-84e1-001987d445c7','Offline','f47a1c89-42a3-4621-bd5e-9b8ad65c9cf0','TRUE','26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-05-13 03:39:41.417', TIMESTAMP '2025-05-13 03:39:41.417') ON CONFLICT DO NOTHING;
INSERT INTO public.list_names (id, name, is_active, created_by, updated_by, created_on, updated_on) VALUES('b19e52fa-8a0f-4e84-9cc2-dcc99a464927','Tags',TRUE,'26c777ac-78a3-11ee-b962-0242ac120002','26c777ac-78a3-11ee-b962-0242ac120002', TIMESTAMP '2025-06-07 04:39:41.417', TIMESTAMP '2025-06-07 04:39:41.417') ON CONFLICT DO NOTHING;
         ]]>
        </sql>
    </changeSet>
</databaseChangeLog>