<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-alter-add-donor-id-in-donor-receipts.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[


            ALTER TABLE donation_receipts ADD COLUMN donor_id UUID;
            ALTER TABLE  donation_receipts
            ADD CONSTRAINT fk_donor_id_individual
            FOREIGN KEY (donor_id)
            REFERENCES individual(id)
            ON DELETE CASCADE;

           ]]>
        </sql>
    </changeSet>
</databaseChangeLog>