package com.chidhagni.web;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;

@RestControllerAdvice
@Slf4j
public class ControllerExceptionAdvice {

    @ExceptionHandler(value = {IllegalArgumentException.class, HttpMessageNotReadableException.class,
            MethodArgumentNotValidException.class, MissingServletRequestParameterException.class,
            InternalAuthenticationServiceException.class})
    public ResponseEntity<ErrorMessage> badRequest(Exception exp, WebRequest req) {
        ErrorMessage error = ErrorMessage.builder()
                .message(exp.getMessage())
                .build();
        log.error("Invalid Input:", exp);
        return ResponseEntity.badRequest().body(error);
    }

    @ExceptionHandler(value = {AccessDeniedException.class})
    public ResponseEntity<ErrorMessage> accessDenied(Exception exp, WebRequest req) {
        ErrorMessage error = ErrorMessage.builder()
                .message(exp.getMessage())
                .build();
        log.error("Access Denied Error:", exp);
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);
    }

    @ExceptionHandler(value = {WatiException.class})
    public ResponseEntity<ErrorMessage> handleWatiServiceException(WatiException e, WebRequest request) {
        ErrorMessage errorMessage = ErrorMessage.builder()
                .message(e.getMessage())
                .build();
        log.error("Wati Service Error:", e);
        return ResponseEntity.internalServerError().body(errorMessage);
    }

    @ExceptionHandler(value = {Exception.class})
    public ResponseEntity<ErrorMessage> internalServerError(Exception exp, WebRequest req) {
        ErrorMessage error = ErrorMessage.builder()
                .message(exp.getMessage())
                .build();
        log.error("Internal Server Error:", exp);
        return ResponseEntity.internalServerError().body(error);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<String> handleNoHandlerFoundException(NoHandlerFoundException e, WebRequest request) {
        ErrorMessage errorMessage = ErrorMessage.builder()
                .message(e.getMessage())
                .build();
        log.error("No Handler Found:", e);
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body("Custom 404 error: The requested resource was not found");
    }
}
