package com.chidhagni.config;


import com.chidhagni.donationreceipt.security.oauth2.CustomOAuth2UserService;
import com.chidhagni.donationreceipt.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository;
import com.chidhagni.donationreceipt.security.oauth2.OAuth2AuthenticationFailureHandler;
import com.chidhagni.donationreceipt.security.oauth2.OAuth2AuthenticationSuccessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(
        securedEnabled = true,
        jsr250Enabled = true,
        prePostEnabled = true
)
@Profile({"dev","prod"})
public class SecurityDevProdConfig {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    @Autowired
    private JwtRequestFilter jwtRequestFilter;



    @Autowired
    private CustomOAuth2UserService customOAuth2UserService;

    @Autowired
    private AuthenticationConfiguration authenticationConfiguration;

    @Autowired
    private OAuth2AuthenticationSuccessHandler auth2AuthenticationSuccessHandler;

    @Autowired
    private OAuth2AuthenticationFailureHandler auth2AuthenticationFailureHandler;

    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/auth/**").permitAll()
                .requestMatchers("/oauth2/**").permitAll()
                .requestMatchers("/public/**").permitAll()
                .requestMatchers("/documents/all").permitAll()
                .requestMatchers("/service-profile/all").permitAll()
                .requestMatchers("/emi-calculator").permitAll()
                .requestMatchers("/api/v1/mobile-otp/**").permitAll()
                .requestMatchers("/list-values/listNameId").permitAll()
                .requestMatchers("/user-services/statistics/{userId}").permitAll()
                .requestMatchers("/documentCategories/all").permitAll()
                .requestMatchers("/documentSubCategories/all").permitAll()
                .requestMatchers("/listNamesValues/all").permitAll()
                .requestMatchers("/documents/projects/images").permitAll()
                .requestMatchers("/service-groups/all").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html", "/dsajczxouidsaljrjesa.html").permitAll()
                .requestMatchers("/pheart/v3/api-docs/**").permitAll()
                .requestMatchers("/cookie").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                .requestMatchers("/admin/api/v1/roles/**").permitAll()
                .anyRequest().authenticated()
            );
        return http.build();
    }

    @Bean
    public OAuth2UserService<OAuth2UserRequest, OAuth2User> oAuth2UserService() {
        return customOAuth2UserService;
    }

    @Bean
    public HttpCookieOAuth2AuthorizationRequestRepository cookieAuthorizationRequestRepository() {
        return new HttpCookieOAuth2AuthorizationRequestRepository();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }


}
