package com.chidhagni.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * Aspect for logging execution of service and repository Spring components.
 * Provides comprehensive logging for the core business packages.
 */
@Aspect
@Component
@Slf4j
public class LoggingAspect {

    /**
     * Pointcut that matches all repositories, services and Web REST endpoints.
     */
    @Pointcut("within(@org.springframework.stereotype.Repository *)" +
            " || within(@org.springframework.stereotype.Service *)" +
            " || within(@org.springframework.web.bind.annotation.RestController *)")
    public void springBeanPointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    /**
     * Pointcut that matches all Spring beans in the core business packages.
     */
    @Pointcut("within(com.chidhagni.donationreceipt.donationhead..*)" +
            " || within(com.chidhagni.donationreceipt.donationreceipts..*)" +
            " || within(com.chidhagni.donationreceipt.donors..*)" +
            " || within(com.chidhagni.donationreceipt.donorgroups..*)" +
            " || within(com.chidhagni.donationreceipt.donorsimport..*)")
    public void coreBusinessPackagePointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    /**
     * Advice that logs methods throwing exceptions.
     */
    @AfterThrowing(pointcut = "coreBusinessPackagePointcut() && springBeanPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {
        log.error("Exception in {}() with cause = {}, message = {}", 
                joinPoint.getSignature().getName(),
                e.getCause() != null ? e.getCause() : "NULL", 
                e.getMessage(), e);
    }

    /**
     * Advice that logs when a method is entered and exited.
     */
    @Around("coreBusinessPackagePointcut() && springBeanPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
        Object[] args = joinPoint.getArgs();

        if (log.isDebugEnabled()) {
            log.debug("→ Enter: {}.{} with arguments = {}", className, methodName, Arrays.toString(args));
        }

        try {
            long startTime = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            if (log.isDebugEnabled()) {
                log.debug("← Exit: {}.{} with result = {} (execution time: {}ms)", 
                        className, methodName, result, executionTime);
            }

            // Log performance warnings for slow operations
            if (executionTime > 1000) { // Log if method takes more than 1 second
                log.warn("⚠ Slow operation detected: {}.{} took {}ms to complete", 
                        className, methodName, executionTime);
            }

            return result;
        } catch (IllegalArgumentException e) {
            log.error("✗ Illegal argument: {} in {}.{}", Arrays.toString(args), className, methodName);
            throw e;
        }
    }

    /**
     * Advice for logging successful business operations
     */
    @AfterReturning(pointcut = "execution(* com.chidhagni.donationreceipt.*.*.create*(..))" +
            " || execution(* com.chidhagni.donationreceipt.*.*.save*(..))" +
            " || execution(* com.chidhagni.donationreceipt.*.*.update*(..))" +
            " || execution(* com.chidhagni.donationreceipt.*.*.delete*(..))" +
            " || execution(* com.chidhagni.donationreceipt.*.*.approve*(..))" +
            " || execution(* com.chidhagni.donationreceipt.*.*.import*(..))",
            returning = "result")
    public void logBusinessOperations(JoinPoint joinPoint, Object result) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
        
        log.info("✓ Business Operation Completed: {}.{} - Result: {}", 
                className, methodName, result != null ? result.getClass().getSimpleName() : "void");
    }

    /**
     * Advice for logging security-related operations
     */
    @AfterReturning(pointcut = "execution(* com.chidhagni.donationreceipt.security..*(..))",
            returning = "result")
    public void logSecurityOperations(JoinPoint joinPoint, Object result) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
        
        log.info("🔐 Security Operation: {}.{} completed", className, methodName);
    }
}
