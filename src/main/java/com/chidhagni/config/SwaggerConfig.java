package com.chidhagni.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    private static final Logger logger = LoggerFactory.getLogger(SwaggerConfig.class);

    @Bean
    public OpenAPI openAPI(
            @Value("${server.servlet.context-path:/}") String contextPath,
            @Value("${server.port:8080}") String serverPort) {
        logger.info("Configuring OpenAPI with context path: {}", contextPath);

        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title("Donation Receipt Service API")
                        .description("Comprehensive donation receipt management system with PLG observability")
                        .version("1.0.0"))
                // Add multiple server configurations for different environments
                .addServersItem(new Server()
                        .url("http://**************:8080" + contextPath)
                        .description("External Production Server"))
                .addServersItem(new Server()
                        .url("http://localhost:" + serverPort + contextPath)
                        .description("Local Development Server"))
                .addServersItem(new Server()
                        .url(contextPath)
                        .description("Relative URL"))
                .addSecurityItem(new SecurityRequirement()
                        .addList("bearerAuth"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")));

        logger.debug("OpenAPI configuration created: {}", openAPI);
        return openAPI;
    }
}
