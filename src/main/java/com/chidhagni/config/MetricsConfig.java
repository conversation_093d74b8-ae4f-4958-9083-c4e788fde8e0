package com.chidhagni.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for custom business metrics using Micrometer.
 * This provides pre-defined counters and timers for business operations.
 */
@Configuration
public class MetricsConfig {

    @Bean
    public Counter donationReceiptCounter(MeterRegistry meterRegistry) {
        return Counter.builder("donation_receipt_created_total")
            .description("Total number of donation receipts created")
            .register(meterRegistry);
    }

    @Bean
    public Counter donorRegistrationCounter(MeterRegistry meterRegistry) {
        return Counter.builder("donor_registration_total")
            .description("Total number of donor registrations")
            .register(meterRegistry);
    }

    @Bean
    public Counter paymentSuccessCounter(MeterRegistry meterRegistry) {
        return Counter.builder("payment_success_total")
            .description("Total number of successful payments")
            .register(meterRegistry);
    }

    @Bean
    public Counter paymentFailureCounter(MeterRegistry meterRegistry) {
        return Counter.builder("payment_failure_total")
            .description("Total number of failed payments")
            .register(meterRegistry);
    }

    @Bean
    public Timer donationReceiptProcessingTimer(MeterRegistry meterRegistry) {
        return Timer.builder("donation_receipt_processing_duration")
            .description("Time taken to process donation receipt")
            .register(meterRegistry);
    }

    @Bean
    public Timer paymentProcessingTimer(MeterRegistry meterRegistry) {
        return Timer.builder("payment_processing_duration")
            .description("Time taken to process payment")
            .register(meterRegistry);
    }

    @Bean
    public Counter emailSentCounter(MeterRegistry meterRegistry) {
        return Counter.builder("email_sent_total")
            .description("Total number of emails sent")
            .register(meterRegistry);
    }

    @Bean
    public Counter smsSentCounter(MeterRegistry meterRegistry) {
        return Counter.builder("sms_sent_total")
            .description("Total number of SMS sent")
            .register(meterRegistry);
    }

    @Bean
    public Counter apiRequestCounter(MeterRegistry meterRegistry) {
        return Counter.builder("api_requests_total")
            .description("Total number of API requests")
            .register(meterRegistry);
    }

    @Bean
    public Counter apiErrorCounter(MeterRegistry meterRegistry) {
        return Counter.builder("api_errors_total")
            .description("Total number of API errors")
            .register(meterRegistry);
    }
} 