package com.chidhagni.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.concurrent.TimeUnit;

@Component
public class ApiMetricsInterceptor implements HandlerInterceptor {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private Counter apiRequestCounter;

    @Autowired
    private Counter apiErrorCounter;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        request.setAttribute("startTime", System.currentTimeMillis());
        apiRequestCounter.increment();
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        Long startTime = (Long) request.getAttribute("startTime");
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            
            // Record response time
            Timer.Sample sample = Timer.start(meterRegistry);
            sample.stop(Timer.builder("http_server_duration")
                .tag("method", request.getMethod())
                .tag("uri", request.getRequestURI())
                .tag("status", String.valueOf(response.getStatus()))
                .register(meterRegistry));

            // Record error if status is 4xx or 5xx
            if (response.getStatus() >= 400) {
                apiErrorCounter.increment();
            }
        }
    }
} 