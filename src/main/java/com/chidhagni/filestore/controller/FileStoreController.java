package com.chidhagni.filestore.controller;

import com.chidhagni.filestore.repository.IFileStoreClient;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.ConversionService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;

@RestController
@CrossOrigin
@RequestMapping("/api/getFile")
public class FileStoreController {

    @Autowired
    private IFileStoreClient fileStoreClient;

    @Autowired
    private ConversionService conversionService;

    public static byte[] loadFile(File file) throws IOException {
        try(InputStream is = new FileInputStream(file)) {

            long length = file.length();
            if (length > Integer.MAX_VALUE) {
                // File is too large
            }
            byte[] bytes = new byte[(int) length];

            int offset = 0;
            int numRead = 0;
            while (offset < bytes.length
                    && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
                offset += numRead;
            }

            if (offset < bytes.length) {
                throw new IOException("Could not completely read file " + file.getName());
            }

            is.close();
            return bytes;
        }
    }

    @GetMapping()
//    @PreAuthorize("hasAnyRole('ROLE_ADMIN','ROLE_SELLER','ROLE_BUYER')")
    public ResponseEntity<HashMap<String, String>> getFile(@RequestParam("location") String location) {

        try {
            File t = fileStoreClient.getFile(location);
            byte[] fileContent = loadFile(t);
            String encodedString = Base64.encodeBase64String(fileContent);

            HashMap<String, String> map = new HashMap<>();
            map.put("data", encodedString);
            return new ResponseEntity<>(map, HttpStatus.OK);
        } catch (IOException e) {
            System.out.println(e);
        }

        return ResponseEntity.ok(null);
    }

    @DeleteMapping("/deleteFile")
    public ResponseEntity<String> deleteFile(@RequestParam("location") String location) {
        try {
            fileStoreClient.deleteFile(location);
            return new ResponseEntity<>("File deleted successfully.", HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>("Error deleting file: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
