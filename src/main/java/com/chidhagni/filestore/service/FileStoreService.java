package com.chidhagni.filestore.service;

import com.chidhagni.filestore.repository.IFileStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;

import static com.chidhagni.filestore.controller.FileStoreController.loadFile;

@Service
@Slf4j
public class FileStoreService implements IFileStoreService {

    @Autowired
    IFileStoreClient fileStoreClient;

    @Override
    public String storeFile(String location, Path path) {
        return fileStoreClient.storeFile(location, path);
    }

    @Override
    public File getFile(String location) {
        return fileStoreClient.getFile(location);
    }

    @Override
    public String getFileContent(String location) {
        try {
            File t = fileStoreClient.getFile(location);
            byte[] fileContent = loadFile(t);
            return Base64.encodeBase64String(fileContent);

        } catch (IOException e) {
            log.error("Image URL is not valid. location: {}", location);
        }
        return null;
    }

    @Override
    public void deleteFile(String location) {
        fileStoreClient.deleteFile(location);
    }

    @Override
    public boolean checkIfFileExists(String location) {
        return fileStoreClient.checkIfFileExists(location);
    }
}
