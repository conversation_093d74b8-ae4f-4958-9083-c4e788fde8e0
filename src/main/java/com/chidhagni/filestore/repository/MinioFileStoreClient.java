package com.chidhagni.filestore.repository;

import io.minio.*;
import io.minio.errors.*;
import org.jooq.exception.DataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(name = "filestore.provider", havingValue = "minio", matchIfMissing = true)
public class MinioFileStoreClient implements IFileStoreClient {

	private static final Logger LOGGER = LoggerFactory.getLogger(MinioFileStoreClient.class);

	@Autowired
	MinioClient minioClient;

	@Value("${filestore.minio.bucket.name}")
	String bucketName;

	private void checkForBucketAvailability() {
		try {
			boolean found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
			if (!found) {
				minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
			} else {
				LOGGER.info("Bucket {} already exists.", bucketName);
			}
		} catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | InternalException
				| InvalidResponseException | NoSuchAlgorithmException | ServerException | XmlParserException
				| IllegalArgumentException | IOException e) {
			throw new DataAccessException("Something went wrong with Bucket Verification", e);
		}
	}

	@Override
	public String storeFile(String location, Path filePath) {
		checkForBucketAvailability();
		try {
			String objectName = location + "/" + filePath.getFileName().toString();
			ObjectWriteResponse objectWriteResponse = minioClient.uploadObject(UploadObjectArgs.builder()
					.bucket(bucketName).object(objectName).filename(filePath.toString()).build());
			return objectWriteResponse.object();
		} catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | InternalException
				| InvalidResponseException | NoSuchAlgorithmException | ServerException | XmlParserException
				| IllegalArgumentException | IOException e) {
			throw new DataAccessException("Something went wrong with File Upload", e);
		}
	}

	@Override
	public File getFile(String location) {
		try {
			List<String> l = Pattern.compile("/").splitAsStream(location).collect(Collectors.toList());
			String saveAs = l.get(l.size() - 2) + l.get(l.size() - 1);
			DownloadObjectArgs args = DownloadObjectArgs.builder().bucket(bucketName).object(location)
					.filename(System.getProperty("java.io.tmpdir") + "/" + saveAs).build();
			minioClient.downloadObject(args);
			return new File(System.getProperty("java.io.tmpdir") + "/" + saveAs);
		} catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | InternalException
				| InvalidResponseException | NoSuchAlgorithmException | ServerException | XmlParserException
				| IllegalArgumentException | IOException e) {
			throw new DataAccessException("Something went wrong with File Upload", e);
		}
	}


	@Override
	public void deleteFile(String location) {
		try {
			minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(location).build());
			LOGGER.info("File {} deleted successfully.", location);
		} catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | InternalException | InvalidResponseException | NoSuchAlgorithmException | ServerException | XmlParserException | IllegalArgumentException | IOException e) {
			throw new DataAccessException("Something went wrong with File Deletion", e);
		}
	}

	@Override
	public boolean checkIfFileExists(String objectName) {
		try {
			return minioClient.statObject(
					StatObjectArgs.builder()
							.bucket(bucketName)
							.object(objectName)
							.build()
			) != null;
		} catch (MinioException e) {
			// Handle MinIO exceptions
			LOGGER.error("Minio: File not found with imageURL: {}", objectName);
			return false;
		} catch (Exception e) {
			LOGGER.error("File not found with imageURL: {}", objectName);
			return false;
		}
	}

}
