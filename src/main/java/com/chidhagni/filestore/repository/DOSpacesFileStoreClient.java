package com.chidhagni.filestore.repository;

import org.jooq.exception.DataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.File;
import java.nio.file.Path;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * DigitalOcean Spaces implementation of IFileStoreClient.
 * Uses AWS S3 SDK for Java since DigitalOcean Spaces is S3-compatible.
 * This implementation is only active when 'filestore.provider' is set to 'digitalocean'.
 */
@Component
@ConditionalOnProperty(name = "filestore.provider", havingValue = "digitalocean")
public class DOSpacesFileStoreClient implements IFileStoreClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(DOSpacesFileStoreClient.class);

    @Autowired
    private S3Client s3Client;

    @Value("${filestore.digitalocean.bucket.name}")
    private String bucketName;

    /**
     * Checks if the bucket exists and creates it if it doesn't.
     * Note: DigitalOcean Spaces buckets must be created manually through their console
     * or API, but we can check for existence.
     */
    private void checkForBucketAvailability() {
        try {
            HeadBucketRequest headBucketRequest = HeadBucketRequest.builder()
                    .bucket(bucketName)
                    .build();
            
            s3Client.headBucket(headBucketRequest);
            LOGGER.info("Bucket {} exists and is accessible.", bucketName);
            
        } catch (NoSuchBucketException e) {
            LOGGER.error("Bucket {} does not exist. Please create it in DigitalOcean Spaces console.", bucketName);
            throw new DataAccessException("Bucket does not exist: " + bucketName, e);
        } catch (Exception e) {
            LOGGER.error("Error checking bucket availability: {}", e.getMessage());
            throw new DataAccessException("Error checking bucket availability", e);
        }
    }

    @Override
    public String storeFile(String location, Path filePath) {
        checkForBucketAvailability();
        try {
            String objectKey = location + "/" + filePath.getFileName().toString();
            
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();

            PutObjectResponse response = s3Client.putObject(putObjectRequest, 
                    RequestBody.fromFile(filePath));
            
            LOGGER.info("File uploaded successfully to DigitalOcean Spaces. ETag: {}", response.eTag());
            return objectKey;
            
        } catch (Exception e) {
            LOGGER.error("Error uploading file to DigitalOcean Spaces: {}", e.getMessage());
            throw new DataAccessException("Something went wrong with File Upload", e);
        }
    }

    @Override
    public File getFile(String location) {
        try {
            List<String> pathParts = Pattern.compile("/").splitAsStream(location)
                    .collect(Collectors.toList());
            String saveAs = pathParts.get(pathParts.size() - 2) + pathParts.get(pathParts.size() - 1);
            
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(location)
                    .build();

            String tempFilePath = System.getProperty("java.io.tmpdir") + "/" + saveAs;
            File tempFile = new File(tempFilePath);
            
            s3Client.getObject(getObjectRequest, tempFile.toPath());
            
            LOGGER.info("File downloaded successfully from DigitalOcean Spaces: {}", location);
            return tempFile;
            
        } catch (NoSuchKeyException e) {
            LOGGER.error("File not found in DigitalOcean Spaces: {}", location);
            throw new DataAccessException("File not found: " + location, e);
        } catch (Exception e) {
            LOGGER.error("Error downloading file from DigitalOcean Spaces: {}", e.getMessage());
            throw new DataAccessException("Something went wrong with File Download", e);
        }
    }

    @Override
    public void deleteFile(String location) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(location)
                    .build();

            s3Client.deleteObject(deleteObjectRequest);
            LOGGER.info("File {} deleted successfully from DigitalOcean Spaces.", location);
            
        } catch (Exception e) {
            LOGGER.error("Error deleting file from DigitalOcean Spaces: {}", e.getMessage());
            throw new DataAccessException("Something went wrong with File Deletion", e);
        }
    }

    @Override
    public boolean checkIfFileExists(String objectName) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectName)
                    .build();

            s3Client.headObject(headObjectRequest);
            return true;
            
        } catch (NoSuchKeyException e) {
            LOGGER.debug("File not found in DigitalOcean Spaces: {}", objectName);
            return false;
        } catch (Exception e) {
            LOGGER.error("Error checking file existence in DigitalOcean Spaces: {}", e.getMessage());
            return false;
        }
    }
}
