package com.chidhagni.filestore.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "filestore.provider", havingValue = "minio", matchIfMissing = true)
public class MinioConfig {
	@Value("${filestore.minio.access.name}")
	String accessKey;
	@Value("${filestore.minio.access.secret}")
	String accessSecret;
	@Value("${filestore.minio.url}")
	String fileStoreUrl;

	@Bean
	public MinioClient generateMinioClient() {
		return MinioClient.builder().endpoint(fileStoreUrl).credentials(accessKey, accessSecret).build();
	}
}
