package com.chidhagni.filestore.converter;

import org.jooq.exception.DataAccessException;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@Component
public class FiletoBytesConverter implements Converter<File, byte[]> {

    @Override
    public byte[] convert(File source) {
        try {
            InputStream is;
            is = new FileInputStream(source);
            long length = source.length();
            if (length > Integer.MAX_VALUE) {
                // File is too large
            }
            byte[] bytes = new byte[(int) length];

            int offset = 0;
            int numRead = 0;
            while (offset < bytes.length
                    && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {

                offset += numRead;
            }

            if (offset < bytes.length) {
                throw new IOException("Could not completely read file " + source.getName());

            }


            is.close();
            return bytes;
        } catch (IOException e) {
            throw new DataAccessException("Bad Data", e);
        }
    }

}
