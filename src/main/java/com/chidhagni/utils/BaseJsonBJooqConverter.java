package com.chidhagni.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import org.jooq.Converter;
import org.jooq.JSONB;

public abstract class BaseJsonBJooqConverter<T> implements Converter<JSONB, T> {

    private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    @SneakyThrows
    @Override
    public T from(JSONB jsonb) {
        if (jsonb == null) {
            return null;
        }
        return objectMapper.readValue(jsonb.data(), toType());
    }

    @SneakyThrows
    @Override
    public JSONB to(T userObject) {
        if (userObject == null) {
            return null;
        }

        return JSONB.valueOf(objectMapper.writeValueAsString(userObject));
    }

    @Override
    public @org.jetbrains.annotations.NotNull Class<JSONB> fromType() {
        return JSONB.class;
    }

    public abstract Class<T> toType();
}
