package com.chidhagni.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * This class provides encryption and decryption functionality using AES-256 in GCM mode.
 * It securely encrypts sensitive data (e.g., PAN numbers) with a key loaded from application properties.
 */
@Slf4j
@Component
public class Aes256EncryptionUtils {
    // The encryption algorithm used (AES/GCM/NoPadding), a standard for secure data encryption
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_NONCE_LENGTH = 12; // 12-byte nonce, as recommended for GCM security
    private static final int GCM_TAG_LENGTH = 16; // 16-byte authentication tag to ensure data integrity

    private SecretKeySpec encryptionKey;

    // Constructor initializes the encryption key from application properties
    public Aes256EncryptionUtils(@Value("${aes.encryption.key}") String aesKey) {
        try {
            // Log the key for debugging (be cautious in production)
            log.info("Loading AES encryption key from application properties (Base64-encoded): {}", aesKey);

            // Validate that the key is not empty
            if (aesKey == null || aesKey.isEmpty()) {
                throw new IllegalStateException("AES encryption key is missing in application properties!");
            }

            // Decode the Base64-encoded key into a 32-byte array
            byte[] keyBytes = Base64.getDecoder().decode(aesKey);
            if (keyBytes.length != 32) {
                throw new IllegalStateException("AES encryption key must be 32 bytes long!");
            }

            // Initialize the key for encryption
            encryptionKey = new SecretKeySpec(keyBytes, "AES");
            log.info("AES encryption key initialized successfully!");
        } catch (Exception e) {
            log.error("Failed to initialize AES encryption key: {}", e.getMessage(), e);
            throw new RuntimeException("Error initializing AES encryption key", e);
        }
    }

    // Encrypts the provided data using AES-256-GCM
    public EncryptedData encrypt(String dataToEncrypt) {
        try {
            validateInputDuringEncryption(dataToEncrypt);
            byte[] nonce = generateNonce();
            Cipher cipher = initializeCipher(Cipher.ENCRYPT_MODE, nonce);
            byte[] encryptedBytes = encryptData(cipher, dataToEncrypt);
            log.info("Data encrypted successfully!");
            return buildEncryptedData(encryptedBytes, nonce);
        } catch (Exception e) {
            log.error("Encryption failed: {}", e.getMessage(), e);
            throw new RuntimeException("Error during encryption", e);
        }
    }

    // Decrypts the provided ciphertext using AES-256-GCM
    public String decrypt(String ciphertext, String nonceBase64) {
        try {
            validateInputDuringDecryption(ciphertext, nonceBase64);
            byte[] nonce = decodeBase64(nonceBase64);
            byte[] encryptedBytes = decodeBase64(ciphertext);
            Cipher cipher = initializeCipher(Cipher.DECRYPT_MODE, nonce);
            String decryptedText = decryptData(cipher, encryptedBytes);
            log.info("Data decrypted successfully!");
            return decryptedText;
        } catch (Exception e) {
            log.error("Decryption failed: {}", e.getMessage(), e);
            throw new RuntimeException("Error during decryption", e);
        }
    }

    // Validates input data before encryption
    private void validateInputDuringEncryption(String input) {
        if (input == null) {
            throw new IllegalArgumentException("Input data for encryption cannot be null!");
        }
    }

    // Validates input data before decryption
    private void validateInputDuringDecryption(String ciphertext, String nonceBase64) {
        if (ciphertext == null || nonceBase64 == null) {
            throw new IllegalArgumentException("Ciphertext and nonce are required for decryption!");
        }
    }

    // Generates a random nonce for GCM mode
    private byte[] generateNonce() {
        byte[] nonce = new byte[GCM_NONCE_LENGTH];
        SecureRandom random = new SecureRandom();
        random.nextBytes(nonce);
        return nonce;
    }

    // Initializes the cipher for encryption or decryption
    private Cipher initializeCipher(int mode, byte[] nonce) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce);
        cipher.init(mode, encryptionKey, gcmSpec);
        return cipher;
    }

    // Encrypts the data using the initialized cipher
    private byte[] encryptData(Cipher cipher, String data) throws Exception {
        return cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    // Decrypts the data using the initialized cipher
    private String decryptData(Cipher cipher, byte[] encryptedBytes) throws Exception {
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    // Builds the encrypted data object with ciphertext and nonce
    private EncryptedData buildEncryptedData(byte[] encryptedBytes, byte[] nonce) {
        String ciphertext = Base64.getEncoder().encodeToString(encryptedBytes);
        String nonceBase64 = Base64.getEncoder().encodeToString(nonce);
        return new EncryptedData(ciphertext, nonceBase64);
    }

    // Decodes a Base64 string into a byte array
    private byte[] decodeBase64(String base64String) {
        return Base64.getDecoder().decode(base64String);
    }
}
