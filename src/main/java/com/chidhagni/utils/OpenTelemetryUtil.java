package com.chidhagni.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * Utility component for verifying and monitoring OpenTelemetry configuration.
 * This component performs startup checks to ensure OpenTelemetry is properly configured
 * and provides logging for trace-related operations.
 */
@Component
public class OpenTelemetryUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenTelemetryUtil.class);
    
    @PostConstruct
    public void init() {
        logger.info("🔍 OpenTelemetryUtil initialized for donation-receipt-service");
        logger.info("📡 Checking OpenTelemetry configuration...");
        
        // Check if OpenTelemetry classes are available
        try {
            Class.forName("io.opentelemetry.api.OpenTelemetry");
            logger.info("✅ OpenTelemetry API is available");
        } catch (ClassNotFoundException e) {
            logger.warn("⚠️ OpenTelemetry API not found: {}", e.getMessage());
        }
        
        try {
            Class.forName("io.opentelemetry.sdk.OpenTelemetrySdk");
            logger.info("✅ OpenTelemetry SDK is available");
        } catch (ClassNotFoundException e) {
            logger.warn("⚠️ OpenTelemetry SDK not found: {}", e.getMessage());
        }
        
        try {
            Class.forName("io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter");
            logger.info("✅ OTLP Exporter is available");
        } catch (ClassNotFoundException e) {
            logger.warn("⚠️ OTLP Exporter not found: {}", e.getMessage());
        }
        
        // Check Java Agent
        String javaAgent = System.getProperty("javaagent");
        if (javaAgent != null && javaAgent.contains("opentelemetry")) {
            logger.info("✅ OpenTelemetry Java Agent detected");
        } else {
            logger.warn("⚠️ OpenTelemetry Java Agent not detected in system properties");
            // Check command line arguments as alternative
            String javaCmd = System.getProperty("sun.java.command");
            if (javaCmd != null && javaCmd.contains("javaagent") && javaCmd.contains("opentelemetry")) {
                logger.info("✅ OpenTelemetry Java Agent detected in command line");
            }
        }
        
        // Check configuration properties
        String otlpEndpoint = System.getProperty("otel.exporter.otlp.endpoint");
        if (otlpEndpoint != null) {
            logger.info("✅ OTLP Endpoint configured: {}", otlpEndpoint);
        } else {
            logger.warn("⚠️ OTLP Endpoint not found in system properties");
        }
        
        String serviceName = System.getProperty("otel.resource.attributes");
        if (serviceName != null && serviceName.contains("service.name")) {
            logger.info("✅ Service name configured in resource attributes");
        } else {
            logger.warn("⚠️ Service name not found in resource attributes");
        }
        
        logger.info("🎯 OpenTelemetry setup verification complete");
        logger.info("📊 Traces will be sent to Tempo Agent in monitoring namespace");
        logger.info("🔗 Expected flow: Application → Tempo Agent → Tempo → Grafana");
    }
    
    /**
     * Log a trace event for debugging purposes
     */
    public void logTraceEvent(String event, String details) {
        logger.info("🔍 TRACE EVENT: {} - {}", event, details);
    }
    
    /**
     * Log span creation for debugging purposes
     */
    public void logSpanCreation(String spanName) {
        logger.info("📊 SPAN CREATED: {}", spanName);
    }
    
    /**
     * Log span completion for debugging purposes
     */
    public void logSpanEnd(String spanName) {
        logger.info("📊 SPAN ENDED: {}", spanName);
    }
    
    /**
     * Log OTLP export operation for debugging purposes
     */
    public void logOtlpExport(String endpoint) {
        logger.info("📡 OTLP EXPORT: Sending traces to {}", endpoint);
    }
    
    /**
     * Log business operation trace for debugging purposes
     */
    public void logBusinessOperation(String operation, String context) {
        logger.info("💼 BUSINESS TRACE: {} - Context: {}", operation, context);
    }
    
    /**
     * Log database operation trace for debugging purposes
     */
    public void logDatabaseOperation(String operation, String table) {
        logger.info("🗄️ DATABASE TRACE: {} on table: {}", operation, table);
    }
    
    /**
     * Log HTTP operation trace for debugging purposes
     */
    public void logHttpOperation(String method, String url) {
        logger.info("🌐 HTTP TRACE: {} {}", method, url);
    }
}
