package com.chidhagni.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorEmailContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class HelperClassToSendCommunication {


    public  static final Map<String, List<String>> EMAIL_TEMPLATE_PARAMS_MAP = Map.of(
            "campaign-announcement", List.of("donorName", "organizationName", "year", "campaignTitle", "campaignLocation",
                    "campaignStartDate", "campaignEndDate", "campaignCause", "campaignLink"),
            "campaign-thank-you-note", List.of("donorName", "organizationName", "year", "donationAmount", "donationDate",
                    "campaignName", "beneficiariesCount", "projectsSupported", "monthlyTarget", "impactArea", "livesImpacted", "impactReportLink"),
            "receipt_email_template", List.of("donorName", "organizationName", "receiptYear", "receiptLink"),
            "acknowledgement_email", List.of("donorName", "organizationName")
    );

    public Map<String, Object> buildEmailContextMap(Donors donor, Organisation org) {
        Map<String, Object> context = new HashMap<>();
        context.put("donorName", donor.getName());
        context.put("organizationName", org.getName());
        context.put("year", 2025);

        // Campaign announcement fields
        context.put("campaignTitle", "Hyderabad Hope Drive");
        context.put("campaignLocation", "Hyderabad");
        context.put("campaignStartDate", LocalDate.now());
        context.put("campaignEndDate", LocalDate.now().plusDays(1));
        context.put("campaignCause", "Medical aid for underprivileged children");
        context.put("campaignLink", "https://yourngo.org/campaigns/hyderabad-drive");

        // Receipt fields
        context.put("receiptYear", 2025);
        context.put("receiptLink", "https://example.com/receipt/" + donor.getId());

        // Thank you note specific fields
        context.put("donationAmount", "5,000");
        context.put("donationDate", LocalDate.now().toString());
        context.put("campaignName", "Education for All Initiative");
        context.put("beneficiariesCount", "25");
        context.put("projectsSupported", "3");
        context.put("monthlyTarget", "50,000");
        context.put("impactArea", "rural education");
        context.put("livesImpacted", "100");
        context.put("impactReportLink", "https://example.com/impact-report/" + donor.getId());

        return context;
    }


    public void setDynamicEmailContextField(DonorEmailContextDTO dto, String fieldName, Object value) {
        try {
            Field field = DonorEmailContextDTO.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(dto, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("Unable to set field '{}' in DonorEmailContextDTO", fieldName, e);
        }
    }

    public Map<String, String> buildWatiParamMap(String templateName, Donors donor, Organisation org) {
        Map<String, String> paramMap = new LinkedHashMap<>();
        switch (templateName) {
            case "campaign_announcement_in_hyd":
                paramMap.put("1", donor.getName());
                paramMap.put("2", "Hyderabad Hope Drive");
                paramMap.put("3", "Hyderabad");
                paramMap.put("4", "15th June 2025");
                paramMap.put("5", "16th June 2025");
                paramMap.put("6", "Medical aid for underprivileged children");
                paramMap.put("7", "https://yourngo.org/campaigns/hyderabad-drive");
                paramMap.put("8", org.getName());
                break;

            case "campaign_reminder":
                paramMap.put("1", donor.getName());
                paramMap.put("2", "Hyderabad");
                paramMap.put("3", "https://yourngo.org/campaigns/hyderabad-drive");
                paramMap.put("4", org.getName());
                break;

            default:
                throw new IllegalArgumentException("Unsupported WATI template: " + templateName);
        }

        return paramMap;
    }
}
