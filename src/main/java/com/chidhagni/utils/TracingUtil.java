package com.chidhagni.utils;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * Utility class for tracing operations in the application.
 * This class provides both logging and actual OpenTelemetry span creation.
 * The Java Agent handles automatic instrumentation, while this class creates
 * custom business-specific spans for enhanced observability.
 */
@Component
@Slf4j
public class TracingUtil {
    
    private final Tracer tracer;
    
    public TracingUtil() {
        this.tracer = GlobalOpenTelemetry.getTracer("donation-receipt-service");
    }

    /**
     * Creates a span for a method execution
     */
    public <T> T traceMethod(String operationName, Supplier<T> operation) {
        Span span = tracer.spanBuilder(operationName)
            .setAttribute("operation.type", "method")
            .startSpan();
            
        try (Scope scope = span.makeCurrent()) {
            log.debug("Tracing method: {}", operationName);
            span.addEvent("method.started");
            
            T result = operation.get();
            
            span.addEvent("method.completed");
            span.setStatus(StatusCode.OK);
            log.debug("Method {} completed successfully", operationName);
            return result;
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            log.error("Method {} failed: {}", operationName, e.getMessage());
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * Creates a span for a method execution that returns void
     */
    public void traceMethod(String operationName, Runnable operation) {
        Span span = tracer.spanBuilder(operationName)
            .setAttribute("operation.type", "method")
            .startSpan();
            
        try (Scope scope = span.makeCurrent()) {
            log.debug("Tracing method: {}", operationName);
            span.addEvent("method.started");
            
            operation.run();
            
            span.addEvent("method.completed");
            span.setStatus(StatusCode.OK);
            log.debug("Method {} completed successfully", operationName);
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            log.error("Method {} failed: {}", operationName, e.getMessage());
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * Creates a span for database operations
     */
    public <T> T traceDatabaseOperation(String operationName, String tableName, Supplier<T> operation) {
        Span span = tracer.spanBuilder(operationName)
            .setAttribute("operation.type", "database")
            .setAttribute("db.table.name", tableName)
            .setAttribute("db.system", "postgresql")
            .startSpan();
            
        try (Scope scope = span.makeCurrent()) {
            log.debug("Tracing database operation: {} on table: {}", operationName, tableName);
            span.addEvent("database.operation.started");
            
            T result = operation.get();
            
            span.addEvent("database.operation.completed");
            span.setStatus(StatusCode.OK);
            log.debug("Database operation {} completed successfully", operationName);
            return result;
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            log.error("Database operation {} failed: {}", operationName, e.getMessage());
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * Creates a span for HTTP client operations
     */
    public <T> T traceHttpClientOperation(String operationName, String url, String method, Supplier<T> operation) {
        Span span = tracer.spanBuilder(operationName)
            .setAttribute("operation.type", "http_client")
            .setAttribute("http.method", method)
            .setAttribute("http.url", url)
            .startSpan();
            
        try (Scope scope = span.makeCurrent()) {
            log.debug("Tracing HTTP operation: {} {} {}", method, operationName, url);
            span.addEvent("http.client.request.started");
            
            T result = operation.get();
            
            span.addEvent("http.client.request.completed");
            span.setStatus(StatusCode.OK);
            log.debug("HTTP operation {} completed successfully", operationName);
            return result;
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            log.error("HTTP operation {} failed: {}", operationName, e.getMessage());
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * Adds an event to the current span
     */
    public void addEvent(String eventName) {
        Span currentSpan = Span.current();
        if (currentSpan.getSpanContext().isValid()) {
            currentSpan.addEvent(eventName);
        }
        log.debug("Tracing event: {}", eventName);
    }

    /**
     * Adds an event with attributes to the current span
     */
    public void addEvent(String eventName, String key, String value) {
        Span currentSpan = Span.current();
        if (currentSpan.getSpanContext().isValid()) {
            currentSpan.addEvent(eventName, 
                io.opentelemetry.api.common.Attributes.of(
                    io.opentelemetry.api.common.AttributeKey.stringKey(key), value));
        }
        log.debug("Tracing event: {} with {}={}", eventName, key, value);
    }

    /**
     * Sets an attribute on the current span
     */
    public void setAttribute(String key, String value) {
        Span currentSpan = Span.current();
        if (currentSpan.getSpanContext().isValid()) {
            currentSpan.setAttribute(key, value);
        }
        log.debug("Setting tracing attribute: {}={}", key, value);
    }

    /**
     * Sets an attribute on the current span
     */
    public void setAttribute(String key, long value) {
        Span currentSpan = Span.current();
        if (currentSpan.getSpanContext().isValid()) {
            currentSpan.setAttribute(key, value);
        }
        log.debug("Setting tracing attribute: {}={}", key, value);
    }

    /**
     * Sets an attribute on the current span
     */
    public void setAttribute(String key, boolean value) {
        Span currentSpan = Span.current();
        if (currentSpan.getSpanContext().isValid()) {
            currentSpan.setAttribute(key, value);
        }
        log.debug("Setting tracing attribute: {}={}", key, value);
    }
} 