package com.chidhagni.utils;

import com.chidhagni.donationreceipt.settings.PageDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class PrivilegesUtil {

    final String CREATE = "CREATE";
    final String UPDATE = "UPDATE";
    final String READ = "READ";
    final String DELETE = "DELETE";

    private Boolean checkPermissions = Boolean.TRUE;

    private List<String> getPermissions(Integer value) {

        //When getting from the settings table we don;t check the permissions.
        if (!checkPermissions) {
            return new ArrayList<>(Arrays.asList(CREATE, UPDATE, READ, DELETE));
        }
        List<String> values = new ArrayList<>();

        if (value >= 8) {
            value -= 8;
            values.add(DELETE);
        }
        if (value >= 4) {
            value -= 4;
            values.add(UPDATE);
        }
        if (value >= 2) {
            value -= 2;
            values.add(READ);
        }
        if (value > 0) {
            values.add(CREATE);
        }

        return values;
    }

    public Integer getPValue(List<String> access) {
        int v = 0;
        if(access.contains(CREATE)) v+=1;
        if(access.contains(READ)) v+=2;
        if(access.contains(UPDATE)) v+=4;
        if(access.contains(DELETE)) v+=8;
        return v;
    }

    public List<String> getConstants() {
        return Arrays.asList(CREATE, READ, UPDATE, DELETE);
    }

    public Map<String, List<String>> getPrivileges(List<PageDTO> pages, Boolean checkPermissions) {


        this.checkPermissions = checkPermissions;
        return pages.stream()
                .collect(Collectors.toMap(
                        PageDTO::getCode,
                        page -> checkPermissions ? page.getPermissions() : new ArrayList<>(Arrays.asList(CREATE, UPDATE, READ, DELETE))
                ));
    }
}
