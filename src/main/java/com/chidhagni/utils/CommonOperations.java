package com.chidhagni.utils;


import com.chidhagni.donationreceipt.member.SystemCodes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;


@Component
@Slf4j
@RequiredArgsConstructor
public class CommonOperations {

    @Value("${token}")
    private String token;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");
    private static final Random random = new Random();
    private static final AtomicInteger counter = new AtomicInteger(0);

    private static final String DEFAULT_SERVICE_PROVIDERS_CONFIG_NAME = "SPsConfiguration";
    private static final String DEFAULT_SOCIETIES_CONFIG_NAME = "SocietiesConfiguration";
    private static final String APPLICATION_JSON = "application/json";
    private static final String APPLICATION_OCTET_STREAM = "application/octet-stream";


    public HttpRequest buildPostRequest(String url, String contentType, HttpRequest.BodyPublisher bodyPublisher) {
        return HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("content-type", contentType)
                .header("Authorization", token)
                .POST(bodyPublisher)
                .build();
    }

    public HttpResponse<String> executeHttpRequest(HttpRequest request) throws IOException, InterruptedException {
        log.info("Sending request to URL: " + request.uri());
        log.info("Request Headers: " + request.headers());
        return HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
    }

    public RequestBody createMultipartRequestBody(String name, MultipartFile file) throws IOException {
        return new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("name", name)
                .addFormDataPart("file", file.getOriginalFilename(),
                        RequestBody.create(MediaType.parse(APPLICATION_OCTET_STREAM), file.getBytes()))
                .build();
    }

    public Request buildMultipartRequest(String url, RequestBody requestBody) {
        return new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Authorization", token)
                .build();
    }


    public static String generateSystemCode(SystemCodes categoryCode) {
        String timestamp = DateUtils.currentTimeIST().format(formatter);
        int randomNum = random.nextInt(100_000); // Five-digit random number
        int count = counter.getAndIncrement() % 1_000; // Three-digit counter

        return categoryCode + timestamp + String.format("%05d", randomNum) + String.format("%03d", count);
    }


    // New method examples for making HTTP requests
    public HttpResponse<String> sendTemplateMessage(String url, String jsonBody) throws IOException, InterruptedException {
        HttpRequest request = buildPostRequest(url, APPLICATION_JSON, HttpRequest.BodyPublishers.ofString(jsonBody));
        return executeHttpRequest(request);
    }

    public Response sendMultipartData(String url, String name, MultipartFile file) throws IOException {
        RequestBody requestBody = createMultipartRequestBody(name, file);
        Request request = buildMultipartRequest(url, requestBody);
        return new OkHttpClient().newCall(request).execute();
    }


    public Response sendTemplateMessageWithDocument(String url, String jsonBody, MultipartFile document) throws IOException {
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("parameters", jsonBody)
                .addFormDataPart("document", document.getOriginalFilename(),
                        RequestBody.create(MediaType.parse("application/pdf"), document.getBytes()));
        RequestBody requestBody = builder.build();

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", token)
                .post(requestBody)
                .build();

        return new OkHttpClient().newCall(request).execute();
    }

}
