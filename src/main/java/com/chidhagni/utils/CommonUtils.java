package com.chidhagni.utils;

//import com.chidhagni.donation_receipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.member.SystemCodes;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;


@RequiredArgsConstructor
@Component
public class CommonUtils {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");
    private static final Random random = new Random();
    private static final AtomicInteger counter = new AtomicInteger(0);

    @Value("${server.hostUrl}")
    public String resetUrl;

    private String encodeLink(String link) {
        return UriComponentsBuilder.fromUriString(link).build().encode().toString();
    }

//    public String generateAndSavePasswordResetCode(IndividualPasswordResetAudit individualPasswordResetAudit) {
//        return encodeLink(resetUrl + "/reset-password/?resetCode=" + individualPasswordResetAudit.getResetLink()
//                + "&emailId=" + individualPasswordResetAudit.getEmail());
//
//    }

    public static String generateSystemCode(SystemCodes categoryCode) {
        String timestamp = DateUtils.currentTimeIST().format(formatter);
        int randomNum = random.nextInt(100_000); // Five-digit random number
        int count = counter.getAndIncrement() % 1_000; // Three-digit counter
        return categoryCode + timestamp + String.format("%05d", randomNum) + String.format("%03d", count);
    }
}




