package com.chidhagni.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtils {
    public static LocalDateTime currentTimeIST() {
        return LocalDateTime.now(ZoneId.of("Asia/Kolkata"));
    }

    public static String currentDateInIST() {
        // Current date
        // Get the current date and time in UTC
        ZonedDateTime nowInUTC = ZonedDateTime.now(ZoneId.of("UTC"));

        // Convert to Asia/Kolkata timezone
        ZonedDateTime nowInKolkata = nowInUTC.withZoneSameInstant(ZoneId.of("Asia/Kolkata"));

        // Extract the date part in Asia/Kolkata timezone
        LocalDate todayInKolkata = nowInKolkata.toLocalDate();

        // Format it to "dd/MM/yyyy"
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        String formattedDate = todayInKolkata.format(formatter);
        return formattedDate;
    }

    public static LocalDate currentDate() {
        return LocalDate.now();
    }

    public static LocalDateTime currentDatetime() {
        return LocalDateTime.now();
    }
}
