package com.chidhagni.donationreceipt.services.listvalues;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class ListValuesMapper {
    public ListValues mapCreate(ListValuesDTO listValuesDTO, UserPrincipal userPrincipal) {
        ListValues listValues = new ListValues();

        listValues.setId(UUID.randomUUID());
        listValues.setName(listValuesDTO.getName());
        listValues.setListNamesId(listValuesDTO.getListNamesId());
        listValues.setIsActive(true);
        listValues.setCreatedOn(DateUtils.currentTimeIST());
        listValues.setUpdatedOn(DateUtils.currentTimeIST());
        listValues.setCreatedBy(userPrincipal.getId());
        listValues.setUpdatedBy(userPrincipal.getId());
        return listValues;
    }

    public ListValuesDTO map(ListValues mappedCreate) {
        return ListValuesDTO.builder()
                .id(mappedCreate.getId())
                .name(mappedCreate.getName())
                .listNamesId(mappedCreate.getListNamesId())
                .isActive(mappedCreate.getIsActive())
                .createdOn(mappedCreate.getCreatedOn())
                .updatedOn(mappedCreate.getUpdatedOn())
                .createdBy(mappedCreate.getCreatedBy())
                .updatedBy(mappedCreate.getUpdatedBy())
                .build();
    }

    public ListValues mapUpdate(ListValuesDTO listValuesDTO, UserPrincipal userPrincipal, ListValues listValueEntity) {
        ListValues listValues = new ListValues();

        listValues.setId(listValuesDTO.getId());
        listValues.setName(listValuesDTO.getName());
        listValues.setListNamesId(listValuesDTO.getListNamesId());
        listValues.setIsActive(listValuesDTO.getIsActive());
        listValues.setCreatedOn(listValueEntity.getCreatedOn());
        listValues.setUpdatedOn(DateUtils.currentTimeIST());
        listValues.setCreatedBy(listValueEntity.getCreatedBy());
        listValues.setUpdatedBy(userPrincipal.getId());
        return listValues;
    }
}
