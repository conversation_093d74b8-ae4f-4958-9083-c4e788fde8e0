package com.chidhagni.donationreceipt.services.listvalues;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.ListValuesDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.Tables.LIST_VALUES;


@Repository
@RequiredArgsConstructor
public class ListValuesRepository {

    private final ListValuesDao listValuesDao;

    public ListValues getById(UUID id) {
        ListValues listValues = null;
        try {
            listValues = listValuesDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching ListValues by it's id", ex);
        }
        return listValues;
    }

    public List<ListValuesDTO> getListValuesByListNameId(UUID listNameId) {
        try {
            return listValuesDao.ctx().select().from(LIST_VALUES).where(LIST_VALUES.LIST_NAMES_ID.eq(listNameId)).fetchInto(ListValuesDTO.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching list values by list name id");
        }
    }


    // This method is used for name of the given id used in decilogic service
    public String fetchNameById(UUID id) {
        return listValuesDao.ctx().select(LIST_VALUES.NAME)
                .from(LIST_VALUES)
                .where(LIST_VALUES.ID.eq(id))
                .fetchOne(LIST_VALUES.NAME);
    }

    public boolean existsById(UUID listValueId) {
        return listValuesDao.ctx().
                fetchExists(listValuesDao.ctx().selectOne().
                        from(LIST_VALUES).where(LIST_VALUES.ID.eq(listValueId)));
    }

    public UUID fetchListValueIdByName(String listValueName) {
        try {
            return listValuesDao.ctx().select(LIST_VALUES.ID).from(LIST_VALUES)
                    .where(LIST_VALUES.NAME.eq(listValueName))
                    .limit(1)
                    .fetchOneInto(UUID.class);
        } catch (Exception ex) {
            throw new InternalServerError(String.format("Exception while fetching list value id by list value name ::" +
                    " %s", listValueName));
        }
    }


    public List<String > getListValuesStringByListNameId(UUID listNameId) {
        try {
            return listValuesDao.ctx().select(LIST_VALUES.NAME).from(LIST_VALUES).where(LIST_VALUES.LIST_NAMES_ID.eq(listNameId)).fetchInto(String.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching list values by list name id");
        }
    }
}


