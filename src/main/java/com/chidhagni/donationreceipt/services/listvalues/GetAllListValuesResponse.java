package com.chidhagni.donationreceipt.services.listvalues;


import com.chidhagni.donationreceipt.services.PaginationRequest;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetAllListValuesResponse {

    private List<ListValuesResponseDTO> listValuesResponse;

    private PaginationRequest paginationRequest;
    private Integer rowCount;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListValuesResponseDTO {
        private UUID id;

        private UUID listNamesId;
        private String name;
        private Boolean isActive;

        private String createdOn;
        private String updatedOn;
        private String createdBy;
        private String updatedBy;
    }
}
