package com.chidhagni.donationreceipt.services.listvalues;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.ListValuesDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.services.PaginationRequest;
import com.chidhagni.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.Tables.*;


@Service
@Slf4j
public class ListValuesService {

    private final ListValuesMapper listValuesMapper;
    private final ListValuesDao listValuesDao;
    private final DSLContext dslContext;
    private final ListValuesRepository listValuesRepository;

    public ListValuesService(ListValuesMapper listValuesMapper, ListValuesDao listValuesDao, DSLContext dslContext
            , ListValuesRepository listValuesRepository) {
        this.listValuesMapper = listValuesMapper;
        this.listValuesDao = listValuesDao;
        this.dslContext = dslContext;
        this.listValuesRepository = listValuesRepository;
    }

    @Transactional
    public ListValuesDTO create(ListValuesDTO listValuesDTO, UserPrincipal userPrincipal) {


        List<ListValues> listValues = listValuesDao.fetchByListNamesId(listValuesDTO.getListNamesId());

        for (ListValues value : listValues) {
            if(value.getIsActive() == true) {
                if (listValuesDTO.getName().equalsIgnoreCase(value.getName())) {
                    throw new IllegalArgumentException("Name matches an existing entry in List Values");
                }
            }
        }
        ListValues mappedCreate = listValuesMapper.mapCreate(listValuesDTO, userPrincipal);
        listValuesDao.insert(mappedCreate);
        ListValuesDTO listValueMapped = listValuesMapper.map(mappedCreate);
        return listValueMapped;
    }

    @Transactional
    public ListValuesDTO updateListValues(ListValuesDTO listValuesDTO, UserPrincipal userPrincipal) {
        ListValues listValueEntity = listValuesDao.fetchOneById(listValuesDTO.getId());

        if (listValueEntity == null) {
            throw new IllegalArgumentException("List Value Doesn't Exist");
        }

        ListValues listValues = listValuesMapper.mapUpdate(listValuesDTO, userPrincipal, listValueEntity);

        listValuesDao.update(listValues);

        return listValuesMapper.map(listValues);
    }

    public ListValuesDTO getListValueById(UUID id) {
        ListValues listValues = listValuesDao.fetchOneById(id);

        if (listValues == null) {
            throw new IllegalArgumentException("List Value Doesn't Exist");
        }

        return listValuesMapper.map(listValues);
    }

    public void deleteListValue(UUID id, UserPrincipal userPrincipal) {
        ListValues listValues = listValuesDao.fetchOneById(id);
        if (listValues == null) {
            throw new IllegalArgumentException(" List Value Doesn't Exist");
        }
        listValues.setIsActive(false);
        listValues.setUpdatedBy(userPrincipal.getId());
        listValues.setUpdatedOn(DateUtils.currentTimeIST());

        listValuesDao.update(listValues);
    }

    public void updateListValue(UUID id, UserPrincipal userPrincipal) {
        ListValues listValues = listValuesDao.fetchOneById(id);
        if (listValues == null) {
            throw new IllegalArgumentException(" List Value Doesn't Exist");
        }
        listValues.setIsActive(true);
        listValues.setUpdatedBy(userPrincipal.getId());
        listValues.setUpdatedOn(DateUtils.currentTimeIST());

        listValuesDao.update(listValues);
    }

    @Transactional
    public void activateListValue(UUID listValueId, UserPrincipal userPrincipal) {
        ListValues listValues = listValuesDao.fetchOneById(listValueId);
        if (listValues == null) {
            throw new IllegalArgumentException(" List Value Doesn't Exist");
        }
        listValues.setIsActive(true);
        listValues.setUpdatedBy(userPrincipal.getId());
        listValues.setUpdatedOn(DateUtils.currentTimeIST());

        listValuesDao.update(listValues);
    }

    public GetAllListValuesResponse getAll(PaginationRequest paginationRequest) {
        if (paginationRequest.getSearchKeyword() == null) {
            paginationRequest.setSearchKeyword("");
        }

        if (paginationRequest.getPage() == null) {
            paginationRequest.setPage(1);
        }

        if (paginationRequest.getPageSize() == null) {
            paginationRequest.setPageSize(10);
        }

        int pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        String dateQuery = "TO_CHAR({0} AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS')";

        List<GetAllListValuesResponse.ListValuesResponseDTO> fetchedListValuesList = dslContext.select(
                        LIST_VALUES.ID,
                        LIST_VALUES.NAME,
                        LIST_VALUES.LIST_NAMES_ID,
                        LIST_VALUES.IS_ACTIVE,

                        DSL.field(dateQuery, String.class, LIST_VALUES.CREATED_ON).as("createdOn"),
                        DSL.field(dateQuery, String.class, LIST_VALUES.UPDATED_ON).as("updatedOn"),
                        INDIVIDUAL.as("m2").EMAIL.as("createdBy"),
                        INDIVIDUAL.as("m3").EMAIL.as("updatedBy"))

                .from(LIST_VALUES)
                .leftOuterJoin(INDIVIDUAL.as("m2")).on(LIST_VALUES.CREATED_BY.eq(INDIVIDUAL.as("m2").ID))
                .leftOuterJoin(INDIVIDUAL.as("m3")).on(LIST_VALUES.UPDATED_BY.eq(INDIVIDUAL.as("m3").ID))
                .where(LIST_VALUES.NAME.containsIgnoreCase(paginationRequest.getSearchKeyword()))
                .orderBy(LIST_VALUES.CREATED_ON.desc())
                .limit(paginationRequest.getPageSize())
                .offset(pageNo)
                .fetchInto(GetAllListValuesResponse.ListValuesResponseDTO.class);

        int count = dslContext.selectCount()
                .from(LIST_VALUES)
                .where(LIST_VALUES.NAME.containsIgnoreCase(paginationRequest.getSearchKeyword()))
                .fetchOne(0, int.class);

        return GetAllListValuesResponse.builder()
                .listValuesResponse(fetchedListValuesList)
                .paginationRequest(paginationRequest)
                .rowCount(count)
                .build();
    }


    @Transactional
    public GetAllListValuesResponse getAllValues(PaginationRequest paginationRequest) {
        if (paginationRequest.getSearchKeyword() == null) {
            paginationRequest.setSearchKeyword("");
        }

        if (paginationRequest.getPage() == null) {
            paginationRequest.setPage(1);
        }

        if (paginationRequest.getPageSize() == null) {
            paginationRequest.setPageSize(10);
        }

        int pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        String dateQuery = "TO_CHAR({0} AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS')";

        List<GetAllListValuesResponse.ListValuesResponseDTO> fetchedListValuesList = dslContext.select(
                        LIST_VALUES.ID,
                        LIST_VALUES.NAME,
                        LIST_VALUES.LIST_NAMES_ID,
                        LIST_VALUES.IS_ACTIVE,

                        DSL.field(dateQuery, String.class, LIST_VALUES.CREATED_ON).as("createdOn"),
                        DSL.field(dateQuery, String.class, LIST_VALUES.UPDATED_ON).as("updatedOn"),
                        INDIVIDUAL.as("m2").EMAIL.as("createdBy"),
                        INDIVIDUAL.as("m3").EMAIL.as("updatedBy"))

                .from(LIST_VALUES)
                .leftOuterJoin(INDIVIDUAL.as("m2")).on(LIST_VALUES.CREATED_BY.eq(INDIVIDUAL.as("m2").ID))
                .leftOuterJoin(INDIVIDUAL.as("m3")).on(LIST_VALUES.UPDATED_BY.eq(INDIVIDUAL.as("m3").ID))
                .where(LIST_VALUES.NAME.containsIgnoreCase(paginationRequest.getSearchKeyword())
                        .and(
                                paginationRequest.getGetAllServices()
                                        ? LIST_VALUES.LIST_NAMES_ID.eq(UUID.fromString("88cf3b1d-8548-41d3-9910-c2f92dcf2815"))
                                        : paginationRequest.getGetAllZones()
                                        ? LIST_VALUES.LIST_NAMES_ID.eq(UUID.fromString("3dcb4434-849f-4b5c-a881-70b31ebde9d2"))
                                        : DSL.falseCondition()
                        )
                )
                .orderBy(LIST_VALUES.CREATED_ON.desc())
                .limit(paginationRequest.getPageSize())
                .offset(pageNo)
                .fetchInto(GetAllListValuesResponse.ListValuesResponseDTO.class);

        int count = dslContext.selectCount()
                .from(LIST_VALUES)
                .where(LIST_VALUES.NAME.containsIgnoreCase(paginationRequest.getSearchKeyword())
                        .and(
                                paginationRequest.getGetAllServices()
                                        ? LIST_VALUES.LIST_NAMES_ID.eq(UUID.fromString("88cf3b1d-8548-41d3-9910-c2f92dcf2815"))
                                        : paginationRequest.getGetAllZones()
                                        ? LIST_VALUES.LIST_NAMES_ID.eq(UUID.fromString("3dcb4434-849f-4b5c-a881-70b31ebde9d2"))
                                        : DSL.falseCondition()
                        )
                )
                .fetchOne(0, int.class);

        return GetAllListValuesResponse.builder()
                .listValuesResponse(fetchedListValuesList)
                .paginationRequest(paginationRequest)
                .rowCount(count)
                .build();
    }


    public GetAllListValuesDTO getAllListValuesById(UUID id) {
        List<GetAllListValuesDTO.ListValuesDTO> fetchedListValues = dslContext.select(
                        LIST_VALUES.ID,
                        LIST_VALUES.IS_ACTIVE,
                        LIST_VALUES.NAME.as("listValue"))
                .from(LIST_VALUES)
                .join(LIST_NAMES).on(LIST_NAMES.ID.eq(LIST_VALUES.LIST_NAMES_ID))
                .where(LIST_NAMES.ID.eq(id))
                .orderBy(LIST_VALUES.NAME.asc())
                .fetchInto(GetAllListValuesDTO.ListValuesDTO.class);

        return GetAllListValuesDTO.builder()
                .listValues(fetchedListValues)
                .build();
    }


    public UUID fetchListValueIdByName(String name){
        UUID listValueIdByName = listValuesRepository.fetchListValueIdByName(name);
        if(listValueIdByName == null){
            throw new IllegalArgumentException(String.format("An List value is not available with the given name :: %s"
                    , name));
        }
        return listValueIdByName;
    }

    public GetAllListValuesResponse getAllListValuesWithPagination(UUID listNameId, PaginationRequest paginationRequest) {
        // Set default values for pagination and search
        if (paginationRequest.getSearchKeyword() == null) {
            paginationRequest.setSearchKeyword("");
        }

        if (paginationRequest.getPage() == null || paginationRequest.getPage() <= 0) {
            paginationRequest.setPage(1);
        }

        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() <= 0) {
            paginationRequest.setPageSize(10);
        }

        int pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        String dateQuery = "TO_CHAR({0} AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS')";

        // Main query with explicit aliases
        List<GetAllListValuesResponse.ListValuesResponseDTO> fetchedListValues = dslContext.select(
                        LIST_VALUES.ID.as("id"),
                        LIST_VALUES.NAME.as("name"),
                        LIST_VALUES.IS_ACTIVE.as("isActive"),
                        DSL.field(dateQuery, String.class, LIST_VALUES.CREATED_ON).as("createdOn"),
                        DSL.field(dateQuery, String.class, LIST_VALUES.UPDATED_ON).as("updatedOn"),
                        INDIVIDUAL.as("m2").EMAIL.as("createdBy"),
                        INDIVIDUAL.as("m3").EMAIL.as("updatedBy")
                )
                .from(LIST_VALUES)
                .join(LIST_NAMES).on(LIST_NAMES.ID.eq(LIST_VALUES.LIST_NAMES_ID))
                .leftOuterJoin(INDIVIDUAL.as("m2")).on(LIST_VALUES.CREATED_BY.eq(INDIVIDUAL.as("m2").ID))
                .leftOuterJoin(INDIVIDUAL.as("m3")).on(LIST_VALUES.UPDATED_BY.eq(INDIVIDUAL.as("m3").ID))
                .where(LIST_NAMES.ID.eq(listNameId)
                        .and(LIST_VALUES.NAME.containsIgnoreCase(paginationRequest.getSearchKeyword())))
                .orderBy(LIST_VALUES.CREATED_ON.desc())
                .limit(paginationRequest.getPageSize())
                .offset(pageNo)
                .fetchInto(GetAllListValuesResponse.ListValuesResponseDTO.class);

        // Count query
        int count = dslContext.selectCount()
                .from(LIST_VALUES)
                .join(LIST_NAMES).on(LIST_NAMES.ID.eq(LIST_VALUES.LIST_NAMES_ID))
                .where(LIST_NAMES.ID.eq(listNameId)
                        .and(LIST_VALUES.NAME.containsIgnoreCase(paginationRequest.getSearchKeyword())))
                .fetchOne(0, int.class);

        // Log results for debugging
        log.info("Fetched {} list values for listNameId: {}, page: {}, pageSize: {}, searchKeyword: {}",
                fetchedListValues.size(), listNameId, paginationRequest.getPage(),
                paginationRequest.getPageSize(), paginationRequest.getSearchKeyword());

        return GetAllListValuesResponse.builder()
                .listValuesResponse(fetchedListValues)
                .paginationRequest(paginationRequest)
                .rowCount(count)
                .build();
    }


}
