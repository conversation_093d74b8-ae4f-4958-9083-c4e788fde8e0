package com.chidhagni.donationreceipt.services.listvalues;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAllListValuesDTO {

    private List<ListValuesDTO> listValues;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListValuesDTO {

        private UUID id;
        private String listValue;
        private Boolean isActive;
    }
}
