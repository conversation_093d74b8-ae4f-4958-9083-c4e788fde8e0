package com.chidhagni.donationreceipt.services;


//import com.chidhagni.donation_receipt.db.jooq.Tables;
import com.chidhagni.donationreceipt.db.jooq.Tables;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class ListNamesValuesService {
    private final DSLContext dslContext;

    public ListNamesValuesService(DSLContext dslContext) {
        this.dslContext = dslContext;
    }

    public ListNamesValuesDTO getAll(ListNamesValuesDTO.SelectType selectType) {

        List<ListNamesValuesDTO.SelectedDTO> selectedDTOList = new ArrayList<>();

        switch (selectType) {

            case LIST_NAMES:
                selectedDTOList = dslContext.select(
                                Tables.LIST_NAMES.ID.as("id"),
                                Tables.LIST_NAMES.NAME.as("name")
                        )
                        .from(Tables.LIST_NAMES)
                        .fetchInto(ListNamesValuesDTO.SelectedDTO.class);
                break;
            case LIST_VALUES:
                selectedDTOList = dslContext.select(
                                Tables.LIST_VALUES.ID.as("id"),
                                Tables.LIST_VALUES.NAME.as("name"),
                                Tables.LIST_VALUES.LIST_NAMES_ID.as("listNamesId")
                        )
                        .from(Tables.LIST_VALUES)
                        .fetchInto(ListNamesValuesDTO.SelectedDTO.class);
                break;

        }

        return ListNamesValuesDTO
                .builder()
                .selectType(selectType)
                .data(selectedDTOList)
                .build();
    }
}
