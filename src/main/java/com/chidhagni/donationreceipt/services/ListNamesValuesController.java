package com.chidhagni.donationreceipt.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/listNamesValues")
public class ListNamesValuesController {

    private final ListNamesValuesService listNamesValuesService;

    public ListNamesValuesController(ListNamesValuesService listNamesValuesService) {
        this.listNamesValuesService = listNamesValuesService;
    }

    @PostMapping("/all")
    public ResponseEntity<ListNamesValuesDTO> getAll(@Valid @RequestParam(value = "selectType")
                                                     ListNamesValuesDTO.SelectType selectType) throws JsonProcessingException {
        ListNamesValuesDTO listNamesValuesDTO = listNamesValuesService.getAll(selectType);
        return ResponseEntity.ok().body(listNamesValuesDTO);

    }


}
