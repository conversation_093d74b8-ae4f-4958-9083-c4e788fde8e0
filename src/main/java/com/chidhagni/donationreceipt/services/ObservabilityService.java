package com.chidhagni.donationreceipt.services;

import com.chidhagni.config.MetricsConfig;
import com.chidhagni.utils.TracingUtil;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Example service demonstrating observability integration.
 * This service shows how to use tracing and metrics in business operations.
 */
@Service
@Slf4j
public class ObservabilityService {

    @Autowired
    private TracingUtil tracingUtil;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private Counter donationReceiptCounter;

    @Autowired
    private Timer donationReceiptProcessingTimer;

    /**
     * Example method demonstrating tracing and metrics integration
     */
    public String processDonationReceipt(String donorId, double amount) {
        return tracingUtil.traceMethod("processDonationReceipt", () -> {
            // Add business context to the span
            tracingUtil.setAttribute("donor.id", donorId);
            tracingUtil.setAttribute("donation.amount", String.valueOf(amount));
            tracingUtil.addEvent("donation_receipt.processing.started");

            // Simulate processing time
            Timer.Sample sample = Timer.start(meterRegistry);
            try {
                // Simulate business logic
                Thread.sleep(100);
                
                // Increment counter for successful processing
                donationReceiptCounter.increment();
                
                tracingUtil.addEvent("donation_receipt.processing.completed");
                
                return "Receipt processed successfully for donor: " + donorId;
            } catch (InterruptedException e) {
                tracingUtil.addEvent("donation_receipt.processing.error", "error.message", e.getMessage());
                throw new RuntimeException("Processing interrupted", e);
            } finally {
                sample.stop(donationReceiptProcessingTimer);
            }
        });
    }

    /**
     * Example method demonstrating database operation tracing
     */
    public String simulateDatabaseOperation(String tableName, String operation) {
        return tracingUtil.traceDatabaseOperation("simulateDatabaseOperation", tableName, () -> {
            tracingUtil.addEvent("database.operation.started", "table", tableName);
            
            // Simulate database operation
            try {
                Thread.sleep(50);
                tracingUtil.addEvent("database.operation.completed", "table", tableName);
                return "Database operation completed on " + tableName;
            } catch (InterruptedException e) {
                tracingUtil.addEvent("database.operation.error", "error.message", e.getMessage());
                throw new RuntimeException("Database operation interrupted", e);
            }
        });
    }

    /**
     * Example method demonstrating HTTP client operation tracing
     */
    public String simulateHttpClientOperation(String url, String method) {
        return tracingUtil.traceHttpClientOperation("simulateHttpClientOperation", url, method, () -> {
            tracingUtil.addEvent("http.client.request.started", "url", url);
            
            // Simulate HTTP client operation
            try {
                Thread.sleep(75);
                tracingUtil.addEvent("http.client.request.completed", "url", url);
                return "HTTP " + method + " request completed to " + url;
            } catch (InterruptedException e) {
                tracingUtil.addEvent("http.client.request.error", "error.message", e.getMessage());
                throw new RuntimeException("HTTP client operation interrupted", e);
            }
        });
    }
} 