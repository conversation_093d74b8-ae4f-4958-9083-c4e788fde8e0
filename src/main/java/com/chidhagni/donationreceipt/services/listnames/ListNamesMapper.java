package com.chidhagni.donationreceipt.services.listnames;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListNames;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class ListNamesMapper {
    public ListNames mapCreate(ListNamesDTO listNamesDTO, UserPrincipal userPrincipal) {
        ListNames listNames = new ListNames();

        listNames.setId(UUID.randomUUID());
        listNames.setName(listNamesDTO.getName());
        listNames.setIsActive(true);

        // Set isStatistics based on the input. If true, set to true. Otherwise, set to false.
        Boolean isStatisticsValue = listNamesDTO.getIsStatistics();
        listNames.setIsStatistics(Boolean.TRUE.equals(isStatisticsValue));

        listNames.setCreatedOn(DateUtils.currentTimeIST());
        listNames.setUpdatedOn(DateUtils.currentTimeIST());
        listNames.setCreatedBy(userPrincipal.getId());
        listNames.setUpdatedBy(userPrincipal.getId());
        return listNames;
    }


    public ListNamesDTO map(ListNames mappedCreate) {
        return ListNamesDTO.builder()
                .id(mappedCreate.getId())
                .name(mappedCreate.getName())
                .isActive(mappedCreate.getIsActive())
                .isStatistics(mappedCreate.getIsStatistics())
                .createdOn(mappedCreate.getCreatedOn())
                .updatedOn(mappedCreate.getUpdatedOn())
                .createdBy(mappedCreate.getCreatedBy())
                .updatedBy(mappedCreate.getUpdatedBy())
                .build();
    }

    public ListNames mapUpdate(ListNamesDTO listNamesDTO, UserPrincipal userPrincipal, ListNames listNameEntity) {
        ListNames listNames = new ListNames();

        listNames.setId(listNamesDTO.getId());
        listNames.setName(listNamesDTO.getName());
        listNames.setIsActive(listNamesDTO.getIsActive());
        // Set isStatistics based on the input. If true, set to true. Otherwise, set to false.
        Boolean isStatisticsValue = listNamesDTO.getIsStatistics();
        listNames.setIsStatistics(Boolean.TRUE.equals(isStatisticsValue));
        listNames.setCreatedOn(listNameEntity.getCreatedOn());
        listNames.setUpdatedOn(DateUtils.currentTimeIST());
        listNames.setCreatedBy(listNameEntity.getCreatedBy());
        listNames.setUpdatedBy(userPrincipal.getId());
        return listNames;
    }
}
