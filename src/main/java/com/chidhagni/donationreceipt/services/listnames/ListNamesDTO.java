package com.chidhagni.donationreceipt.services.listnames;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListNamesDTO {

    private UUID id;
    private String name;
    private Boolean isActive;

    private UUID serviceListValueId;
    private UUID uiComponentsId;
    private Boolean isStatistics;

    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
}
