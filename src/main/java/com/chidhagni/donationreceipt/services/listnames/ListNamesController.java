package com.chidhagni.donationreceipt.services.listnames;

import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.validation.Valid;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@CrossOrigin
@RestController
@RequestMapping("/list-names")
@RequiredArgsConstructor
public class ListNamesController {
    private final ListNamesService listNamesService;


    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListNamesDTO> create(@RequestBody @Valid ListNamesDTO listNamesDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {

        ListNamesDTO listNamesDTO1 = listNamesService.create(listNamesDTO, userPrincipal);
        return ResponseEntity.created(
                        UriComponentsBuilder
                                .fromPath("/list-names")
                                .buildAndExpand()
                                .toUri()
                )
                .body(listNamesDTO1);
    }

    /**
     * Used in Master Data -Service Profile Page in Statistics Tab
     *
     * @param listNamesDTO
     * @param userPrincipal
     * @return
     * @throws Exception
     */
    @PostMapping(value = {"/statistics-questions"}, produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListNamesDTO> createStatisticsQuestions(@RequestBody @Valid ListNamesDTO listNamesDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {

        ListNamesDTO listNamesDTO1 = listNamesService.createStatisticsQuestions(listNamesDTO, userPrincipal);
        return ResponseEntity.created(
                        UriComponentsBuilder
                                .fromPath("/list-names")
                                .buildAndExpand()
                                .toUri()
                )
                .body(listNamesDTO1);
    }


    @PatchMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListNamesDTO> update(@RequestBody @Valid ListNamesDTO listNamesDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {
        return ResponseEntity.ok().body(listNamesService.updateListName(listNamesDTO, userPrincipal));
    }

    @GetMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListNamesDTO> getListName(@PathVariable UUID id) throws JsonProcessingException {
        return ResponseEntity.ok().body(listNamesService.getListNameById(id));
    }

    @DeleteMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
    public void deleteListName(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
        listNamesService.deleteListName(id, userPrincipal);
    }

    @PatchMapping(value = {"/{listNameId}"}, produces = APPLICATION_JSON_VALUE)
    public void activateListName(@PathVariable UUID listNameId, @CurrentUser UserPrincipal userPrincipal) {
        listNamesService.activateListName(listNameId, userPrincipal);
    }
}
