package com.chidhagni.donationreceipt.services.listnames;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.ListNamesDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListNames;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.Tables.LIST_NAMES;


@Service
@RequiredArgsConstructor
public class ListNamesService {
    private final ListNamesMapper listNamesMapper;
    private final ListNamesDao listNamesDao;
    private final DSLContext dslContext;


    @Transactional
    public ListNamesDTO create(ListNamesDTO listNamesDTO, UserPrincipal userPrincipal) {

        List<ListNames> listNames = listNamesDao.fetchByName(listNamesDTO.getName());

        for (ListNames lNames : listNames) {
            if (lNames.getIsActive()) {
                if (listNamesDTO.getName().equalsIgnoreCase(lNames.getName())) {
                    throw new IllegalArgumentException("Name matches an existing entry in List Names");
                }
            }
        }

        ListNames mappedCreate = listNamesMapper.mapCreate(listNamesDTO, userPrincipal);
        listNamesDao.insert(mappedCreate);
        ListNamesDTO serviceTypeMapped = listNamesMapper.map(mappedCreate);
        return serviceTypeMapped;
    }


    @Transactional
    public ListNamesDTO createStatisticsQuestions(ListNamesDTO listNamesDTO, UserPrincipal userPrincipal) {

        // Fetch existing active and statistics list names
        ListNames existingListName = dslContext
                .selectFrom(LIST_NAMES)
                .where(LIST_NAMES.NAME.eq(listNamesDTO.getName())
                        .and(LIST_NAMES.IS_ACTIVE.isTrue())
                        .and(LIST_NAMES.IS_STATISTICS.isTrue()))
                .fetchOneInto(ListNames.class);

        ListNamesDTO newListNameMappedDTO = null;


        ListNames newListName = listNamesMapper.mapCreate(listNamesDTO, userPrincipal);
        listNamesDao.insert(newListName);
        newListNameMappedDTO = listNamesMapper.map(newListName);


        UUID listNameId = (existingListName != null) ? existingListName.getId() : newListNameMappedDTO.getId();

        return newListNameMappedDTO;
    }


    @Transactional
    public ListNamesDTO updateListName(ListNamesDTO listNamesDTO, UserPrincipal userPrincipal) {
        ListNames listNameEntity = listNamesDao.fetchOneById(listNamesDTO.getId());

        if (listNameEntity == null) {
            throw new IllegalArgumentException("List name Doesn't Exist");
        }

        ListNames listName = listNamesMapper.mapUpdate(listNamesDTO, userPrincipal, listNameEntity);

        listNamesDao.update(listName);

        return listNamesMapper.map(listName);
    }

    @Transactional
    public ListNamesDTO getListNameById(UUID id) {
        ListNames listNames = listNamesDao.fetchOneById(id);

        if (listNames == null) {
            throw new IllegalArgumentException("List Name Doesn't Exist");
        }

        return listNamesMapper.map(listNames);
    }

    @Transactional
    public void deleteListName(UUID id, UserPrincipal userPrincipal) {
        ListNames listNames = listNamesDao.fetchOneById(id);
        if (listNames == null) {
            throw new IllegalArgumentException(" List Name Doesn't Exist");
        }
        listNames.setIsActive(false);
        listNames.setUpdatedBy(userPrincipal.getId());
        listNames.setUpdatedOn(DateUtils.currentTimeIST());

        listNamesDao.update(listNames);
    }

    @Transactional
    public void activateListName(UUID listNameId, UserPrincipal userPrincipal) {
        ListNames listNames = listNamesDao.fetchOneById(listNameId);
        if (listNames == null) {
            throw new IllegalArgumentException(" List Name Doesn't Exist");
        }
        listNames.setIsActive(true);
        listNames.setUpdatedBy(userPrincipal.getId());
        listNames.setUpdatedOn(DateUtils.currentTimeIST());

        listNamesDao.update(listNames);
    }
}
