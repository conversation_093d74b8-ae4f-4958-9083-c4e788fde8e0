package com.chidhagni.donationreceipt.services;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Builder
public class PaginationRequest {
    private Integer page;
    private Integer pageSize;
    private String searchKeyword;
    private List<UUID> serviceTypeIds;
    private UUID serviceGroupId;
    private Boolean isStrategicPartner;
    @Builder.Default
    private Boolean getAllServices = false;
    @Builder.Default
    private Boolean getAllZones = false;
}
