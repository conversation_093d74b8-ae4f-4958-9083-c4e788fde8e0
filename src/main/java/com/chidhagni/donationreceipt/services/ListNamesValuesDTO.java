package com.chidhagni.donationreceipt.services;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListNamesValuesDTO {
    private SelectType selectType;
    private List<SelectedDTO> data;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SelectedDTO {
        private UUID id;
        private String name;

        private UUID listNamesId;
    }

    public enum SelectType {
       LIST_NAMES,LIST_VALUES
    }
}
