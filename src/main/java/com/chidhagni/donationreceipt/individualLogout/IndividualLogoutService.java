package com.chidhagni.donationreceipt.individualLogout;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualSessionsDao;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualSessions.INDIVIDUAL_SESSIONS;

@Service
@RequiredArgsConstructor
public class IndividualLogoutService {
    private final IndividualSessionsDao individualSessionsDao;


    public void logout(UserPrincipal userPrincipal) {
        individualSessionsDao.ctx().deleteFrom(INDIVIDUAL_SESSIONS)
                .where(INDIVIDUAL_SESSIONS.INDIVIDUAL_ID.eq(userPrincipal.getId()))
                .execute();
    }
}
