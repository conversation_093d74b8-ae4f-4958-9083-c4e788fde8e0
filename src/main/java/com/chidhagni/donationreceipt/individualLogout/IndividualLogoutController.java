package com.chidhagni.donationreceipt.individualLogout;


import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/logout-individual")
@RequiredArgsConstructor
public class IndividualLogoutController {
    private final IndividualLogoutService individualLogoutService;

    @PostMapping
    public void logout(@CurrentUser UserPrincipal userPrincipal) {
        individualLogoutService.logout(userPrincipal);
    }

}
