package com.chidhagni.donationreceipt.individualPasswordResetAudit;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndividualPasswordResetAuditDTO {

    private String email;
    private UUID individualId;
    private String resetCode;
    private LocalDateTime resetRequestedAt;
    private LocalDateTime resetExpireAt;
    private LocalDateTime resetCompletedAt;
    private String resetStatus;
}
