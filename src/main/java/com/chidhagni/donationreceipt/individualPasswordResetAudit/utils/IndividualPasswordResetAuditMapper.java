package com.chidhagni.donationreceipt.individualPasswordResetAudit.utils;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.UUID;

@Mapper(componentModel = "spring")
public interface IndividualPasswordResetAuditMapper {


    @Mapping(target = "resetCompletedAt",expression = "java(com.chidhagni.utils.DateUtils.currentTimeIST())")
    @Mapping(target = "resetStatus",expression = "java(String.valueOf(com.chidhagni.donationreceipt.individualPasswordResetAudit" +
            ".dto.ResetStatus.ACTIVE))")
    @Mapping(target = "updatedBy",source = "individualId")
    @Mapping(target = "isActive", expression = "java(true)")
    IndividualPasswordResetAudit map(IndividualPasswordResetAudit individualPasswordResetAudit, UUID individualId);


}
