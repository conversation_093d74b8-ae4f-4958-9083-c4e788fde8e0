package com.chidhagni.donationreceipt.individualPasswordResetAudit;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPasswordResetAuditDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT;


@Repository
@Slf4j
@RequiredArgsConstructor
public class IndividualPasswordResetRepository {

   private final IndividualPasswordResetAuditDao individualPasswordResetAuditDao;

    public void create(IndividualPasswordResetAudit individualPasswordResetAudit)
    {
        try{
            individualPasswordResetAuditDao.insert(individualPasswordResetAudit);
            log.info("Successfully created individual password reset");
        }
        catch (Exception e) {
            throw new InternalServerError("Exception occurred while inserting into individual password reset", e);
        }
    }

    public IndividualPasswordResetAudit getByIndividualId(UUID individual) {
        List<IndividualPasswordResetAudit> individualPasswordResetAudit = null;
        try {
            individualPasswordResetAudit = individualPasswordResetAuditDao.fetchByIndividualId(individual);
            return individualPasswordResetAudit.get(0);
        } catch (Exception e) {
           throw new IllegalArgumentException(String.format("No Individual Password Reset Record Found [id=%s]",individual),e);
        }
    }

    public void update(IndividualPasswordResetAudit mapperIndividualPasswordResetAudit) {

        try{
            individualPasswordResetAuditDao.update(mapperIndividualPasswordResetAudit);
        }
        catch (Exception e) {
            throw new IllegalArgumentException(String.format("Exception while Updating individual password reset audit [id=%s]",mapperIndividualPasswordResetAudit.getId()),e);
        }
    }

    public boolean isExistPasswordResetAuditByIndividualId(UUID individualId){
        try{
            return individualPasswordResetAuditDao.ctx().fetchExists(
                    individualPasswordResetAuditDao.ctx().select(INDIVIDUAL_PASSWORD_RESET_AUDIT.ID)
                            .from(INDIVIDUAL_PASSWORD_RESET_AUDIT)
                            .where(INDIVIDUAL_PASSWORD_RESET_AUDIT.INDIVIDUAL_ID.eq(individualId)
                            ));
        }catch (Exception e){
            throw new IllegalArgumentException(String.format("Exception while checking individual password reset audit exist for individual [id=%s]", individualId),e);
        }
    }

}
