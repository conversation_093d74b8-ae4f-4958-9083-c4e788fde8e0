package com.chidhagni.donationreceipt.documentrepo;


import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;


@RestController
@RequestMapping("/api/v1/document")
@RequiredArgsConstructor
public class DocumentRepoController {

    private final DocumentRepoService documentRepoService;
    private final ObjectMapper objectMapper;

    /**
     * Updates an existing document in the repository.
     *
     * <p><b>Usage:</b> Documents -> Edit</p>
     *
     * <p>This endpoint allows authenticated users with `UPDATE_PERMISSION` for the `Documents`
     * section under `Left_Menu` to modify an existing document.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Validates that the file is not null before proceeding.</li>
     *   <li>Parses the `documentDTO` JSON string into a `DocumentRepoDTO` object.</li>
     *   <li>Delegates to `documentRepoService` to update the document metadata and replace the file.</li>
     *   <li>Returns the UUID of the updated document.</li>
     * </ul>
     *
     *
     * @param id             The id of the document to be updated.
     * @param documentDTO    The JSON string containing updated document details.
     * @param file           The new file associated with the document. Cannot be null.
     * @param userPrincipal  The authenticated user performing the update.
     * @return A `ResponseEntity` containing the UUID of the updated document.
     * @throws IOException If an error occurs while processing the file or parsing the JSON data.
     * @throws IllegalArgumentException If the provided file is null.
     * * @throws AccessDeniedException If the user does not have `UPDATE_PERMISSION` for `Documents`.
     * <AUTHOR>
     */

}
