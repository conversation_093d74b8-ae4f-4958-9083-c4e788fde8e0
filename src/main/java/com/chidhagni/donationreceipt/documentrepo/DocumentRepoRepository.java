package com.chidhagni.donationreceipt.documentrepo;



import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.DocumentRepoDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.donationreceipt.documentrepo.dto.request.PaginationRequest;
import com.chidhagni.donationreceipt.documentrepo.dto.response.DocumentDataDTO;
import com.chidhagni.donationreceipt.documentrepo.dto.response.DocumentRepoResponses;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.constants.DefaultOrganisationCategoryEnums;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.filestore.service.IFileStoreService;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.jooq.SortField;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;


import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo.DOCUMENT_REPO;
import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static com.chidhagni.filestore.controller.FileStoreController.loadFile;
import static org.jooq.impl.DSL.field;

@Repository
@Slf4j
@RequiredArgsConstructor
public class DocumentRepoRepository {

    private final DocumentRepoDao documentRepoDao;
    private final IndividualRoleDao individualRoleDao;
    private final OrganizationRepository organizationRepository;
    public final IFileStoreService fileStoreService;



    /**
     * Retrieves a list of document repository responses for admin based on the given condition, sorting, and pagination.
     *
     * @param condition The filtering condition for retrieving documents.
     * @param sortFields The list of fields used for sorting the results.
     * @param paginationRequest The pagination settings, including page number and page size.
     * @return A list of `DocumentRepoResponses` matching the specified criteria.
     * @throws InternalServerError If an error occurs while fetching document repository data.
     */
    public List<DocumentRepoResponses> getAllDocumentRepoResponsesAdmin(
            Condition condition,
            List<SortField<?>> sortFields,
            PaginationRequest paginationRequest
    ) {
        return executeDocumentRepoQuery(condition, sortFields, paginationRequest);
    }


    /**
     * Retrieves all document repository responses based on filtering conditions, sorting, and pagination.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Fetches the organisation ID for the logged-in user.</li>
     *   <li>Determines the organisation category of the user.</li>
     *   <li>Builds a filtering condition based on the user's organisation category.</li>
     *   <li>Combines the provided search condition with the category-based condition.</li>
     *   <li>Executes the document repository query with sorting and pagination.</li>
     * </ul>
     *
     * @param condition The search condition for filtering documents (nullable).
     * @param sortFields A list of fields to sort the results.
     * @param paginationRequest The pagination request containing page size and number.
     * @param userPrincipal The authenticated user requesting the documents.
     * @return A list of  DocumentRepoResponses matching the criteria.
     */
    public List<DocumentRepoResponses> getAllDocumentRepoResponses(
            Condition condition,
            List<SortField<?>> sortFields,
            PaginationRequest paginationRequest,
            UserPrincipal userPrincipal
    ) {
        UUID loggedInUserId = userPrincipal.getId();
        UUID orgId = fetchOrganisationIdForUser(loggedInUserId);
        DefaultOrganisationCategoryEnums organisationCategory = fetchOrganisationCategory(loggedInUserId);

        Condition categoryCondition = buildConditionBasedOnCategory(organisationCategory, orgId);

        Condition effectiveCondition = condition != null ? condition.and(categoryCondition) : categoryCondition;

        return executeDocumentRepoQuery(
                effectiveCondition,
                sortFields,
                paginationRequest
        );
    }

    public UUID fetchOrganisationIdForUser(UUID loggedInUserId) {
        return Objects.requireNonNull(individualRoleDao.ctx()
                .select(INDIVIDUAL_ROLE.ORG_ID)
                .from(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(loggedInUserId))
                .fetchOneInto(UUID.class));
    }

    public DefaultOrganisationCategoryEnums fetchOrganisationCategory(UUID loggedInUserId) {
        return null;
    }


    /**
     * Builds a condition for filtering documents based on the organisation category.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>If the organisation category is EMPLOYEE, returns no condition.</li>
     *   <li>If the organisation category is  SOCIETY or SERVICE_PROVIDER, filters documents where:</li>
     *   <ul>
     *     <li>The sender's organisation ID matches the given organisation ID.</li>
     *     <li>OR the recipient's organisation ID matches the given organisation ID.</li>
     *   </ul>
     *   <li>Throws an exception if the organisation category is invalid.</li>
     * </ul>
     *
     * @param organisationCategory The category of the organisation.
     * @param orgId The UUID of the organisation.
     * @return A {@code Condition} representing the filtering criteria.
     * @throws IllegalArgumentException If the organisation category is invalid.
     */
    private Condition buildConditionBasedOnCategory(DefaultOrganisationCategoryEnums organisationCategory, UUID orgId) {
        switch (organisationCategory) {
            case EMPLOYEE:
                return DSL.noCondition();
            case SOCIETY:
            case SERVICE_PROVIDER:
                return field("DOCUMENT_REPO.SENDER ->> 'organisationId'", String.class)
                        .eq(orgId.toString())
                        .or(field("recipients_json ->> 'organisationId'", String.class)
                                .eq(orgId.toString()));
            default:
                throw new IllegalArgumentException("Invalid organisation category");
        }
    }

    /**
     * Executes a query to fetch document repository responses based on filtering, sorting, and pagination.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Calculates the pagination offset based on the requested page number and size.</li>
     *   <li>Expands the recipients JSON array into rows using a lateral join.</li>
     *   <li>Constructs a query that selects document details, sender, and recipient information.</li>
     *   <li>Joins with the `INDIVIDUAL` and `ORGANISATION` tables to fetch sender and recipient details.</li>
     *   <li>Applies the filtering condition, sorting fields, and pagination.</li>
     *   <li>Executes the query and maps the results into a list of `DocumentRepoResponses` objects.</li>
     *   <li>Handles any exceptions that occur during execution.</li>
     * </ul>
     *
     * <p><b>Database Operations:</b></p>
     * <ul>
     *   <li>Performs a `LEFT JOIN` to fetch sender and receiver details.</li>
     *   <li>Uses `jsonb_array_elements` to expand recipient JSON arrays into separate rows.</li>
     *   <li>Applies sorting, filtering, and pagination dynamically based on the request parameters.</li>
     * </ul>
     *
     *
     * @param condition The filtering condition to apply to the query.
     * @param sortFields The sorting fields to order the results.
     * @param paginationRequest The pagination request specifying page number and size.
     * @return A list of `DocumentRepoResponses` objects matching the query criteria.
     * @throws InternalServerError If an exception occurs while fetching documents from the database.
     */
    private List<DocumentRepoResponses> executeDocumentRepoQuery(
            Condition condition,
            List<SortField<?>> sortFields,
            PaginationRequest paginationRequest
    ) {
        Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();

        // as the recipients is in json array format , here i'm expanding the array into rows and temporarily storing in table as rows
        Table<?> recipientsJson = DSL.table("jsonb_array_elements(DOCUMENT_REPO.RECIPIENTS) AS recipients_json");

        try {
            SelectJoinStep<?> baseQuery = documentRepoDao.ctx()
                    .select(
                            DOCUMENT_REPO.ID,
                            DOCUMENT_REPO.CATEGORY,
                            DOCUMENT_REPO.SUB_CATEGORY,
                            field("tags ->> 'documentName'", String.class).as("documentName"),
                            field("DOCUMENT_REPO.SENDER ->> 'individualId'", UUID.class).as("senderIndividualId"),
                            field("DOCUMENT_REPO.SENDER ->> 'organisationId'", UUID.class).as("senderOrganisationId"),
                            field("DOCUMENT_REPO.SENDER ->> 'type'", String.class).as("senderType"),
                            DSL.concat(
                                    DSL.coalesce(field(DSL.name("senderIndividual", "first_name")), DSL.val("")),
                                    DSL.val(" "),
                                    DSL.coalesce(field(DSL.name("senderIndividual", "last_name")), DSL.val(""))
                            ).as("senderIndividualName"),

                            field(DSL.name("senderOrganisation", "name")).as("senderOrganisationName"),
                            field(DSL.name("senderOrganisation", "category")).as("senderOrganisationCategory"),
                            field("recipients_json ->> 'individualId'", UUID.class).as("receiverIndividualId"),
                            field("recipients_json ->> 'organisationId'", UUID.class).as("receiverOrganisationId"),
                            DSL.concat(
                                    DSL.coalesce(field(DSL.name("receiverIndividual", "first_name")), DSL.val("")),
                                    DSL.val(" "),
                                    DSL.coalesce(field(DSL.name("receiverIndividual", "last_name")), DSL.val(""))
                            ).as("receiverIndividualName"),
                            field(DSL.name("receiverOrganisation", "name")).as("receiverOrganisationName"),
                            field(DSL.name("receiverOrganisation", "category")).as("receiverOrganisationCategory"),
                            DOCUMENT_REPO.FILE_DATE,
                            DOCUMENT_REPO.REMARKS,
                            DOCUMENT_REPO.PATH,
                            DOCUMENT_REPO.CREATED_BY,
                            DOCUMENT_REPO.UPDATED_BY,
                            DOCUMENT_REPO.CREATED_ON,
                            DOCUMENT_REPO.UPDATED_ON
                    )
                    .from(DOCUMENT_REPO)
                    // Sender Joins
                    .leftJoin(INDIVIDUAL.as("senderIndividual"))
                    .on(field("DOCUMENT_REPO.SENDER ->> 'individualId'", String.class).cast(UUID.class)
                            .eq(field(DSL.name("senderIndividual", "id"), UUID.class)))
                    .leftJoin(ORGANISATION.as("senderOrganisation"))
                    .on(field("DOCUMENT_REPO.SENDER ->> 'organisationId'", String.class).cast(UUID.class)
                            .eq(field(DSL.name("senderOrganisation", "id"), UUID.class)))
                    // Lateral Join for Recipients
                    .join(recipientsJson).on(DSL.noCondition())
                    .leftJoin(INDIVIDUAL.as("receiverIndividual"))
                    .on(field("recipients_json ->> 'individualId'", String.class).cast(UUID.class)
                            .eq(field(DSL.name("receiverIndividual", "id"), UUID.class)))
                    .leftJoin(ORGANISATION.as("receiverOrganisation"))
                    .on(field("recipients_json ->> 'organisationId'", String.class).cast(UUID.class)
                            .eq(field(DSL.name("receiverOrganisation", "id"), UUID.class)));

            // Apply the condition, sorting, and pagination
            return baseQuery.where(condition)
                    .orderBy(sortFields)
                    .limit(paginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(DocumentRepoResponses.class);

        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching documents", ex);
        }
    }


    public Integer getDocumentRepoResponsesCount(Condition condition) {
        try {
            return documentRepoDao.ctx().selectCount()
                    .from(DOCUMENT_REPO)
                    .where(condition)
                    .fetchOne(0, Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching document responses count", ex);
        }
    }

    public Integer getDocumentRepoResponsesCountAdmin(Condition condition) {
        try {
            return documentRepoDao.ctx().selectCount()
                    .from(DOCUMENT_REPO)
                    .where(condition)
                    .fetchOne(0, Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching document responses count", ex);
        }
    }


    public DocumentRepo getById(UUID id) {
        DocumentRepo documentRepo = null;
        try {

            documentRepo = documentRepoDao.fetchOneById(id);
            log.info("Fetched Document Successfully");

        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching document", ex);

        }

        return documentRepo;
    }

    public void insertDocument(DocumentRepo documentRepo) {
        try {
            documentRepoDao.insert(documentRepo);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while inserting the document");
        }
    }

    public DocumentRepo getDocumentByID(UUID id) {
        try {
            return documentRepoDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching document by id");
        }
    }

    public void updateDocument(DocumentRepo... documentRepo) {
        try {
            documentRepoDao.update(documentRepo);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while updating document");
        }
    }

    public UUID getDocumentByFileLocation(String fileLocation, UUID orgId) {
        try {
            return documentRepoDao.ctx()
                    .select(DOCUMENT_REPO.ID)
                    .from(DOCUMENT_REPO)
                    .where(DOCUMENT_REPO.PATH.eq(fileLocation.trim()))
                    .and(DOCUMENT_REPO.SENDER.isNotNull())
                    .and(DSL.field("DOCUMENT_REPO.sender ->> 'organisationId'", String.class)
                            .eq(orgId.toString()))
                    .and(DOCUMENT_REPO.IS_ACTIVE.eq(Boolean.TRUE))
                    .fetchOneInto(UUID.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching document by location", ex);
        }
    }


    /**
     * Deactivates a document by updating its status to inactive.
     *
     * @param documentId The unique identifier of the document to be deactivated.
     * @param loggedInUserId The ID of the user performing the deactivation.
     * @return The number of rows affected by the update operation.
     * @throws InternalServerError If an error occurs while deactivating the document.
     */
    public Integer inActivateDocumentById(UUID documentId, UUID loggedInUserId) {
        try {
            return documentRepoDao.ctx()
                    .update(DOCUMENT_REPO)
                    .set(DOCUMENT_REPO.IS_ACTIVE, Boolean.FALSE)
                    .set(DOCUMENT_REPO.UPDATED_BY, loggedInUserId)
                    .set(DOCUMENT_REPO.UPDATED_ON, DateUtils.currentDatetime())
                    .where(DOCUMENT_REPO.ID.eq(documentId))
                    .execute();
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while de activating document by document id");
        }
    }

    public Boolean isDocumentExist(UUID documentId){
        try{
            return documentRepoDao.ctx().fetchExists(DSL.selectOne()
                    .from(DOCUMENT_REPO)
                    .where(DOCUMENT_REPO.ID.eq(documentId).and(DOCUMENT_REPO.IS_ACTIVE.eq(Boolean.TRUE))));
        }catch (Exception ex){
            throw new InternalServerError("Exception while checking document exist by document id");
        }
    }

    public List<DocumentDataDTO> getAllFiles(UUID documentCategory, UUID documentSubCategory, UUID orgId) {
        List<String> locationsList =
                documentRepoDao.ctx()
                        .select(DOCUMENT_REPO.PATH)
                        .from(DOCUMENT_REPO)
                        .where(DSL.field("DOCUMENT_REPO.sender ->> 'organisationId'",String.class).cast(UUID.class)
                                .eq(orgId)
                                .and(DOCUMENT_REPO.CATEGORY.eq(documentCategory))
                                .and(DOCUMENT_REPO.SUB_CATEGORY.eq(documentSubCategory)))
                        .fetchInto(String.class);

        FileSystem fileSystem = FileSystems.getDefault();


        log.info("size:: {}", locationsList.size());
        return locationsList.stream()
                .map(location -> {
                    try {
                        File file = fileStoreService.getFile(location);
                        byte[] fileContent = loadFile(file);
                        String encodedString = Base64.encodeBase64String(fileContent);

                        return DocumentDataDTO
                                .builder()
                                .fileName(fileSystem.getPath(location).getFileName().toString())
                                .fileData(encodedString)
                                .build();

                    } catch (IOException e) {
                        throw new RuntimeException(e.getMessage());
                    }
                })
                .collect(Collectors.toList());
    }

    public UUID getOrgIdByIndividualId(UUID individualId){
        try {
            return documentRepoDao.ctx().select(INDIVIDUAL_ROLE.ORG_ID)
                    .from(INDIVIDUAL_ROLE).where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(individualId)).fetchOneInto(UUID.class);
        }
        catch (Exception ex){
            throw new InternalServerError("Exception while fetching org id by individual id");
        }
    }


    public List<String> getAllDocumentPathByOrgIdCategoryAndSubcategory(
            UUID orgId, UUID documentCategory, UUID documentSubCategory, Boolean isActive){
        try{
            Condition isActiveCondition = DSL.noCondition();
            if(isActive != null && isActive){
                isActiveCondition = isActiveCondition.and(DOCUMENT_REPO.IS_ACTIVE.eq(Boolean.TRUE));
            }
            return documentRepoDao.ctx().select(DOCUMENT_REPO.PATH).from(DOCUMENT_REPO).where(
                    field("DOCUMENT_REPO.sender ->> 'organisationId'",String.class).cast(UUID.class).eq(orgId)
                    .and(DOCUMENT_REPO.CATEGORY.eq(documentCategory))
                    .and(DOCUMENT_REPO.SUB_CATEGORY.eq(documentSubCategory))
                    .and(isActiveCondition)
            ).fetchInto(String.class);
        }catch(Exception ex){
            throw new InternalServerError("Exception while fetching all document location by org id");
        }
    }

    public Boolean isDocumentExistById(UUID documentId) {
        try {
            return documentRepoDao.ctx()
                    .fetchExists(documentRepoDao.ctx()
                            .select()
                            .from(DOCUMENT_REPO)
                            .where(DOCUMENT_REPO.ID.eq(documentId).and(DOCUMENT_REPO.IS_ACTIVE.eq(Boolean.TRUE))));
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while checking document existence by document id");
        }
    }

    public Integer inActivateDocumentByFilePath(String filePath, UUID loggedInUserId) {
        try {
            return documentRepoDao.ctx()
                    .update(DOCUMENT_REPO)
                    .set(DOCUMENT_REPO.IS_ACTIVE, Boolean.FALSE)
                    .set(DOCUMENT_REPO.UPDATED_BY, loggedInUserId)
                    .set(DOCUMENT_REPO.UPDATED_ON, DateUtils.currentDatetime())
                    .where(DOCUMENT_REPO.PATH.eq(filePath))
                    .execute();
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while de activating document by document path");
        }
    }
}
