package com.chidhagni.donationreceipt.documentrepo.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
public class PaginationRequest {

    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer pageSize = 10;

    @Builder.Default
    private String documentNameFilter = "";

    @Builder.Default
    private String uploadedDocumentName="";

    @Builder.Default
    private List<UUID> documentCategoryFilter = new ArrayList<>();

    @Builder.Default
    private List<UUID> documentSubCategoryFilter = new ArrayList<>();


    @Builder.Default
    private String senderTypeFilter= null;

    @Builder.Default
    private List<UUID> senderNameFilter= null;

    @Builder.Default
    private List<UUID>  senderOrganisationFilter = null;

    @Builder.Default
    private String receiverTypeFilter = "";

    @Builder.Default
    private List<UUID> receiverNameFilter = new ArrayList<>();

    @Builder.Default
    private List<UUID> receiverOrganisationFilter = new ArrayList<>();

}
