package com.chidhagni.donationreceipt.documentrepo.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentRepoDetailsDTO {
    private UUID serviceNameId;
    private UUID userId;
    private UUID orgId;
    private UUID documentCategory;
    private UUID documentSubCategory;
    private String documentFrom;
    private String documentTo;
}
