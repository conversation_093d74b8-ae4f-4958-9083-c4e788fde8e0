package com.chidhagni.donationreceipt.documentrepo.dto.request;

import com.chidhagni.donationreceipt.organisation.constants.DefaultOrganisationCategoryEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class  participantDetailsDTO {
    private UUID individualId;
    private UUID organisationId;
    private DefaultOrganisationCategoryEnums type;
}
