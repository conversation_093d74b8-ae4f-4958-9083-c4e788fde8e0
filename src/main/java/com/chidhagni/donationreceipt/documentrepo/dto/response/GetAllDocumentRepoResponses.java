package com.chidhagni.donationreceipt.documentrepo.dto.response;

import com.chidhagni.donationreceipt.documentrepo.dto.request.PaginationRequest;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GetAllDocumentRepoResponses {

    private List<DocumentRepoResponses> documentRepoResponsesList;
    private PaginationRequest paginationRequest;
    private int rowCount;
}
