package com.chidhagni.donationreceipt.documentrepo.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentRepoDTO {
    @NotNull
    private UUID categoryId;
    private UUID subCategoryId;
    private  participantDetailsDTO senderDetails;
    private List< participantDetailsDTO> recipientDetails;
    private String path;
    private String remarks;
    private LocalDateTime fileDate;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;

    private TagsDTO tags;
}
