package com.chidhagni.donationreceipt.documentrepo.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocumentRepoResponses {
    private UUID id;
    private UUID category;
    private UUID subCategory;
    private String senderIndividualId;
    private String senderIndividualName;
    private UUID senderOrganisationId;
    private String senderOrganisationName;
    private String senderOrganisationCategory;

    private UUID receiverIndividualId;
    private String receiverIndividualName;
    private UUID receiverOrganisationId;
    private String receiverOrganisationName;
    private String receiverOrganisationCategory;

    private String path;
    private String remarks;
    private LocalDateTime fileDate;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String documentName;
}

