package com.chidhagni.donationreceipt.documentrepo;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.donationreceipt.documentrepo.dto.request.*;
import com.chidhagni.donationreceipt.documentrepo.dto.response.DocumentDataDTO;
import com.chidhagni.donationreceipt.documentrepo.dto.response.DocumentRepoResponses;
import com.chidhagni.donationreceipt.documentrepo.dto.response.GetAllDocumentRepoResponses;
import com.chidhagni.donationreceipt.documentrepo.utils.DocumentRepoMapper;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.resources.ResourceRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.filestore.service.IFileStoreService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo.DOCUMENT_REPO;


@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentRepoService {

    private final DocumentRepoRepository documentRepoRepository;

    private static final Integer pageSize = 10;
    private static final Integer pageNo = 1;

    private final IFileStoreService fileStoreService;
    private final ResourceRepository resourceRepository;
    private final IndividualRepository individualRepository;
    private final OrganizationRepository organizationRepository;
    private final ObjectMapper objectMapper;


    /**
     * Uploads a document file to the repository.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Validates recipient and sender details from the provided {@code DocumentRepoDTO}.</li>
     *   <li>Checks if the uploaded file is of an allowed type.</li>
     *   <li>Generates a unique document ID for storage.</li>
     *   <li>Delegates to  processFiles to handle the upload process.</li>
     *   <li>Returns the UUID of the successfully stored document.</li>
     * </ul>
     *
     * @param files           The file to be uploaded.
     * @param documentRepoDTO The metadata of the document to be stored.
     * @param userPrincipal   The authenticated user performing the upload.
     * @return The UUID of the uploaded document.
     * @throws IOException              If an error occurs while processing the file.
     * @throws IllegalArgumentException If file validation fails.
     */
    public UUID upload(MultipartFile files, DocumentRepoDTO documentRepoDTO, UserPrincipal userPrincipal) throws IOException {
        validateRecipientAndSenderDetails(documentRepoDTO);
        validateFileType(files);
        UUID documentRepoId = generateRandomUUID();
        return processFiles(files, documentRepoId, documentRepoDTO, userPrincipal);
    }


    /**
     * Processes the uploaded document file.
     *
     * <p>This method acts as a bridge between the file upload request and the actual storage process.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Calls uploadSingle to handle the file storage.</li>
     * </ul>
     *
     * @param file            The file being processed.
     * @param documentID      The UUID assigned to the document.
     * @param documentRepoDTO The metadata of the document.
     * @param userPrincipal   The authenticated user performing the upload.
     * @return The UUID of the processed document.
     * @throws IOException If an error occurs while handling the file.
     */
    public UUID processFiles(MultipartFile file, UUID documentID
            , DocumentRepoDTO documentRepoDTO, UserPrincipal userPrincipal) throws IOException {
        return uploadSingle(file, documentID, documentRepoDTO, userPrincipal);
    }

    /**
     * Handles the actual file upload and storage process.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Constructs the file storage path.</li>
     *   <li>Saves the file temporarily to a system directory.</li>
     *   <li>Transfers the file to the designated storage location.</li>
     *   <li>Maps the document metadata to a  DocumentRepo entity.</li>
     *   <li>Persists the document metadata in the repository.</li>
     *   <li>Returns the UUID of the stored document.</li>
     * </ul>
     *
     * @param file            The file being uploaded.
     * @param documentID      The UUID assigned to the document.
     * @param documentRepoDTO The metadata of the document.
     * @param userPrincipal   The authenticated user performing the upload.
     * @return The UUID of the uploaded document.
     * @throws IOException              If an error occurs while transferring or storing the file.
     * @throws IllegalArgumentException If the file location is null or empty.
     */
    public UUID uploadSingle(MultipartFile file, UUID documentID
            , DocumentRepoDTO documentRepoDTO, UserPrincipal userPrincipal) throws IOException {
        String folders = buildFolderPath(documentRepoDTO);
        String fileName = file.getOriginalFilename();
        log.info("file name :: {}", fileName);
        Path path = Path.of(System.getProperty("java.io.tmpdir"), fileName);
        file.transferTo(path);
        String filePath = folders + "/" + documentID;
        Path absolutePath = path.toAbsolutePath();
        String fileLocation = fileStoreService.storeFile(filePath, absolutePath);
        if (fileLocation == null || fileLocation.isEmpty()) {
            log.error("File location is null or empty for file: {}", path);
            throwIllegalArgumentException("File location is null or empty for file");
        }
        DocumentRepo documentRepo = DocumentRepoMapper.mapToEntity(documentID, documentRepoDTO, fileLocation, userPrincipal);
        documentRepoRepository.insertDocument(documentRepo);
        return documentRepo.getId();
    }

    public UUID generateRandomUUID() {
        return UUID.randomUUID();
    }


    /**
     * Validates the file type of the uploaded document.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Checks if the file is null and throws an exception if so.</li>
     *   <li>Ensures the file has a valid name.</li>
     *   <li>Extracts the file extension and verifies if it belongs to the list of allowed types.</li>
     *   <li>Throws an exception if the file type is not permitted.</li>
     * </ul>
     *
     * <p><b>Allowed File Types:</b></p>
     * <ul>
     *   <li>Images: `.jpg`, `.jpeg`, `.png`, `.gif`</li>
     *   <li>Documents: `.pdf`, `.docx`, `.xlsx`, `.txt`, `.csv`</li>
     * </ul>
     *
     * @param file The file being validated.
     * @throws IllegalArgumentException If the file is null, has no name, or is of an unsupported type.
     */
    private void validateFileType(MultipartFile file) {
        if (file == null) {
            throwIllegalArgumentException("Files could not be null");
        }
        List<String> allowedExtensions = List.of(".jpg", ".jpeg", ".png", ".gif", ".pdf", ".docx", ".xlsx", ".txt", ".csv", ".xls");
        String fileName = Objects.requireNonNull(file).getOriginalFilename();
        if (fileName == null || fileName.isBlank()) {
            throwIllegalArgumentException("Invalid file: missing file name");
        }
        String fileExtension = getFileExtension(Objects.requireNonNull(fileName));
        if (!allowedExtensions.contains(fileExtension)) {
            throwIllegalArgumentException(String.format(
                    "Invalid file type. Allowed types are: %s", String.join(", ", allowedExtensions)));
        }

    }


    private String getFileExtension(String fileName) {
        int lastIndexOfDot = fileName.lastIndexOf(".");
        if (lastIndexOfDot == -1) {
            throwIllegalArgumentException("Invalid file: missing file extension");
        }
        return fileName.substring(lastIndexOfDot).toLowerCase();
    }

    public void throwIllegalArgumentException(String msg) {
        throw new IllegalArgumentException(msg);
    }

    public void checkCategoryAndSubcategory(UUID id) {
        if (!resourceRepository.validResourceId(id)) {
            throwIllegalArgumentException("Invalid category or sub category type id");
        }
    }

    /**
     * Builds the folder path for storing the document based on its category, subcategory, and sender details.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Retrieves category ID, subcategory ID, and sender details from {@code DocumentRepoDTO}.</li>
     *   <li>Validates that the category and subcategory (if provided) exist.</li>
     *   <li>Constructs the folder path based on the sender’s organization and individual details.</li>
     *   <li>Formats the path in the structure: <br>
     *       {@code {organisationId}/{individualId?}/{categoryId}/{subCategoryId?} }</li>
     * </ul>
     *
     * @param documentRepoDTO The document metadata containing category, subcategory, and sender details.
     * @return A string representing the folder path where the document will be stored.
     * @throws IllegalArgumentException If the category or subcategory is invalid.
     */
    public String buildFolderPath(DocumentRepoDTO documentRepoDTO) {
        UUID categoryId = documentRepoDTO.getCategoryId();
        UUID subCategoryId = documentRepoDTO.getSubCategoryId();
        UUID individualId = documentRepoDTO.getSenderDetails().getIndividualId();
        checkCategoryAndSubcategory(categoryId);
        String folderPath = documentRepoDTO.getSenderDetails().getOrganisationId() + "";
        if (individualId != null) {
            folderPath += "/" + individualId;
        }
        folderPath += "/" + categoryId;
        if (subCategoryId != null) {
            checkCategoryAndSubcategory(subCategoryId);
            folderPath += "/" + subCategoryId;
        }
        return folderPath;
    }


    public void validateRecipientAndSenderDetails(DocumentRepoDTO documentRepoDTO) {
        validateSender(documentRepoDTO.getSenderDetails());
        List<participantDetailsDTO> participantDetailsDTOS = validateRecipients(documentRepoDTO.getRecipientDetails());
        documentRepoDTO.setRecipientDetails(participantDetailsDTOS);
    }

    public void validateSender(participantDetailsDTO senderDetails) {
        if ((senderDetails.getIndividualId() != null) && !individualRepository
                .isIndividualExist(senderDetails.getIndividualId())) {
            throwIllegalArgumentException("An existing user is not available with the given id");
        }
        if (!organizationRepository.isOrganisationExist(senderDetails.getOrganisationId())) {
            throwIllegalArgumentException("An existing organisation is not available with the given id");
        }
    }

    public List<participantDetailsDTO> validateRecipients(List<participantDetailsDTO> recipientDetails) {
        if (recipientDetails == null || recipientDetails.isEmpty()) {
            log.info("Recipient details are null or empty, skipping validation.");
            return Collections.emptyList();
        }
        return recipientDetails.stream().filter(this::validateRecipient).collect(Collectors.toList());
    }

    public boolean validateRecipient(participantDetailsDTO senderDetails) {
        UUID individualId = senderDetails.getIndividualId();
        UUID organisationId = senderDetails.getOrganisationId();
        if ((individualId != null) && !individualRepository.isIndividualExist(individualId)) {
            log.info("An valid user not found with the id :: {} so, not added to recipients list", individualId);
            return false;
        }
        if (!organizationRepository.isOrganisationExist(organisationId)) {
            log.info("An valid organisation not found with the id :: {} so, not added to recipients list", organisationId);
            return false;
        }
        return true;
    }

    public GetDocumentRepoResponses getById(UUID id) {
        DocumentRepo documentRepo = documentRepoRepository.getById(id);
        return DocumentRepoMapper.mapDocumentRepoDTOToDocumentRepo(documentRepo);
    }


    /**
     * Retrieves a paginated list of all document repository responses for the admin.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Applies default pagination settings if not provided.</li>
     *   <li>Generates the search condition for filtering results.</li>
     *   <li>Initializes sorting fields for the query.</li>
     *   <li>Fetches the list of document repository responses from the repository.</li>
     *   <li>Retrieves the total count of matching documents for pagination.</li>
     *   <li>Builds and returns the paginated response.</li>
     * </ul>
     *
     * <p><b>Request Parameters:</b></p>
     * <ul>
     *   <li>`paginationRequest` (Optional) - Contains pagination details such as page number and size.</li>
     * </ul>
     *
     * @param paginationRequest The pagination request containing filtering and sorting details.
     * @return A `GetAllDocumentRepoResponses` object containing the list of document responses and pagination details.
     * @throws IllegalArgumentException If the pagination parameters are invalid.
     */
    public GetAllDocumentRepoResponses getAllDocumentRepoResponsesAdmin(PaginationRequest paginationRequest) {
        applyDefaults(paginationRequest);
        Condition finalCondition = getSearchingCondition(paginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();
        getSortingCondition(sortFields);
        List<DocumentRepoResponses> documentRepoResponsesList = documentRepoRepository.getAllDocumentRepoResponsesAdmin(finalCondition, sortFields, paginationRequest);
        Integer count = documentRepoRepository.getDocumentRepoResponsesCountAdmin(finalCondition);
        return GetAllDocumentRepoResponses.builder()
                .rowCount(count)
                .documentRepoResponsesList(documentRepoResponsesList)
                .paginationRequest(paginationRequest)
                .build();
    }


    /**
     * Retrieves a paginated list of document repository responses for the Society,Service Provider,Employee
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Applies default pagination settings if none are provided.</li>
     *   <li>Generates a search condition based on the pagination request.</li>
     *   <li>Initializes and applies sorting conditions.</li>
     *   <li>Fetches the list of document repository responses from the repository.</li>
     *   <li>Retrieves the total count of matching documents for pagination.</li>
     *   <li>Builds and returns a paginated response.</li>
     * </ul>
     *
     * @param paginationRequest The pagination request containing filtering and sorting details.
     * @param userPrincipal     The authenticated user making the request.
     * @return A `GetAllDocumentRepoResponses` object containing the list of document responses and pagination details.
     * @throws IllegalArgumentException If the pagination parameters are invalid.
     */
    public GetAllDocumentRepoResponses getAllDocumentRepoResponses(PaginationRequest paginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = getSearchingCondition(paginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();
        getSortingCondition(sortFields);
        List<DocumentRepoResponses> documentRepoResponsesList = documentRepoRepository.getAllDocumentRepoResponses(finalCondition, sortFields, paginationRequest, userPrincipal);
        Integer count = documentRepoRepository.getDocumentRepoResponsesCount(finalCondition);


        return GetAllDocumentRepoResponses.builder()
                .rowCount(count)
                .documentRepoResponsesList(documentRepoResponsesList)
                .paginationRequest(paginationRequest)
                .build();
    }

    /**
     * Applies default pagination values if not provided.
     */
    public void applyDefaults(PaginationRequest paginationRequest) {
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }

        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
    }

    /**
     * Builds a search condition based on provided filters.
     */
    public Condition getSearchingCondition(PaginationRequest paginationRequest) {

        String searchByDocumentName = paginationRequest.getDocumentNameFilter();
        String searchByUploadedDocumentName = paginationRequest.getUploadedDocumentName();


        List<Condition> conditions = new ArrayList<>();

        if (searchByDocumentName != null && !searchByDocumentName.isEmpty()) {
            conditions.add(DSL.condition("tags->>'documentName' ILIKE ?", "%" + searchByDocumentName + "%"));
        }

        if (searchByUploadedDocumentName != null && !searchByUploadedDocumentName.isEmpty()) {
            conditions.add(DSL.condition(
                    "regexp_replace(path, '^.*/', '') ILIKE ?",
                    "%" + searchByUploadedDocumentName + "%"
            ));
        }

        if (paginationRequest.getDocumentCategoryFilter() != null && !paginationRequest.getDocumentCategoryFilter().isEmpty()) {
            Condition documentCategoryCondition = DSL.noCondition();
            for (UUID categoryId : paginationRequest.getDocumentCategoryFilter()) {
                documentCategoryCondition = documentCategoryCondition.or(DOCUMENT_REPO.CATEGORY.eq(categoryId));
            }
            conditions.add(documentCategoryCondition);
        }

        if (paginationRequest.getDocumentSubCategoryFilter() != null && !paginationRequest.getDocumentSubCategoryFilter().isEmpty()) {
            Condition documentSubCategoryCondition = DSL.noCondition();
            for (UUID subCategoryId : paginationRequest.getDocumentSubCategoryFilter()) {
                documentSubCategoryCondition = documentSubCategoryCondition.or(DOCUMENT_REPO.SUB_CATEGORY.eq(subCategoryId));
            }
            conditions.add(documentSubCategoryCondition);
        }


        if (paginationRequest.getSenderNameFilter() != null && !paginationRequest.getSenderNameFilter().isEmpty()) {
            Condition senderNameCondition = DSL.noCondition();
            for (UUID senderId : paginationRequest.getSenderNameFilter()) {
                senderNameCondition = senderNameCondition.or(
                        DSL.field("sender->>'individualId'", SQLDataType.VARCHAR).eq(senderId.toString())
                );
            }
            conditions.add(senderNameCondition);
        }


        if (paginationRequest.getSenderOrganisationFilter() != null && !paginationRequest.getSenderOrganisationFilter().isEmpty()) {
            Condition senderOrganisationCondition = DSL.noCondition();
            for (UUID organisationId : paginationRequest.getSenderOrganisationFilter()) {
                senderOrganisationCondition = senderOrganisationCondition.or(
                        DSL.field("sender->>'organisationId'", SQLDataType.VARCHAR).eq(organisationId.toString())
                );
            }
            conditions.add(senderOrganisationCondition);
        }
        if (paginationRequest.getSenderTypeFilter() != null && !paginationRequest.getSenderTypeFilter().isEmpty()) {
            conditions.add(
                    DSL.field("sender->>'type'", SQLDataType.VARCHAR).eq(paginationRequest.getSenderTypeFilter())
            );
        }


        if (paginationRequest.getReceiverTypeFilter() != null && !paginationRequest.getReceiverTypeFilter().isEmpty()) {
            conditions.add(
                    DSL.condition("recipients @> CAST(? AS JSONB)", DSL.val("[{\"type\": \"" + paginationRequest.getReceiverTypeFilter() + "\"}]"))
            );
        }

        if (paginationRequest.getReceiverNameFilter() != null && !paginationRequest.getReceiverNameFilter().isEmpty()) {
            Condition receiverNameCondition = DSL.noCondition();
            for (UUID receiverName : paginationRequest.getReceiverNameFilter()) {
                receiverNameCondition = receiverNameCondition.or(
                        DSL.condition("recipients @> CAST(? AS JSONB)", DSL.val("[{\"individualId\": \"" + receiverName.toString() + "\"}]"))
                );
            }
            conditions.add(receiverNameCondition);
        }


        if (paginationRequest.getReceiverOrganisationFilter() != null && !paginationRequest.getReceiverOrganisationFilter().isEmpty()) {
            Condition receiverOrganisationCondition = DSL.noCondition();
            for (UUID receiverOrganisation : paginationRequest.getReceiverOrganisationFilter()) {
                receiverOrganisationCondition = receiverOrganisationCondition.or(
                        DSL.condition("recipients @> CAST(? AS JSONB)", DSL.val("[{\"organisationId\": \"" + receiverOrganisation.toString() + "\"}]"))
                );
            }
            conditions.add(receiverOrganisationCondition);
        }


        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }

    /**
     * Updates an existing document in the repository.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Validates recipient and sender details from the provided  DocumentRepoDTO.</li>
     *   <li>Checks if the uploaded file is of an allowed type.</li>
     *   <li>Retrieves the existing document entity from the repository.</li>
     *   <li>Throws an exception if no document is found with the given ID.</li>
     *   <li>Uploads or updates the file in the storage system.</li>
     *   <li>Maps the updated document details and file location to the existing entity.</li>
     *   <li>Persists the updated document entity in the repository.</li>
     *   <li>Returns the UUID of the updated document.</li>
     * </ul>
     *
     * @param id              The UUID of the document to be updated.
     * @param file            The new file associated with the document.
     * @param documentRepoDTO The updated document metadata.
     * @param userPrincipal   The authenticated user performing the update.
     * @return The UUID of the updated document.
     * @throws IOException              If an error occurs while processing the file.
     * @throws IllegalArgumentException If the document does not exist or the file is invalid.
     */
    public UUID updateDocumentsRepo(UUID id, MultipartFile file, DocumentRepoDTO documentRepoDTO, UserPrincipal userPrincipal) throws IOException {
        validateRecipientAndSenderDetails(documentRepoDTO);
        validateFileType(file);
        DocumentRepo documentEntity = documentRepoRepository.getDocumentByID(id);
        if (documentEntity == null) {
            throw new IllegalArgumentException("A document not available with the given id");
        }
        String fileLocation = uploadOrUpdateFile(id, file, documentRepoDTO);
        documentEntity = DocumentRepoMapper.toDocumentRepo(documentEntity, documentRepoDTO, fileLocation, userPrincipal.getId());
        documentRepoRepository.updateDocument(documentEntity);
        return documentEntity.getId();
    }

    /**
     * Uploads or updates the file associated with an existing document.
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Constructs the folder path where the file will be stored.</li>
     *   <li>Transfers the file to a temporary location.</li>
     *   <li>Determines the final storage path for the file.</li>
     *   <li>Uploads the file to the storage system.</li>
     *   <li>Validates that the file location is not null or empty.</li>
     *   <li>Returns the file location path.</li>
     * </ul>
     *
     * @param documentID      The UUID of the document being updated.
     * @param file            The new file to be uploaded or updated.
     * @param documentRepoDTO The document metadata containing category and sender details.
     * @return The file location path in the storage system.
     * @throws IOException              If an error occurs while handling the file transfer.
     * @throws IllegalArgumentException If the file location is null or empty.
     */
    public String uploadOrUpdateFile(UUID documentID, MultipartFile file
            , DocumentRepoDTO documentRepoDTO) throws IOException {
        String folders = buildFolderPath(documentRepoDTO);
        String fileName = file.getOriginalFilename();
        log.info("file name is:: {}", fileName);
        Path path = Path.of(System.getProperty("java.io.tmpdir"), fileName);
        file.transferTo(path);
        String filePath = folders + "/" + documentID;
        Path absolutePath = path.toAbsolutePath();
        String fileLocation = fileStoreService.storeFile(filePath, absolutePath);
        if (fileLocation == null || fileLocation.isEmpty()) {
            log.error("File location is null or empty for the file: {}", path);
            throwIllegalArgumentException("File location is null or empty for file");
        }
        return fileLocation;
    }

    public void getSortingCondition(List<SortField<?>> sortFields) {
        sortFields.add(DOCUMENT_REPO.CREATED_ON.desc());
    }

    public DocumentRepo getDocumentRepoById(UUID id) {
        DocumentRepo documentRepoById = documentRepoRepository.getById(id);
        if (documentRepoById == null) {
            log.info("A document is not available with the given document id :: {}", id);
            return null;
        }
        return documentRepoById;
    }

    public void deactivateDocumentByDocumentId(UUID documentId, UserPrincipal userPrincipal) {
        if (!documentRepoRepository.isDocumentExist(documentId)) {
            log.info("given document id to de-activated already deleted");
        }
        documentRepoRepository.inActivateDocumentById(documentId, userPrincipal.getId());
    }

    public List<DocumentDataDTO> getAllFiles(UUID documentCategory, UUID documentSubCategory, UUID individualId) {
        UUID orgId = documentRepoRepository.getOrgIdByIndividualId(individualId);
        if (orgId == null) {
            throw new IllegalArgumentException(String.format("Organisation Id not found for individual id :: %s", individualId));
        }
        return documentRepoRepository.getAllFiles(documentCategory, documentSubCategory, orgId);
    }



    public List<String> getAllDocumentPathByOrgId(DocumentRepoDetailsDTO documentRepoDetailsDTO, Boolean isActive, UserPrincipal userPrincipal) {
        UUID orgId = documentRepoDetailsDTO.getOrgId();
        UUID documentCategoryId = documentRepoDetailsDTO.getDocumentCategory();
        UUID documentSubCategoryId = documentRepoDetailsDTO.getDocumentSubCategory();
        if (documentCategoryId == null || documentSubCategoryId == null) {
            throw new IllegalArgumentException("either document category or sub-category is null request body");
        }
        if (orgId == null) {
            //orgId = organizationRepository.fetchClientOrganisationId();
        }
        return documentRepoRepository.getAllDocumentPathByOrgIdCategoryAndSubcategory(
                orgId, documentCategoryId, documentSubCategoryId, isActive);
    }

    public List<UUID> uploadMultipleFiles(List<MultipartFile> files, DocumentRepoDTO documentRepoDTO, UserPrincipal userPrincipal) {
        if (files == null || CollectionUtils.isEmpty(files)) {
            throwIllegalArgumentException("Files could not be null");
        }
        return files.stream()
                .filter(Objects::nonNull)
                .map(file -> {
                    try {
                        return upload(file, documentRepoDTO, userPrincipal);
                    } catch (IOException e) {
                        log.warn("Failed to upload the image :: {} with error as :: {}", file.getOriginalFilename(), e.getMessage());
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<String> getByDocumentRepoIdList(List<UUID> documentsIds) {
        return documentsIds.stream()
                .map(documentRepoRepository::getDocumentByID)
                .filter(Objects::nonNull)
                .map(DocumentRepo::getPath)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public Integer deleteDocument(UUID id, UserPrincipal userPrincipal) {
        if (!documentRepoRepository.isDocumentExistById(id)) {
            log.info("Document not found with the given id :: {}", id);
        }
        Integer inactivatedDocumentCount = documentRepoRepository.inActivateDocumentById(id, userPrincipal.getId());
        if (inactivatedDocumentCount == 0) {
            log.info("Document not inactivated with the given id :: {}", id);
        }
        return inactivatedDocumentCount;
    }

    public Integer deleteDocumentByFilePath(String filePath, UserPrincipal userPrincipal) {
        Integer inactivatedDocumentCount
                = documentRepoRepository.inActivateDocumentByFilePath(filePath, userPrincipal.getId());
        if (inactivatedDocumentCount == 0) {
            log.info("Document not inactivated with the given path :: {}", filePath);
        }
        return inactivatedDocumentCount;
    }

}
