package com.chidhagni.donationreceipt.documentrepo.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.donationreceipt.documentrepo.dto.request.DocumentRepoDTO;
import com.chidhagni.donationreceipt.documentrepo.dto.request.GetDocumentRepoResponses;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Slf4j
@Component
public class DocumentRepoMapper {

    private DocumentRepoMapper() {
    }

    public static DocumentRepo toDocumentRepo(DocumentRepo documentRepo, DocumentRepoDTO documentRepoDTO, String fileLocation, UUID loggedInUserId) {
        documentRepo.setCategory(documentRepoDTO.getCategoryId());
        documentRepo.setSubCategory(documentRepoDTO.getSubCategoryId());
        documentRepo.setSender(documentRepoDTO.getSenderDetails());
        documentRepo.setRecipients(documentRepoDTO.getRecipientDetails());
        documentRepo.setPath(fileLocation);
        documentRepo.setRemarks(documentRepoDTO.getRemarks());
        documentRepo.setUpdatedBy(loggedInUserId);
        documentRepo.setUpdatedOn(DateUtils.currentDatetime());
        documentRepo.setFileDate(documentRepoDTO.getFileDate());
        documentRepo.setTags(documentRepoDTO.getTags());
        return documentRepo;
    }

    public static DocumentRepo mapToEntity(UUID documentRepoId, DocumentRepoDTO documentRepoDTO, String filePath, UserPrincipal userPrincipal) {
        DocumentRepo documentRepo = new DocumentRepo();
        documentRepo.setId(documentRepoId);
        documentRepo.setCategory(documentRepoDTO.getCategoryId());
        documentRepo.setSubCategory(documentRepoDTO.getSubCategoryId());
        documentRepo.setSender(documentRepoDTO.getSenderDetails());
        documentRepo.setRecipients(documentRepoDTO.getRecipientDetails());
        documentRepo.setPath(filePath);
        documentRepo.setRemarks(documentRepoDTO.getRemarks());
        documentRepo.setFileDate(documentRepoDTO.getFileDate());
        documentRepo.setCreatedBy(userPrincipal.getId());
        documentRepo.setCreatedOn(DateUtils.currentDatetime());
        documentRepo.setTags(documentRepoDTO.getTags());
        documentRepo.setIsActive(Boolean.TRUE);
        return documentRepo;
    }


    public static GetDocumentRepoResponses mapDocumentRepoDTOToDocumentRepo(DocumentRepo documentRepo) {
        String fileName = extractFileName(documentRepo.getPath());
        return GetDocumentRepoResponses.builder()
                .id(documentRepo.getId())
                .categoryId(documentRepo.getCategory())
                .subCategoryId(documentRepo.getSubCategory())
                .recipientDetails(documentRepo.getRecipients())
                .senderDetails((documentRepo.getSender()))
                .path(documentRepo.getPath())
                .remarks(documentRepo.getRemarks())
                .fileDate(documentRepo.getFileDate())
                .createdOn(documentRepo.getCreatedOn())
                .createdBy(documentRepo.getCreatedBy())
                .updatedBy(documentRepo.getUpdatedBy())
                .updatedOn(documentRepo.getUpdatedOn())
                .fileName(fileName)
                .tags(documentRepo.getTags())
                .build();
    }


    private static String extractFileName(String path) {
        if (path == null || path.isEmpty()) {
            return null;
        }
        String[] parts = path.split("/");
        return parts[parts.length - 1];
    }
}
