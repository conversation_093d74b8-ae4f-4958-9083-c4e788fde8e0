package com.chidhagni.donationreceipt.documentrepo.jooq;

import com.chidhagni.donationreceipt.documentrepo.dto.request.participantDetailsDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;

import jakarta.validation.constraints.NotNull;

public class docSenderMetaDataJsonConverter extends BaseJsonBJooqConverter< participantDetailsDTO> {

    @Override
    public @NotNull Class<participantDetailsDTO> toType() {
        return  participantDetailsDTO.class;
    }
}
