package com.chidhagni.donationreceipt.documentrepo.jooq;

import com.chidhagni.donationreceipt.documentrepo.dto.request.participantDetailsDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;

import jakarta.validation.constraints.NotNull;
import java.util.List;

public class docReceiverMetaDataJsonConverter extends BaseJsonBJooqConverter<List< participantDetailsDTO>> {
    @Override
    public @NotNull Class<List<participantDetailsDTO>> toType() {
        return (Class<List< participantDetailsDTO>>) (Class<?>) List.class;
    }
}
