package com.chidhagni.donationreceipt;

import com.chidhagni.config.SecurityConfig;

import com.chidhagni.config.SwaggerConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableConfigurationProperties
@SpringBootApplication(scanBasePackages =
		{"com.chidhagni.donationreceipt", "com.chidhagni.config", "com.chidhagni.web", "com.chidhagni.utils",
				"com.chidhagni.service", "com.chidhagni.filestore"})
@Import({ SwaggerConfig.class,SecurityConfig.class})
@EnableScheduling
public class DonationReceiptApplication {

	public static void main(String[] args) {
		SpringApplication.run(DonationReceiptApplication.class, args);
	}
}

