package com.chidhagni.donationreceipt.individualrole;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.Tables.DONATION_HEADS;
import static com.chidhagni.donationreceipt.db.jooq.Tables.DONATION_RECEIPTS;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;

import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;

@Slf4j
@Repository
@RequiredArgsConstructor
public class IndividualRoleRepository {

   private final IndividualRoleDao individualRoleDao;

   private final DSLContext dslContext;
    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;
//
//
//    /**
//     * Retrieves a list of IndividualRole associated with a given individual ID.
//     *
//     * @param individualId The unique identifier of the individual.
//     * @return A list of  IndividualRole objects associated with the provided individual ID.
//     *         Returns null if an exception occurs or no roles are found.
//     * @throws InternalServerError if an exception occurs while fetching data from the database.
//     */
    public List<IndividualRole> getByIndivdiualId(UUID individualId) {
        List<IndividualRole> individualRole = null;
        try {
            individualRole = individualRoleDao.fetchByIndividualId(individualId);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching ListValues by it's id", ex);
        }
        return individualRole;
    }

//
//    /**
//     * Retrieves a list of individual IDs associated with the given list of organization IDs.
//     *
//     * @param organisationIds A list of unique organization identifiers.
//     * @return A list of UUID representing individual IDs associated with the given organization IDs.
//     * @throws InternalServerError if an exception occurs while fetching the data.
//     */
//    public List<UUID> getAllIndividualIds(List<UUID> organisationIds) {
//        try {
//            return individualRoleDao.ctx().select(INDIVIDUAL_ROLE.INDIVIDUAL_ID).from(INDIVIDUAL_ROLE)
//                    .where(INDIVIDUAL_ROLE.ORG_ID.in(organisationIds)).fetchInto(UUID.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individual id's by organisation id's");
//        }
//    }
//
//    /**
//     * Retrieves the organization ID associated with a given individual ID.
//     *
//     * @param individualId The id of the individual.
//     * @return A  UUID representing the organization ID associated with the given individual ID.
//     * @throws IllegalArgumentException if an exception occurs while fetching the data.
//     */
    public UUID getOrgIdByIndividualId(UUID individualId) {
        try {
            return individualRoleDao.ctx().select(INDIVIDUAL_ROLE.ORG_ID).from(INDIVIDUAL_ROLE)
                    .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(individualId)).fetchOneInto(UUID.class);
        } catch (Exception ex) {
            throw new IllegalArgumentException(String.format("Exception occurred while fetching the organisation id " +
                    "by individual id given is :: [id=%s]", individualId));
        }
    }
//
//    /**
//     * Inserts or updates a list of individual roles.
//     *
//     * @param individualRoles A list of IndividualRole objects to be inserted or updated.
//     * @throws InternalServerError if an exception occurs while performing the operation.
//     */
    public void upsertIndividualRoles(List<IndividualRole> individualRoles) {
        try {
            individualRoleDao.merge(individualRoles);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while upsert individual roles", ex);
        }
    }
//
//    /**
//     * Retrieves the individual ID associated with a given organization ID.
//     *
//     * @param orgId The unique identifier of the organization.
//     * @return A  UUID representing the individual ID associated with the given organization ID.
//     * @throws InternalServerError if an exception occurs while fetching the data.
//     */
    public List<IndividualRole> getIndividualIdByOrganisationId(UUID orgId){
        try {
            return individualRoleDao.ctx().select().from(INDIVIDUAL_ROLE)
                    .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId)).fetchInto(IndividualRole.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching individual id by organisation id", ex);
        }
    }

    public void insertIndividualRole(IndividualRole individualRole){
        try{
            individualRoleDao.insert(individualRole);
            log.info("successfully insert the individual role with primary id :: {}", individualRole.getId());
        }catch(Exception ex){
            throw new InternalServerError("Exception while inserting the individual role", ex);
        }
    }

    public IndividualRole findByOrganisationIdAndRoleId(UUID orgId, UUID tenantAdminRoleId) {
        return individualRoleDao.ctx().select().from(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId))
                .and(INDIVIDUAL_ROLE.ROLE_ID.eq(tenantAdminRoleId))
                .fetchOneInto((IndividualRole.class));
    }
//
//    public UUID getRoleByIndividualId(UUID individualId){
//        try{
//            return individualRoleDao.ctx().select(INDIVIDUAL_ROLE.ROLE_ID).from(INDIVIDUAL_ROLE)
//                    .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(individualId)).limit(1).fetchOneInto(UUID.class);
//        }catch(Exception ex){
//            throw new InternalServerError("Exception while fetching role by individual id", ex);
//        }
//    }

    public Condition applyOrganizationFilterDonationHead(UUID individualId, Condition baseCondition) {
        // Fetch the user's role and organization
        IndividualRole individualRole = dslContext.selectFrom(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(individualId))
                .limit(1)
                .fetchOneInto(IndividualRole.class);

        if (individualRole == null) {
            throw new RuntimeException("Individual role not found for user");
        }

        Organisation organisation = dslContext.selectFrom(ORGANISATION)
                .where(ORGANISATION.ID.eq(individualRole.getOrgId()))
                .fetchOneInto(Organisation.class);

        if (organisation == null) {
            throw new RuntimeException("Organization not found for user");
        }

        // Apply organization filter based on category
        if (OrganisationEnums.SUPER_ADMIN.name().equals(organisation.getCategory())) {
            // No additional condition for SUPER_ADMIN
            return baseCondition;
        } else if (OrganisationEnums.TENANT.name().equals(organisation.getCategory())) {
            // Filter by organization ID for TENANT
            return baseCondition.and(DONATION_HEADS.ORG_ID.eq(individualRole.getOrgId()));
        } else {
            throw new RuntimeException("Invalid organization category: " + organisation.getCategory());
        }
    }

    public Condition applyOrganizationFilterDonationReceipt(UUID individualId, Condition baseCondition) {
        // Fetch the user's role and organization
        IndividualRole individualRole = dslContext.selectFrom(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(individualId))
                .limit(1)
                .fetchOneInto(IndividualRole.class);

        if (individualRole == null) {
            throw new RuntimeException("Individual role not found for user");
        }

        Organisation organisation = dslContext.selectFrom(ORGANISATION)
                .where(ORGANISATION.ID.eq(individualRole.getOrgId()))
                .fetchOneInto(Organisation.class);

        if (organisation == null) {
            throw new RuntimeException("Organization not found for user");
        }

        // Apply organization filter based on category
        if (OrganisationEnums.SUPER_ADMIN.name().equals(organisation.getCategory())) {
            // No additional condition for SUPER_ADMIN
            return baseCondition;
        } else if (OrganisationEnums.TENANT.name().equals(organisation.getCategory())) {
            // Filter by organization ID for TENANT
            return baseCondition.and(DONATION_RECEIPTS.TENANT_ORG_ID.eq(individualRole.getOrgId()));
        } else {
            throw new RuntimeException("Invalid organization category: " + organisation.getCategory());
        }
    }

    public List<IndividualRole> getTenantAdminOfOrganisation(UUID id) {

        return individualRoleDao.ctx().select()
                .from(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.ORG_ID.eq(id).and(INDIVIDUAL_ROLE.ROLE_ID.eq(tenantAdminRoleId)))
                .fetchInto(IndividualRole.class);
    }
}

