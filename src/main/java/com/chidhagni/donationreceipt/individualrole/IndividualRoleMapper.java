package com.chidhagni.donationreceipt.individualrole;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@RequiredArgsConstructor
public class IndividualRoleMapper {


//    private final IndividualRoleDao individualRoleDao;
//
//    public List<IndividualRole> mapToIndividualRoles(List<IndividualRoleDTO> individualRoleDTOList) {
//        return individualRoleDTOList.stream()
//                .filter(individualRoleDTO -> individualRoleDTO.getIndividualId() != null
//                        && individualRoleDTO.getRolesId() != null)
//                .map(this::mapIndividualRoleDTO)
//                .collect(Collectors.toList());
//    }
//
//    private IndividualRole mapIndividualRoleDTO(IndividualRoleDTO individualRoleDTO) {
//        UUID individualId = individualRoleDTO.getIndividualId();
//        UUID rolesId = individualRoleDTO.getRolesId();
//        UUID orgId = individualRoleDTO.getOrgId();
//
//        List<IndividualRole> existingIndividualId = individualRoleDao.fetchByIndividualId(individualId);
//
//        // Create IndividualRole instance
//        IndividualRole individualRole = new IndividualRole();
//        if (existingIndividualId.isEmpty()) {
//            individualRole.setId(UUID.randomUUID());
//        } else {
//            individualRole.setId(existingIndividualId.get(0).getId());
//        }
//
//        individualRole.setIndividualId(individualId);
//        individualRole.setRoleId(rolesId);
//
//        if (orgId != null) {
//            individualRole.setOrgId(orgId);
//        }
//
//        individualRole.setCreatedOn(LocalDateTime.now());
//        individualRole.setUpdatedOn(LocalDateTime.now());
//        individualRole.setCreatedBy(UUID.randomUUID());
//        individualRole.setUpdatedBy(UUID.randomUUID());
//
//        log.info("Mapped IndividualRole: {}", individualRole);
//        return individualRole;
//    }




}

