package com.chidhagni.donationreceipt.individualrole;


import com.chidhagni.donationreceipt.individualrole.util.IndividualRoleMapper;
import com.chidhagni.donationreceipt.roles.RolesRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndividualRoleService {

    @Value("${service.provider.roles.id}")
    private UUID serviceProviderId;
    @Value("${society.roles.id}")
    private UUID societyId;
    @Value("${employee.roles.id}")
    private UUID employeeId;
    private final IndividualRoleMapper individualRoleMapper;
    private final RolesRepository rolesRepository;
    private final IndividualRoleRepository individualRoleRepository;

//    public IndividualRole createIndividualRole(UUID individualId, UUID organisationId
//            , DefaultOrganisationCategoryEnums role) throws JsonProcessingException {
//        UUID roleId = role.equals(DefaultOrganisationCategoryEnums.SERVICE_PROVIDER)
//                ? serviceProviderId : role.equals(DefaultOrganisationCategoryEnums.EMPLOYEE)? employeeId : societyId;
//        return individualRoleMapper.createIndividualRole(individualId, roleId, organisationId);
//    }
//
//    public IndividualRole createIndividualRoleByRoleId(UUID individualId, UUID organisationId
//            , UUID roleId) throws JsonProcessingException {
//        if(!rolesRepository.isRoleExist(roleId)){
//            throw new IllegalArgumentException(String.format("A role with given role id :: %s not present in roles"
//                    , roleId));
//        }
//        return individualRoleMapper.createIndividualRole(individualId, roleId, organisationId);
//    }
//
//    public void updateIndividualRole(UUID individualId, UUID roleId, UserPrincipal userPrincipal){
//        UUID loggedInUserId = userPrincipal.getId();
//        if(!rolesRepository.isRoleExist(roleId)){
//            throw new IllegalArgumentException(String.format("A role with given role id :: %s not present in roles"
//                    , roleId));
//        }
//        IndividualRole individualRole = getIndividualRoleByIndividualId(individualId);
//        individualRoleMapper.updateIndividualRole(roleId, individualRole, loggedInUserId);
//        individualRoleRepository.upsertIndividualRoles(List.of(individualRole));
//    }
//
//    public Boolean checkIfRoleChanged(UUID individualId, UUID roleId){
//        if(!rolesRepository.isRoleExist(roleId)){
//            throw new IllegalArgumentException(String.format("A role with given role id :: %s not present in roles"
//                    , roleId));
//        }
//        IndividualRole individualRole = getIndividualRoleByIndividualId(individualId);
//        UUID existingRoleId = individualRole.getRoleId();
//        if(existingRoleId.compareTo(roleId) == 0){
//            log.info("Existing role id id having the provided role, so no need to update");
//            return Boolean.FALSE;
//        }
//        return Boolean.TRUE;
//    }
//
//    public IndividualRole getIndividualRoleByIndividualId(UUID individualId){
//        List<IndividualRole>  individualRoleList = individualRoleRepository.getByIndivdiualId(individualId);
//        if(individualRoleList == null || CollectionUtils.isEmpty(individualRoleList)){
//            throw new IllegalArgumentException(String.format(
//                    "No individual roles present with the given individual id :: %s", individualId));
//        }
//        return individualRoleList.get(0);
//    }

}
