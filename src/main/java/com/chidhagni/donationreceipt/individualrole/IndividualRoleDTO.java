package com.chidhagni.donationreceipt.individualrole;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IndividualRoleDTO {
    private UUID individualId;
    private UUID userId;
    private UUID roleId;
    private UUID rolesId;
    private UUID orgId;


}
