package com.chidhagni.donationreceipt.donationhead.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.UUID;


@Data
@Builder
@AllArgsConstructor
public class DonationHeadPaginationRequest {

    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 10;
    @Builder.Default
    private String nameFilter = "";
    @Builder.Default
    private String descriptionFilter = "";
    @Builder.Default
    private UUID orgIdFilter = null;

    private String searchKeyWord;
}
