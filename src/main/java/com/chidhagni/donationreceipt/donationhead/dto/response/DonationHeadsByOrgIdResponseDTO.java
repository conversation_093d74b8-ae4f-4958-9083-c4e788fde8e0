package com.chidhagni.donationreceipt.donationhead.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationHeadsByOrgIdResponseDTO {
    private List<DonationHeadSummary> donationHeads;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DonationHeadSummary {
        private UUID id;
        private String name;
    }
}
