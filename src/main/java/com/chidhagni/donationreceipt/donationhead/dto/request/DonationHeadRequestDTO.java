package com.chidhagni.donationreceipt.donationhead.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationHeadRequestDTO {
    @NotNull(message = "orgId is mandatory")
    private UUID orgId;
    @NotNull(message = "Donation Head Name is mandatory")
    private String name;
    private String description;
}
