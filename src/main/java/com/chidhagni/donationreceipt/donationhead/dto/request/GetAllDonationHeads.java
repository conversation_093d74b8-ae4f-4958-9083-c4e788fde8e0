package com.chidhagni.donationreceipt.donationhead.dto.request;

import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAllDonationHeads {
    private List<DonationHeadResponseDTO> donationHeads;
    private int rowCount;
}
