package com.chidhagni.donationreceipt.donationhead.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationHeadResponseDTO {
    private UUID id;
    private String name;
    private String description;
    private UUID orgId;
    private LocalDateTime createdOn;
    private UUID createdBy;
    private LocalDateTime updatedOn;
    private UUID updatedBy;
    private Boolean isActive;
}
