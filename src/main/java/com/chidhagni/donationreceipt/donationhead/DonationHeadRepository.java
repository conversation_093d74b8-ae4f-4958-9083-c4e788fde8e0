package com.chidhagni.donationreceipt.donationhead;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonationHeadsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadPaginationRequest;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsDropDown;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.Tables.DONATION_HEADS;

@Repository
@RequiredArgsConstructor
public class DonationHeadRepository {

    private final DonationHeadsDao donationHeadsDao;
    private final DSLContext dslContext;

    public void saveDonationHead(DonationHeads donationHead) {
        try {
            donationHeadsDao.insert(donationHead);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while saving donation head", ex);
        }
    }

    public void updateDonationHead(DonationHeads donationHead) {
        try {
            donationHeadsDao.update(donationHead);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while saving donation head", ex);
        }
    }

    public DonationHeads fetchOneById(UUID id) {
        try {
            return donationHeadsDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching donation head", ex);
        }
    }

    public boolean existsByOrgIdAndName(UUID orgId, String name) {
        try {
            int count = dslContext
                    .selectCount()
                    .from(DONATION_HEADS)
                    .where(DONATION_HEADS.ORG_ID.eq(orgId))
                    .and(DONATION_HEADS.NAME.equalIgnoreCase(name))
                    .fetchOneInto(Integer.class);

            return count > 0;
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while checking donation head existence", ex);
        }
    }

    public List<Record> fetchDonationHeadByPagination(DonationHeadPaginationRequest request, Condition condition) {
        try {
            Integer offset = (request.getPage() - 1) * request.getPageSize();
            return dslContext.select(DONATION_HEADS.fields())
                    .from(DONATION_HEADS)
                    .where(condition)
                    .orderBy(DONATION_HEADS.CREATED_ON.desc())
                    .offset(offset)
                    .limit(request.getPageSize())
                    .fetch();
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching donation heads", ex);
        }
    }

    public Integer fetchDonationHeadCount(Condition condition) {
        try {
            return dslContext.selectCount()
                    .from(DONATION_HEADS)
                    .where(condition)
                    .fetchOneInto(Integer.class);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching donation head count", ex);
        }
    }

    public List<Record> findAllByOrgId(UUID orgId) {
        try {
            return new ArrayList<>(dslContext
                    .select(DONATION_HEADS.ID, DONATION_HEADS.NAME)
                    .from(DONATION_HEADS)
                    .where(DONATION_HEADS.ORG_ID.eq(orgId)
                            .and(DONATION_HEADS.IS_ACTIVE.isTrue()))
                    .fetch());
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching active donation heads for orgId: " + orgId, ex);
        }
    }


    public List<DonationHeadsDropDown> getAllDonationHeads() {
        return donationHeadsDao.ctx().select(DONATION_HEADS.ID.as("id"),DONATION_HEADS.NAME.as("donationHeadName"))
                .from(DONATION_HEADS)
                .fetchInto(DonationHeadsDropDown.class);
    }
}
