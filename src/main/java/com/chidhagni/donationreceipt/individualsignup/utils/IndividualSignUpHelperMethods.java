package com.chidhagni.donationreceipt.individualsignup.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.*;
import com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.IndividualPasswordResetRepository;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.dto.ResetStatus;
import com.chidhagni.donationreceipt.individualpermission.IndividualPermissionRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualOrganisationResponse;
import com.chidhagni.donationreceipt.organisation.OrganisationDonorMetaDataDto;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.organisation.dto.OrganisationTenantMetaData;
import com.chidhagni.donationreceipt.roles.RolesRepository;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class IndividualSignUpHelperMethods {

    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;


    private final IndividualRoleRepository individualRoleRepository;
    private final IndividualPermissionRepository individualPermissionRepository;
    private final RolesRepository rolesRepository;
    private final IndividualPasswordResetRepository individualPasswordResetRepository;
    private final OrganizationRepository organizationRepository;
    private final IndividualRepository individualRepository;

    public Individual createIndividual(IndividualVerificationAudit audit, UUID id) {
        Individual individual = new Individual();
        individual.setId(id);
        individual.setEmail(audit.getContactValue());
        individual.setName(audit.getMetaData().getIndividualName());
        individual.setMobileNumber(audit.getMetaData().getContactNumber());
        individual.setIsActive(Boolean.TRUE);
        individual.setCreatedOn(DateUtils.currentTimeIST());

        IndividualMetaDataDTO metaData = new IndividualMetaDataDTO();
        metaData.setPanNo(audit.getMetaData().getDonorPanNo());
        metaData.setAddress(audit.getMetaData().getDonorAddress());
        individual.setMetaData(metaData);

        return individual;
    }

    public void insertIndividualRole(IndividualVerificationAudit audit, UUID individualId, UUID orgId) {
        IndividualRole role = new IndividualRole();
        role.setId(UUID.randomUUID());
        role.setIndividualId(individualId);
        role.setRoleId(audit.getRoleId());
        role.setCreatedBy(individualId);
        role.setCreatedOn(DateUtils.currentTimeIST());
        role.setUpdatedOn(DateUtils.currentTimeIST());
        role.setOrgId(orgId);

        individualRoleRepository.insertIndividualRole(role);
    }

    public void insertIndividualPermissions(IndividualVerificationAudit audit, UUID individualId, UUID orgId) {
        Roles role = rolesRepository.getRoleById(audit.getRoleId());

        IndividualPermission permission = new IndividualPermission();
        permission.setId(UUID.randomUUID());
        permission.setIndividualId(individualId);
        permission.setPermissions(role.getPermissions());
        permission.setCreatedBy(individualId);
        permission.setCreatedOn(DateUtils.currentTimeIST());
        permission.setUpdatedOn(DateUtils.currentTimeIST());
        permission.setOrgId(orgId);

        individualPermissionRepository.insertIndividualPermission(permission);
    }

    public void insertPasswordResetAudit(IndividualVerificationAudit audit, UUID individualId) {
        IndividualPasswordResetAudit resetAudit = new IndividualPasswordResetAudit();
        resetAudit.setId(UUID.randomUUID());
        resetAudit.setIndividualId(individualId);
        resetAudit.setEmail(audit.getContactValue());
        resetAudit.setIsActive(Boolean.TRUE);
        resetAudit.setCreatedBy(individualId);
        resetAudit.setResetStatus(ResetStatus.INACTIVE.name());

        individualPasswordResetRepository.create(resetAudit);
    }

    public Organisation createOrganisation(IndividualVerificationAudit audit, Individual individual, UUID orgId) {
        Organisation organisation = new Organisation();
        organisation.setId(orgId);
        organisation.setName(audit.getMetaData().getDonorOrgName());
        organisation.setCategory(OrganisationEnums.DONOR.name());
        organisation.setIsActive(Boolean.TRUE);
        organisation.setCreatedBy(individual.getId());
        organisation.setCreatedOn(DateUtils.currentTimeIST());
        organisation.setUpdatedOn(DateUtils.currentTimeIST());
        organisation.setUpdatedBy(individual.getId());

        OrganisationDonorMetaDataDto organisationMetaData=new OrganisationDonorMetaDataDto();
        organisationMetaData.setType("DONOR");
        organisationMetaData.setDonorType(audit.getMetaData().getDonorType());
        organisationMetaData.setDonorReferralSource(audit.getMetaData().getDonorReferralSource());
        organisationMetaData.setDonorReferralSourceAnyOther(audit.getMetaData().getDonorReferralSourceAnyOther());
        organisation.setMetaData(organisationMetaData);

        return organisation;
    }


    public IndividualOrganisationResponse saveIndividualAndOrganisation(IndividualVerificationAudit individualVerificationAudit) {


        if (individualVerificationAudit.getOrgId() != null) {
            Individual individual = new Individual();
            individual.setId(UUID.randomUUID());
            individual.setEmail(individualVerificationAudit.getContactValue());
            individual.setName(individualVerificationAudit.getMetaData().getIndividualName());
            individual.setMobileNumber(individualVerificationAudit.getMetaData().getContactNumber());
            individual.setIsActive(Boolean.TRUE);
            individual.setCreatedOn(DateUtils.currentTimeIST());
            UUID loggedInIndividualId = null;
            if (individualVerificationAudit.getRoleId() != tenantAdminRoleId) {
                Organisation organisation = organizationRepository.getById(individualVerificationAudit.getOrgId());
                List<IndividualRole> individualRoleList = individualRoleRepository.getIndividualIdByOrganisationId(organisation.getId());
                loggedInIndividualId = individualRoleList.get(0).getIndividualId();
            }
            individual.setCreatedBy(loggedInIndividualId);
            individual.setUpdatedBy(loggedInIndividualId);

            individualRepository.insertIndividual(individual);
            return IndividualOrganisationResponse.builder()
                    .organisationId(individualVerificationAudit.getOrgId())
                    .individualId(individual.getId())
                    .email(individualVerificationAudit.getContactValue())
                    .build();
        } else {
            Individual individual = new Individual();
            individual.setId(UUID.randomUUID());
            individual.setEmail(individualVerificationAudit.getContactValue());
            individual.setName(individualVerificationAudit.getMetaData().getIndividualName());
            individual.setMobileNumber(individualVerificationAudit.getMetaData().getContactNumber());
            individual.setIsActive(Boolean.TRUE);
            individual.setCreatedOn(DateUtils.currentTimeIST());
            individualRepository.insertIndividual(individual);


            Organisation organisation = new Organisation();
            UUID orgId = UUID.randomUUID();
            organisation.setId(orgId);
            organisation.setName(individualVerificationAudit.getMetaData().getTrustName());
            organisation.setCreatedOn(DateUtils.currentTimeIST());
            organisation.setCategory(OrganisationEnums.TENANT.name());
            organisation.setIsActive(Boolean.TRUE);
            organisation.setCreatedBy(individual.getId());
            organisation.setCreatedOn(DateUtils.currentTimeIST());
            organisation.setUpdatedOn(DateUtils.currentTimeIST());
            organisation.setUpdatedBy(individual.getId());


            OrganisationTenantMetaData organisationTenantMetaData = new OrganisationTenantMetaData();
            organisationTenantMetaData.setType("TENANT");
            organisationTenantMetaData.setId(String.valueOf(orgId));
            organisationTenantMetaData.setOrgEmail(individualVerificationAudit.getMetaData().getOrgEmail());
            organisationTenantMetaData.setWebsite(individualVerificationAudit.getMetaData().getWebsite());
            organisationTenantMetaData.setAddress(null);
            organisationTenantMetaData.setRegistrationNo(null);
            organisationTenantMetaData.setPanNo(null);
            organisationTenantMetaData.setG80RegistrationNo(null);
            organisationTenantMetaData.setState(null);
            organisationTenantMetaData.setPinCode(null);
            organisationTenantMetaData.setLogoFileLocation(null);
            organisationTenantMetaData.setG80CertificationFileLocationPageOne(null);
            organisationTenantMetaData.setG80CertificationFileLocationPageTwo(null);

            organisation.setMetaData(organisationTenantMetaData);

            organizationRepository.insertOrganisation(organisation);
            return IndividualOrganisationResponse.builder()
                    .organisationId(organisation.getId())
                    .individualId(individual.getId())
                    .email(individualVerificationAudit.getContactValue())
                    .build();
        }


    }

}
