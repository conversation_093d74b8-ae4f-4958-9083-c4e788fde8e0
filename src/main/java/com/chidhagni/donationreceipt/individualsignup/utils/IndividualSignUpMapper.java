package com.chidhagni.donationreceipt.individualsignup.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface IndividualSignUpMapper {

    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
    @Mapping(target = "individualId", source = "individual.id")
    @Mapping(target = "email", source = "individual.email")
    @Mapping(target = "resetLink", ignore = true)
    @Mapping(target = "resetLinkRequestedAt", ignore = true)
    @Mapping(target = "resetLinkExpiresAt", ignore = true)
    @Mapping(target = "resetCompletedAt", ignore = true)
    @Mapping(target = "isActive", expression = "java(true)")
    @Mapping(target = "createdBy", source = "individual.id")
    @Mapping(target = "updatedBy", ignore = true)
    IndividualPasswordResetAudit mapIndividualToPasswordResetAudit(Individual individual);
}

