package com.chidhagni.donationreceipt.individualsignup.utils;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.IndividualPasswordResetRepository;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.dto.ResetStatus;
import com.chidhagni.donationreceipt.individualpermission.IndividualPermissionRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualOrganisationResponse;
import com.chidhagni.donationreceipt.roles.RolesRepository;
import com.chidhagni.utils.DateUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.UUID;



@Component
@RequiredArgsConstructor
public class SignUpMapper {
    private final ObjectMapper objectMapper;
    private final IndividualRoleRepository individualRoleRepository;
    private final IndividualPasswordResetRepository individualPasswordResetRepository;
    private final RolesRepository rolesRepository;
    private final IndividualPermissionRepository individualPermissionRepository;

    public void saveIndividualDerived(UUID roleId, IndividualOrganisationResponse individualOrganisationResponse) {

        IndividualRole individualRole = new IndividualRole();
        individualRole.setId(UUID.randomUUID());
        individualRole.setIndividualId(individualOrganisationResponse.getIndividualId());
        individualRole.setRoleId(roleId);
        individualRole.setOrgId(individualOrganisationResponse.getOrganisationId());
        individualRole.setCreatedBy(individualOrganisationResponse.getIndividualId());
        individualRole.setCreatedOn(DateUtils.currentTimeIST());
        individualRole.setUpdatedOn(DateUtils.currentTimeIST());

        individualRoleRepository.insertIndividualRole(individualRole);

        Roles roles = rolesRepository.getRoleById(roleId);

        IndividualPermission individualPermission = new IndividualPermission();
        individualPermission.setId(UUID.randomUUID());
        individualPermission.setIndividualId(individualOrganisationResponse.getIndividualId());
        individualPermission.setOrgId(individualOrganisationResponse.getOrganisationId());
        individualPermission.setPermissions(roles.getPermissions());
        individualPermission.setCreatedBy(individualOrganisationResponse.getIndividualId());
        individualPermission.setCreatedOn(DateUtils.currentTimeIST());
        individualPermission.setUpdatedOn(DateUtils.currentTimeIST());

        individualPermissionRepository.insertIndividualPermission(individualPermission);


        IndividualPasswordResetAudit individualPasswordResetAudit = new IndividualPasswordResetAudit();
        individualPasswordResetAudit.setId(UUID.randomUUID());
        individualPasswordResetAudit.setIndividualId(individualOrganisationResponse.getIndividualId());
        individualPasswordResetAudit.setEmail(individualOrganisationResponse.getEmail());
        individualPasswordResetAudit.setIsActive(Boolean.TRUE);
        individualPasswordResetAudit.setCreatedBy(individualOrganisationResponse.getIndividualId());
        individualPasswordResetAudit.setResetStatus(ResetStatus.INACTIVE.name());

        individualPasswordResetRepository.create(individualPasswordResetAudit);

    }

}
