package com.chidhagni.donationreceipt.individualsignup;

import com.chidhagni.donationreceipt.individualsignup.dto.request.ActivationDTO;
import com.chidhagni.donationreceipt.individualsignup.dto.request.CreatePasswordDTO;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualSignUpDTO;
import com.chidhagni.donationreceipt.individualsignup.dto.request.PasswordResetDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.mail.MessagingException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;


@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class SignUpController {
    private final SignUpService signUpService;

    @PostMapping("/api/v1/individual-signup")
    public ResponseEntity<String> signUp(@RequestBody IndividualSignUpDTO signUpDTO) {
        try {
            signUpService.signUp(signUpDTO);
            return ResponseEntity.ok("Activation link has been sent to your email.");
        } catch (JsonProcessingException | MessagingException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to process signup request. Please try again.");
        }
    }


    @PostMapping("/api/v1/activate")
    public ResponseEntity<?> activate(@RequestBody ActivationDTO activationDTO) {
        signUpService.activate(activationDTO);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }


    @PostMapping("/forgot-password-individual")
    public void forgotPassword( @RequestParam
                                    @Pattern(
                                            regexp = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,6}$",
                                            message = "Invalid email format")
                                    String email) throws MessagingException {
        signUpService.forgotPassword(email);
    }

    @PostMapping("/create-password")
    public void verifyResetPassword(@Valid @RequestBody CreatePasswordDTO createPasswordDTO) {
        signUpService.createPassword(createPasswordDTO.getEmailId(), createPasswordDTO.getPassword());
    }


    @PostMapping("/reset-password")
    public void verifyResetPassword(@Valid @RequestBody PasswordResetDTO passwordResetDTO) {
        signUpService.resetPassword(passwordResetDTO.getEmailId(), passwordResetDTO.getResetCode(), passwordResetDTO.getPassword());
    }
}
