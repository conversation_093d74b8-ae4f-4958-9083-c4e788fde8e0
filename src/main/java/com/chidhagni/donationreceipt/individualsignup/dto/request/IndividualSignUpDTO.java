package com.chidhagni.donationreceipt.individualsignup.dto.request;

import com.chidhagni.donationreceipt.organisation.constants.DefaultOrganisationCategoryEnums;
import com.chidhagni.donationreceipt.organisation.constants.ReadinessEnums;
import com.chidhagni.donationreceipt.organisation.constants.RegisteredFor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualSignUpDTO {
    @Pattern(
            regexp = "^((25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)$",
            message = "Invalid IP address format"
    )
    private String ipAddress;
    private String trustName;
    @NotNull
    private String contactPersonName;
    private String mobileNumber;
    @Email(regexp = "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,6}", flags = Pattern.Flag.CASE_INSENSITIVE)
    private String email;
    private String website;
    private String orgEmail;


    private String donorName;
    private String donorAddress;
    private String donorPanNo;
    private UUID donorType;
    private UUID donorReferralSource;
    private String donorReferralSourceAnyOther;
    private String donorOrgName;


    private UUID roleId;

}
