package com.chidhagni.donationreceipt.settings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PackageDTO {

    private List<PackageTypeDTO> packageTypes;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PackageTypeDTO {
        private UUID id;
        private String name;
        private Integer price;
        private Boolean listing;
        private Boolean microSite;
        private Boolean editorialIncluded;
        private Integer editorialDurationInMinutes;
        private Boolean audioVisualIncluded;
        private Integer audioVisualDurationInMinutes;
        private Boolean podcastIncluded;
        private Integer podcastDurationInMinutes;
        private Boolean isActive;
    }

}
