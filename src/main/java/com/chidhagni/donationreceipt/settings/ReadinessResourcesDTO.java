package com.chidhagni.donationreceipt.settings;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ReadinessResourcesDTO {

    List<PostDTO> needAssessment;

    List<PostDTO> typesOfRedevelopment;

    List<PostDTO> committee;

    List<PostDTO> consents;

    List<PostDTO> conveyance;
    List<PostDTO> managingCommittee;

    List<PostDTO> documents;

    List<PostDTO> appointmentOfProfessionals;

    List<PostDTO> preTenderingStage;

    List<PostDTO> tenderingStage;

    List<PostDTO> financialClosure;

}
