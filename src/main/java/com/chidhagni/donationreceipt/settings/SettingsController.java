package com.chidhagni.donationreceipt.settings;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/settings")
public class SettingsController {

//    private final SettingsService settingsService;

//    public SettingsController(SettingsService settingsService) {
//        this.settingsService = settingsService;
//    }
//
//    @PatchMapping
//    public ResponseEntity<SettingsManagementDTO> update(@Valid @RequestBody
//                                                        SettingsManagementDTO settingsManagementDTO,
//                                                        @CurrentUser UserPrincipal userPrincipal) throws Exception {
//
//        return ResponseEntity.ok().body(settingsService.update(settingsManagementDTO, userPrincipal));
//
//    }
//
//    @GetMapping
//    public ResponseEntity<SettingsManagementDTO> get(@Valid @RequestParam
//                                                     SettingsType settingsType) throws JsonProcessingException {
//
//        SettingsManagementDTO settingsManagementDTO = settingsService.get(settingsType);
//        return ResponseEntity.ok().body(settingsManagementDTO);
//
//    }

}
