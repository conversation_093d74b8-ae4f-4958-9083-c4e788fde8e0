package com.chidhagni.donationreceipt.settings;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListingCategoriesDTO {
    private List<CategoriesDTO> categoriesDTOList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoriesDTO {
        private String category;
        private List<String> services;
        private String imageUrl;
    }
}

