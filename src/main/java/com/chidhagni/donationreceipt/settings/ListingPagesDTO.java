package com.chidhagni.donationreceipt.settings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ListingPagesDTO {
    private List<CategoryDTO> category;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CategoryDTO {
        private String name;
        private List<ListingPagesChildrenDTO> children;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ListingPagesChildrenDTO {
        private String name;
    }
}
