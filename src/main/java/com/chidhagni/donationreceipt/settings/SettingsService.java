package com.chidhagni.donationreceipt.settings;


import org.springframework.stereotype.Service;


@Service
public class SettingsService {
//    private final SettingsDao settingsDao;
//    private final ObjectMapper objectMapper;
//    private static final String READINESS_REPORT_SETTINGS_NAME = "ReadinessReport";
//    private static final String USER_MANAGEMENT_NAME = "UserManagementSections";
//    private static final String FAQ_MANAGEMENT_NAME = "FaqData";
//    private static final String SITE_MAP_DATA = "SiteMapData";
//
//    private static final String LISTING_PAGES_DATA = "ListingPagesData";
//
//    private static final String USERTYPE_MANAGEMENT_NAME = "UserTypeData";//We are not using anywhere now. When Nav becomes dynamic we use it.
//
//    private static final String SITEMAP_TYPE_NAME = "SiteMapTypeData";//We are not using anywhere now. When Nav becomes dynamic we use it.
//
//
//
//    private static final String SP_LEFT_MENU_DATA_SET = "SP_LEFT_MENU_DATA_SET";
//
//    private  static  final String CHS_LEFT_MENU_DATA_SET="CHS_LEFT_MENU_DATA_SET";
//
//    private static final String EMP_LEFT_MENU_DATA_SET="EMP_LEFT_MENU_DATA_SET";
//
//    private static final String ADMIN_LEFT_MENU_DATA_SET="ADMIN_LEFT_MENU_DATA_SET";
//
//    private static final String TOP_MENU_DATA_SET = "TopMenuDataSet";
//
//    private static final String QUICK_LINKS_DATA_SET = "QuickLinksDataSet";
//
//    private static final String LISTING_CATEGORIES_DATA = "ListingCategoriesData";
//
//    private static final String DEFAULT_SERVICE_PROVIDERS_CONFIG_NAME = "SPsConfiguration";
//
//    private static final String DEFAULT_SOCIETIES_CONFIG_NAME = "SocietiesConfiguration";
//    private static final String PACKAGE_TYPE_DATA_SET = "PackageTypesData";
//
//
//    public SettingsService(SettingsDao settingsDao, ObjectMapper objectMapper) {
//        this.settingsDao = settingsDao;
//        this.objectMapper = objectMapper;
//    }
//
//    @Transactional
//    public SettingsManagementDTO update(SettingsManagementDTO settingsManagementDTO, UserPrincipal userPrincipal) throws IOException {
//
//        Settings setting = new Settings();
//
//        switch (settingsManagementDTO.getSettingsType()) {
//
//            case FAQ_DATA: {
//                setting = settingsDao.fetchByName(FAQ_MANAGEMENT_NAME).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getFaqContentDTO())));
//                break;
//            }
//            case USER_MANAGEMENT_DATA: {
//                setting = settingsDao.fetchByName(USER_MANAGEMENT_NAME).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getUserManagementDTO())));
//                break;
//            }
//            case CHECK_READINESS_POSTS: {
//                setting = settingsDao.fetchByName(READINESS_REPORT_SETTINGS_NAME).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getReadinessResourcesDTO())));
//                break;
//            }
//
//            case USER_TYPE: {
//                setting = settingsDao.fetchByName(USERTYPE_MANAGEMENT_NAME).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getUserTypeDTO())));
//                break;
//            }
//            case SITEMAP_TYPE: {
//                setting = settingsDao.fetchByName(SITEMAP_TYPE_NAME).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getSiteMapTypeDTO())));
//                break;
//            }
//
//            case SITE_MAP: {
//                setting = settingsDao.fetchByName(SITE_MAP_DATA).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getSiteMapDTO())));
//                break;
//            }
//            case LISTING_PAGES: {
//                setting = settingsDao.fetchByName(LISTING_PAGES_DATA).get(0);
//                setting.setMetadata(
//                        JSONB.valueOf(objectMapper.writeValueAsString(
//                                settingsManagementDTO.getListingPagesDTO())));
//                break;
//            }
//            case SP_LEFT_MENU: {
//                setting = settingsDao.fetchByName(SP_LEFT_MENU_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getLeftMenuDataSetDTO())));
//                break;
//            }
//            case CHS_LEFT_MENU:{
//                setting = settingsDao.fetchByName( CHS_LEFT_MENU_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getLeftMenuDataSetDTO())));
//                break;
//            }
//
//            case EMP_LEFT_MENU:{
//                setting = settingsDao.fetchByName(EMP_LEFT_MENU_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getLeftMenuDataSetDTO())));
//                break;
//            }
//
//            case ADMIN_LEFT_MENU:{
//                setting = settingsDao.fetchByName(ADMIN_LEFT_MENU_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getLeftMenuDataSetDTO())));
//                break;
//            }
//
//
//
//            case TOP_MENU: {
//                setting = settingsDao.fetchByName(TOP_MENU_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getTopMenuDataSetDTO())));
//                break;
//            }
//            case QUICK_LINKS: {
//                setting = settingsDao.fetchByName(QUICK_LINKS_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getQuickLinksDataSetDTO())));
//                break;
//            }
//
//            case LISTING_CATEGORIES: {
//                setting = settingsDao.fetchByName(LISTING_CATEGORIES_DATA).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getListingCategoriesDTO())));
//                break;
//            }
//            case DEFAULT_SERVICE_PROVIDERS_CONFIGURATION: {
//                setting = settingsDao.fetchByName(DEFAULT_SERVICE_PROVIDERS_CONFIG_NAME).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getConfigurationDTO())));
//                break;
//            }
//            case DEFAULT_SOCIETIES_CONFIGURATION: {
//                setting = settingsDao.fetchByName(DEFAULT_SOCIETIES_CONFIG_NAME).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getConfigurationDTO())));
//                break;
//            }
//            case PACKAGE_TYPES: {
//                setting = settingsDao.fetchByName(PACKAGE_TYPE_DATA_SET).get(0);
//                setting.setMetadata(JSONB.valueOf(objectMapper.
//                        writeValueAsString(settingsManagementDTO.getPackageDTO())));
//                break;
//            }
//        }
//        setting.setUpdatedOn(DateUtils.currentTimeIST());
//        setting.setUpdatedBy(userPrincipal.getId());
//        settingsDao.update(setting);
//
//        return settingsManagementDTO;
//    }
//
//    public SettingsManagementDTO get(SettingsType settingsType) throws JsonProcessingException {
//        SettingsManagementDTO.SettingsManagementDTOBuilder
//                settingsManagementDTOBuilder = new SettingsManagementDTO.SettingsManagementDTOBuilder();
//
//        settingsManagementDTOBuilder.settingsType(settingsType);
//
//        switch (settingsType) {
//
//            case FAQ_DATA: {
//                settingsManagementDTOBuilder
//                        .faqContentDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(FAQ_MANAGEMENT_NAME).get(0).getMetadata().toString(),
//                                FAQContentDTO.class));
//                break;
//            }
//            case USER_MANAGEMENT_DATA: {
//                settingsManagementDTOBuilder
//                        .userManagementDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(USER_MANAGEMENT_NAME).get(0).getMetadata().toString(),
//                                UserManagementDTO.class));
//                break;
//            }
//            case CHECK_READINESS_POSTS: {
//                settingsManagementDTOBuilder
//                        .readinessResourcesDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(READINESS_REPORT_SETTINGS_NAME).get(0).getMetadata().toString(),
//                                ReadinessResourcesDTO.class));
//                break;
//            }
//
//            case USER_TYPE: {
//                settingsManagementDTOBuilder
//                        .userTypeDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(USERTYPE_MANAGEMENT_NAME).get(0).getMetadata().toString(),
//                                UserTypeDTO.class));
//                break;
//            }
//
//            case SITE_MAP: {
//                settingsManagementDTOBuilder
//                        .siteMapDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(SITE_MAP_DATA).get(0).getMetadata().toString(),
//                                SiteMapDTO.class));
//                break;
//            }
//
//
//            case SITEMAP_TYPE: {
//                settingsManagementDTOBuilder
//                        .siteMapTypeDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(SITEMAP_TYPE_NAME).get(0).getMetadata().toString(),
//                                SiteMapTypeDTO.class));
//                break;
//
//            }
//            case LISTING_PAGES: {
//                settingsManagementDTOBuilder
//                        .listingPagesDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(LISTING_PAGES_DATA).get(0).getMetadata().toString(),
//                                ListingPagesDTO.class));
//                break;
//            }
//            case SP_LEFT_MENU: {
//                settingsManagementDTOBuilder
//                        .leftMenuDataSetDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(SP_LEFT_MENU_DATA_SET).get(0).getMetadata().toString(),
//                                LeftMenuDataSetDTO.class));
//                break;
//
//            }
//
//            case CHS_LEFT_MENU: {
//                settingsManagementDTOBuilder
//                        .leftMenuDataSetDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(CHS_LEFT_MENU_DATA_SET).get(0).getMetadata().toString(),
//                                LeftMenuDataSetDTO.class));
//                break;
//
//            }
//
//            case EMP_LEFT_MENU: {
//                settingsManagementDTOBuilder
//                        .leftMenuDataSetDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(EMP_LEFT_MENU_DATA_SET).get(0).getMetadata().toString(),
//                                LeftMenuDataSetDTO.class));
//                break;
//
//            }
//
//            case ADMIN_LEFT_MENU: {
//                settingsManagementDTOBuilder
//                        .leftMenuDataSetDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(ADMIN_LEFT_MENU_DATA_SET).get(0).getMetadata().toString(),
//                                LeftMenuDataSetDTO.class));
//                break;
//
//            }
//            case TOP_MENU: {
//                settingsManagementDTOBuilder
//                        .topMenuDataSetDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(TOP_MENU_DATA_SET).get(0).getMetadata().toString(),
//                                TopMenuDataSetDTO.class));
//                break;
//            }
//            case QUICK_LINKS: {
//                settingsManagementDTOBuilder
//                        .quickLinksDataSetDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(QUICK_LINKS_DATA_SET).get(0).getMetadata().toString(),
//                                QuickLinksDataSetDTO.class));
//            }
//
//            case LISTING_CATEGORIES: {
//                settingsManagementDTOBuilder
//                        .listingCategoriesDTO(objectMapper.readValue(
//                                settingsDao.fetchByName(LISTING_CATEGORIES_DATA).get(0).getMetadata().toString(),
//                                ListingCategoriesDTO.class));
//                break;
//            }
//            case DEFAULT_SERVICE_PROVIDERS_CONFIGURATION:{
//                settingsManagementDTOBuilder.configurationDTO(objectMapper.
//                        readValue(settingsDao.fetchByName(DEFAULT_SERVICE_PROVIDERS_CONFIG_NAME).get(0).getMetadata().toString(),
//                        ConfigurationDTO.class));
//                break;
//            }
//            case DEFAULT_SOCIETIES_CONFIGURATION:{
//                settingsManagementDTOBuilder.configurationDTO(objectMapper.
//                        readValue(settingsDao.fetchByName(DEFAULT_SOCIETIES_CONFIG_NAME).get(0).getMetadata().toString(),
//                                ConfigurationDTO.class));
//                break;
//            }
//            case PACKAGE_TYPES:{
//                settingsManagementDTOBuilder.packageDTO(objectMapper.
//                        readValue(settingsDao.fetchByName(PACKAGE_TYPE_DATA_SET).get(0).getMetadata().toString(),
//                                PackageDTO.class));
//                break;
//            }
//        }
//
//        return settingsManagementDTOBuilder.build();
//
//
//    }

}
