package com.chidhagni.donationreceipt.settings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettingsManagementDTO {

    private SettingsType settingsType;

    private ConfigurationDTO configurationDTO;

    private UserManagementDTO userManagementDTO;

    private LeftMenuDataSetDTO leftMenuDataSetDTO;

    private TopMenuDataSetDTO topMenuDataSetDTO;

    private QuickLinksDataSetDTO quickLinksDataSetDTO;

    private ReadinessResourcesDTO readinessResourcesDTO;

    private FAQContentDTO faqContentDTO;
    private SiteMapDTO siteMapDTO;

    private UserTypeDTO userTypeDTO;

    private SiteMapTypeDTO siteMapTypeDTO;
    private ListingPagesDTO listingPagesDTO;
    private ListingCategoriesDTO listingCategoriesDTO;
    private PackageDTO packageDTO;


}
