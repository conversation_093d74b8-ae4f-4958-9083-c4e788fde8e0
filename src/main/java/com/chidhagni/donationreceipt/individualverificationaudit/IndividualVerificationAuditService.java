package com.chidhagni.donationreceipt.individualverificationaudit;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.ContactType;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.response.EmailVerifyResponseDTO;
import com.chidhagni.donationreceipt.individualverificationaudit.utils.IndividualVerificationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import java.util.List;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;


@Slf4j
@Service
@RequiredArgsConstructor
public class IndividualVerificationAuditService {
    private final DSLContext dsl;
    private final IndividualVerificationAuditRepository individualVerificationAuditRepository;
    private final IndividualVerificationMapper individualVerificationMapper;

    public IndividualVerificationAudit updateActivateLink(IndividualVerificationAudit individualVerificationAudit) {
        IndividualVerificationAudit individualVerificationAuditUpdate =
                individualVerificationMapper.updateActivationLink(individualVerificationAudit);
        individualVerificationAuditRepository.updateIndividualVerificationAudit(individualVerificationAuditUpdate);
        return individualVerificationAuditUpdate;
    }

    public EmailVerifyResponseDTO checkEmailExists(String email) {
        // Fetch verification status from DB as String
        String statusStr = dsl.select(field("verification_status"))
                .from(table("individual_verification_audit"))
                .where(field("contact_value").eq(email)
                        .and(field("contact_type").eq(ContactType.EMAIL.name())))
                .fetchOneInto(String.class);

        if (statusStr == null) {
            return EmailVerifyResponseDTO.builder()
                    .isVerified(false)
                    .message(null)
                    .build();
        }

        VerificationStatusEnum status;
        try {
            status = VerificationStatusEnum.valueOf(statusStr);
        } catch (IllegalArgumentException e) {
            // Handle unknown status string gracefully
            status = VerificationStatusEnum.PENDING;
        }

        boolean isVerified = VerificationStatusEnum.VERIFIED.equals(status);

        return EmailVerifyResponseDTO.builder()
                .isVerified(isVerified)
                .message("Email Already Exist")
                .build();
    }

}
