package com.chidhagni.donationreceipt.individualverificationaudit.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualSignUpDTO;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.ContactType;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData;
import com.chidhagni.donationreceipt.mobileotp.MobileOtpRequestDto;
import com.chidhagni.donationreceipt.organisation.MetaDataDTO;
import com.chidhagni.donationreceipt.organisation.dto.OrganisationTenantMetaData;
import com.chidhagni.donationreceipt.organisation.dto.request.OrgIndividuals;
import com.chidhagni.utils.DateUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class IndividualVerificationMapper {

    private final ObjectMapper objectMapper;


    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;

    public IndividualVerificationAudit createIndividualVerification(IndividualSignUpDTO signUpDTO) throws JsonProcessingException {
        IndividualVerificationAudit individualVerificationAudit=new IndividualVerificationAudit();
        individualVerificationAudit.setId(UUID.randomUUID());
        individualVerificationAudit.setContactType(ContactType.EMAIL.name());
        individualVerificationAudit.setContactValue(signUpDTO.getEmail());
        individualVerificationAudit.setActivationLink(Boolean.FALSE);
        individualVerificationAudit.setRoleId(signUpDTO.getRoleId());
        individualVerificationAudit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
        individualVerificationAudit.setIsActive(Boolean.TRUE);
        individualVerificationAudit.setActivationLinkCreatedAt(DateUtils.currentTimeIST());
        individualVerificationAudit.setActivationLinkExpiresAt(DateUtils.currentTimeIST().plusDays(1));
        individualVerificationAudit.setCreatedOn(DateUtils.currentTimeIST());

        IndividualVerificationMetaData individualVerificationMetaData=new IndividualVerificationMetaData();
        individualVerificationMetaData.setIndividualName(signUpDTO.getContactPersonName());
        individualVerificationMetaData.setTrustName(signUpDTO.getTrustName());
        individualVerificationMetaData.setContactNumber(signUpDTO.getMobileNumber());
        individualVerificationMetaData.setWebsite(signUpDTO.getWebsite());
        individualVerificationMetaData.setOrgEmail(signUpDTO.getOrgEmail());
        individualVerificationAudit.setMetaData(individualVerificationMetaData);
        return individualVerificationAudit;
    }


    public IndividualVerificationAudit updateActivationLink(IndividualVerificationAudit individualVerificationAudit)
    {

        individualVerificationAudit.setActivationLink(Boolean.TRUE);
        individualVerificationAudit.setUpdatedOn(DateUtils.currentTimeIST());
        individualVerificationAudit.setVerificationStatus(VerificationStatusEnum.VERIFIED.name());
        individualVerificationAudit.setActivationLinkVerifiedAt(DateUtils.currentTimeIST());
        return individualVerificationAudit;
    }

    public IndividualVerificationAudit createIndividualVerificationAudit(OrgIndividuals individualDTO, Organisation organisation) {

        IndividualVerificationAudit individualVerificationAudit=new IndividualVerificationAudit();
        individualVerificationAudit.setId(individualDTO.getId());
        individualVerificationAudit.setContactType(ContactType.EMAIL.name());
        individualVerificationAudit.setContactValue(individualDTO.getEmail());
        individualVerificationAudit.setActivationLink(Boolean.FALSE);
        individualVerificationAudit.setRoleId(individualDTO.getRoleId());
        individualVerificationAudit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
        individualVerificationAudit.setIsActive(Boolean.TRUE);
        individualVerificationAudit.setActivationLinkCreatedAt(DateUtils.currentDatetime());
        individualVerificationAudit.setActivationLinkExpiresAt(DateUtils.currentTimeIST().plusDays(1));
        individualVerificationAudit.setCreatedOn(DateUtils.currentTimeIST());
        individualVerificationAudit.setOrgId(organisation.getId());


        IndividualVerificationMetaData individualVerificationMetaData=new IndividualVerificationMetaData();
        individualVerificationMetaData.setIndividualName(individualDTO.getName());
        individualVerificationMetaData.setTrustName(organisation.getName());
        individualVerificationMetaData.setContactNumber(individualDTO.getMobileNumber());
        MetaDataDTO metaDataDTO=organisation.getMetaData();
        if(metaDataDTO instanceof OrganisationTenantMetaData)
        {
            OrganisationTenantMetaData tenantMetaData = (OrganisationTenantMetaData) metaDataDTO;
            individualVerificationMetaData.setWebsite(tenantMetaData.getWebsite());
            individualVerificationMetaData.setOrgEmail(tenantMetaData.getOrgEmail());
        }
        individualVerificationAudit.setMetaData(individualVerificationMetaData);
        individualVerificationAudit.setMetaData(individualVerificationMetaData);

        return individualVerificationAudit;
    }


    public IndividualVerificationAudit createEmailIndividualVerificationDonor(IndividualSignUpDTO signUpDTO) throws JsonProcessingException {
        IndividualVerificationAudit individualVerificationAudit=new IndividualVerificationAudit();
        individualVerificationAudit.setId(UUID.randomUUID());
        individualVerificationAudit.setContactType(ContactType.EMAIL.name());
        individualVerificationAudit.setContactValue(signUpDTO.getEmail());
        individualVerificationAudit.setActivationLink(Boolean.FALSE);
        individualVerificationAudit.setRoleId(signUpDTO.getRoleId());
        individualVerificationAudit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
        individualVerificationAudit.setIsActive(Boolean.TRUE);
        individualVerificationAudit.setActivationLinkCreatedAt(DateUtils.currentTimeIST());
        individualVerificationAudit.setActivationLinkExpiresAt(DateUtils.currentTimeIST().plusDays(1));
        individualVerificationAudit.setCreatedOn(DateUtils.currentTimeIST());

        IndividualVerificationMetaData individualVerificationMetaData=new IndividualVerificationMetaData();
        individualVerificationMetaData.setIndividualName(signUpDTO.getDonorName());
        individualVerificationMetaData.setContactNumber(signUpDTO.getMobileNumber());
        individualVerificationMetaData.setDonorPanNo(signUpDTO.getDonorPanNo());
        individualVerificationMetaData.setDonorAddress(signUpDTO.getDonorAddress());
        individualVerificationMetaData.setDonorType(signUpDTO.getDonorType());
        individualVerificationMetaData.setDonorReferralSource(signUpDTO.getDonorReferralSource());
        individualVerificationMetaData.setDonorReferralSourceAnyOther(signUpDTO.getDonorReferralSourceAnyOther());
        individualVerificationMetaData.setDonorOrgName(
                signUpDTO.getDonorOrgName() != null ? signUpDTO.getDonorOrgName() : signUpDTO.getDonorName()
        );
        individualVerificationAudit.setMetaData(individualVerificationMetaData);
        return individualVerificationAudit;
    }


    public IndividualVerificationAudit createMobileNumberVerification(String mobileNumber, MobileOtpRequestDto mobileOtpRequestDto, String otpCode)
    {

        IndividualVerificationAudit individualVerificationAudit=new IndividualVerificationAudit();
        individualVerificationAudit.setId(UUID.randomUUID());
        individualVerificationAudit.setContactType(ContactType.MOBILE.name());
        individualVerificationAudit.setContactValue(mobileNumber);
        individualVerificationAudit.setActivationLink(Boolean.FALSE);
        individualVerificationAudit.setRoleId(mobileOtpRequestDto.getRoleId());
        individualVerificationAudit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
        individualVerificationAudit.setIsActive(Boolean.TRUE);
        individualVerificationAudit.setOtpCode(otpCode);
        individualVerificationAudit.setOtpCreatedAt(DateUtils.currentTimeIST());
        individualVerificationAudit.setOtpExpiresAt(DateUtils.currentTimeIST().plusMinutes(10));
        individualVerificationAudit.setCreatedOn(DateUtils.currentTimeIST());

        IndividualVerificationMetaData individualVerificationMetaData=new IndividualVerificationMetaData();
        individualVerificationMetaData.setIndividualName(mobileOtpRequestDto.getName());
        individualVerificationMetaData.setContactNumber(mobileNumber);
        individualVerificationAudit.setMetaData(individualVerificationMetaData);
        return individualVerificationAudit;
    }
}
