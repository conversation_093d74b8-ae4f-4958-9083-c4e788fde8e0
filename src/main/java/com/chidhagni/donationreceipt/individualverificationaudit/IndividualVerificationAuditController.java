package com.chidhagni.donationreceipt.individualverificationaudit;

import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.EmailCheckRequestDTO;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.response.EmailVerifyResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class IndividualVerificationAuditController {

    private final IndividualVerificationAuditService individualVerificationAuditService;

    @PostMapping("/check-email")
    public ResponseEntity<EmailVerifyResponseDTO> checkEmail(@RequestBody EmailCheckRequestDTO request) {
        EmailVerifyResponseDTO response = individualVerificationAuditService.checkEmailExists(request.getEmail());
        return ResponseEntity.ok(response);
    }

}
