package com.chidhagni.donationreceipt.individualverificationaudit.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualVerificationResponseDTO {

    private UUID id;
    private String contactType;
    private String contactValue;
    private String verificationStatus;
    private Boolean isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
