package com.chidhagni.donationreceipt.individualverificationaudit.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IndividualVerificationMetaData {

    private String individualName;
    private String contactNumber;
    private String website;
    private String orgEmail;
    private String trustName;

    private String donorPanNo;
    private String donorAddress;
    private UUID donorType;
    private UUID donorReferralSource;
    private String donorReferralSourceAnyOther;
    private String donorOrgName;
}
