package com.chidhagni.donationreceipt.individualverificationaudit.jooq;

import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import org.jetbrains.annotations.NotNull;

public class IndividualVerificationJsonConverter extends BaseJsonBJooqConverter<IndividualVerificationMetaData> {

    @Override
    public @NotNull Class<IndividualVerificationMetaData> toType() {

        return IndividualVerificationMetaData.class;
    }
}


