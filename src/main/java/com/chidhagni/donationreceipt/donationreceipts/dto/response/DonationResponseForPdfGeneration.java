package com.chidhagni.donationreceipt.donationreceipts.dto.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationResponseForPdfGeneration {

    private String orgName;
    private String mobileNumber;//org tenant admin
    private String email;//org tenant admin

    private String regdNo;
    private String receiptNo;
    private LocalDate receiptDate;
    private String panNo;

    private String donorName;
    private String donorAddress;
    private String donorMobileNumber;
    private String donorEmail;
    private String donorPanNo;
    private String donorAadharNo;
    private String amount;
    private String onAcOf;
    private String donationHeadName;
    private String paymentMode;


    private String orgEmail;
    private String tenantAdminName;
}
