package com.chidhagni.donationreceipt.donationreceipts.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationReceiptPaginationRequest {
    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 10;
    @Builder.Default
    private String receiptNoFilter = "";
    @Builder.Default
    private UUID orgIdFilter = null;
    @Builder.Default
    private UUID donationTypeIdFilter = null;
    @Builder.Default
    private UUID donationHeadIdFilter = null;


    @Builder.Default
    private String donorName="";
    @Builder.Default
    private UUID paymentMode= null;

    private String searchKeyWord;
}
