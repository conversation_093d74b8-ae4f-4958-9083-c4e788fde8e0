package com.chidhagni.donationreceipt.donationreceipts.jooq;

import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;

public class DonationReceiptsMetaDataJsonConverter extends BaseJsonBJooqConverter<DonationReceiptMetaDataDTO> {
    @Override
    public Class<DonationReceiptMetaDataDTO> toType() {
        return DonationReceiptMetaDataDTO.class;
    }
}

