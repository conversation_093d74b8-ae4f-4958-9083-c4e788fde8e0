package com.chidhagni.donationreceipt.donationreceipts;

import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptPaginationRequest;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptRequestDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.GetAllDonationReceipts;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.mail.MessagingException;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.UUID;

@RestController
@RequestMapping("/donation-receipts")
@RequiredArgsConstructor
@Slf4j
public class DonationReceiptController {

    private final DonationReceiptService donationReceiptService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<UUID> createDonationReceipt(
            @Valid @RequestBody DonationReceiptRequestDTO requestDTO,
            @CurrentUser UserPrincipal userPrincipal) {
        UUID id = donationReceiptService.createDonationReceipt(requestDTO, userPrincipal.getId());
        return new ResponseEntity<>(id, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> updateDonationReceipt(
            @PathVariable UUID id,
            @Valid @RequestBody DonationReceiptRequestDTO requestDTO,
            @CurrentUser UserPrincipal userPrincipal) {
        donationReceiptService.updateDonationReceipt(id, requestDTO, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @PatchMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> activateDonationReceipt(
            @PathVariable UUID id,
            @CurrentUser UserPrincipal userPrincipal) {
        donationReceiptService.activateDonationReceipt(id, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> deactivateDonationReceipt(
            @PathVariable UUID id,
            @CurrentUser UserPrincipal userPrincipal) {
        donationReceiptService.deactivateDonationReceipt(id, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/all")
    public ResponseEntity<GetAllDonationReceipts> fetchAllDonationReceipts(
            @RequestBody(required = false) DonationReceiptPaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = DonationReceiptPaginationRequest.builder().build();
        }
        GetAllDonationReceipts getAllDonationReceipts =
                donationReceiptService.fetchAllDonationReceipts(paginationRequest,userPrincipal.getId());
        return ResponseEntity.ok(getAllDonationReceipts);
    }

    @GetMapping("/{id}")
    public ResponseEntity<DonationReceiptResponseDTO> getDonationReceiptById(@PathVariable UUID id) {
        DonationReceiptResponseDTO response = donationReceiptService.getDonationReceiptsById(id);
        return ResponseEntity.ok(response);
    }


    @PostMapping(path ="/generate-pdf/{id}",produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<byte[]> downloadReceipt(@PathVariable UUID id) throws IOException, MessagingException {
        byte[] pdfBytes = donationReceiptService.generateReceipt(id);
        log.info("Request to download the receipt pdf  id :: {}", id);
        if (pdfBytes != null) {
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=receipt.pdf")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(pdfBytes);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping(path="/api/wati/shareReceiptViaWA",produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> sendReceipt(
            @RequestParam String mobileNumber,
            @RequestParam UUID receiptId) {
        try {
            // Basic validation
            if (mobileNumber == null || mobileNumber.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("Mobile number is required");
            }
            if (receiptId == null) {
                return ResponseEntity.badRequest().body("Receipt ID is required");
            }

            boolean success = donationReceiptService.sendReceiptMessage(mobileNumber, receiptId);

            if (success) {
                return ResponseEntity.ok("Message sent successfully");
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Failed to send message");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error: " + e.getMessage());
        }
    }
}
