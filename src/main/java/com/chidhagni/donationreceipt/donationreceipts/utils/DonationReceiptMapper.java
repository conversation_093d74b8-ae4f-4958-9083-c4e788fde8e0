package com.chidhagni.donationreceipt.donationreceipts.utils;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptRequestDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import com.chidhagni.utils.DateUtils;


import java.util.UUID;

@Mapper(componentModel = "spring")
public interface DonationReceiptMapper {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "tenantOrgId", source = "requestDTO.orgId")
    @Mapping(target = "metaData", source = "requestDTO.metaData")
    @Mapping(target = "donationTypeId", source = "requestDTO.donationTypeId")
    @Mapping(target = "donationHeadId", source = "requestDTO.donationHeadId")
    @Mapping(target = "receiptDate", source = "requestDTO.receiptDate")
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "createdBy", source = "userId")
    @Mapping(target = "createdOn", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    @Mapping(target = "donorOrgId",source = "requestDTO.donorOrgId")
    @Mapping(target = "donorId",source = "requestDTO.donorId")
    DonationReceipts donationReceiptRequestDtoToDonationReceipt(DonationReceiptRequestDTO requestDTO,
                                                                UUID userId, UUID id);


    @Mapping(target = "metaData", source = "requestDTO.metaData")
    @Mapping(target = "receiptDate", source = "requestDTO.receiptDate")
    @Mapping(target = "updatedOn", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "updatedBy", source = "userId")
    @Mapping(target = "tenantOrgId", source = "requestDTO.orgId")
    @Mapping(target = "donationTypeId", source = "requestDTO.donationTypeId")
    @Mapping(target = "donationHeadId", source = "requestDTO.donationHeadId")
    @Mapping(target = "donorOrgId",source = "requestDTO.donorOrgId")
    @Mapping(target = "donorId",source = "requestDTO.donorId")
    DonationReceipts updateDonationReceiptFromDto(DonationReceiptRequestDTO requestDTO,
                                                  @MappingTarget DonationReceipts donationReceipt, UUID userId);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "receiptNo", source = "receiptNo")
    @Mapping(target = "orgId", source = "tenantOrgId")
    @Mapping(target = "metaData", source = "metaData")
    @Mapping(target = "donationTypeId", source = "donationTypeId")
    @Mapping(target = "donationHeadId", source = "donationHeadId")
    @Mapping(target = "receiptDate", source = "receiptDate")
    @Mapping(target = "isActive", source = "isActive")
    @Mapping(target = "createdOn", source = "createdOn")
    @Mapping(target = "createdBy", source = "createdBy")
    @Mapping(target = "updatedOn", source = "updatedOn")
    @Mapping(target = "updatedBy", source = "updatedBy")
    DonationReceiptResponseDTO donationReceiptToResponseDTO(DonationReceipts donationReceipt);

}
