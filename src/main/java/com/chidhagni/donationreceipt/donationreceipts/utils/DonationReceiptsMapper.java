package com.chidhagni.donationreceipt.donationreceipts.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.donationhead.DonationHeadRepository;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.SelfRegisteredDonorDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.TenantDonorsDTO;
import com.chidhagni.donationreceipt.services.listvalues.ListValuesRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DonationReceiptsMapper {

    private final DonationHeadRepository donationHeadRepository;

    public DonationReceiptResponseDTO mapToDonationReceiptResponseDto(DonationReceipts donationReceipts,
                                                                      Donors donors, Individual individual,
                                                                      String tenantOrgName)
    {

        SelfRegisteredDonorDTO selfRegisteredDonorDTO=new SelfRegisteredDonorDTO();
        if(individual!=null) {
            selfRegisteredDonorDTO.setId(individual.getId());
            selfRegisteredDonorDTO.setDonorEmail(individual.getEmail() != null ? individual.getEmail() : null);
            selfRegisteredDonorDTO.setDonorName(individual.getName() != null ? individual.getName() : null);
            selfRegisteredDonorDTO.setDonorPanNo(individual.getMetaData() != null
                    && individual.getMetaData().getPanNo() != null ? individual.getMetaData().getPanNo() : null);
            selfRegisteredDonorDTO.setAddress(individual.getMetaData() != null
                    && individual.getMetaData().getAddress() != null ? individual.getMetaData().getAddress() : null);
            selfRegisteredDonorDTO.setDonorMobileNumber(individual.getMobileNumber() != null
                    ? individual.getMobileNumber() : null);
        }

        TenantDonorsDTO tenantDonorsDTO=new TenantDonorsDTO();
        if(donors!=null) {
            tenantDonorsDTO.setId(donors.getId());
            tenantDonorsDTO.setName(donors.getName() != null ? donors.getName() : null);
            tenantDonorsDTO.setEmail(donors.getEmail() != null ? donors.getEmail() : null);
            tenantDonorsDTO.setPanNo(donors.getPanNo() != null ? donors.getPanNo() : null);
            tenantDonorsDTO.setMobileNumber(donors.getMobileNumber() != null ? donors.getMobileNumber() : null);
            tenantDonorsDTO.setAddress(donors.getMetaData() != null &&
                    donors.getMetaData().getAddress() != null ? donors.getMetaData().getAddress() : null);
        }

        DonationHeads donationHead=donationHeadRepository.fetchOneById(donationReceipts.getDonationHeadId());

     return DonationReceiptResponseDTO.builder()
             .id(donationReceipts.getId())
             .donationTypeId(donationReceipts.getDonationTypeId())
             .donationHeadId(donationReceipts.getDonationHeadId())
             .donationHead(donationHead.getName())
             .orgId(donationReceipts.getTenantOrgId())
             .orgName(tenantOrgName)
             .receiptNo(donationReceipts.getReceiptNo())
             .metaData(donationReceipts.getMetaData())
             .receiptDate(String.valueOf(donationReceipts.getReceiptDate()))
             .selfRegisteredDonorDTO(selfRegisteredDonorDTO)
             .tenantDonorsDTO(tenantDonorsDTO)
             .createdOn(donationReceipts.getCreatedOn())
             .createdBy(donationReceipts.getCreatedBy())
             .updatedBy(donationReceipts.getUpdatedBy())
             .updatedOn(donationReceipts.getUpdatedOn())
             .isActive(donationReceipts.getIsActive())
             .build();

    }
}
