package com.chidhagni.donationreceipt.donorsimport.importbatch;


import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportPaginationRequest;
import com.chidhagni.donationreceipt.donorsimport.dto.response.GetAllImportBatchResponses;
import com.chidhagni.donationreceipt.donorsimport.dto.response.ImportBatchResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ImportBatchService {


    private final ImportBatchRepository importBatchRepository;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;

    public GetAllImportBatchResponses getAllImportBatch(ImportPaginationRequest paginationRequest,
                                                        UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = null;
        List<SortField<?>> sortFields = new ArrayList<>();
        List<ImportBatchResponse> allImportBatchResponses = importBatchRepository.
                getAllImportBatch(finalCondition, sortFields,paginationRequest, userPrincipal);
        return GetAllImportBatchResponses.builder().
                importBatchResponsesList(allImportBatchResponses)
                .rowCount(allImportBatchResponses.size()).build();
    }

    public void applyDefaults(ImportPaginationRequest paginationRequest) {
        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }
    }
}
