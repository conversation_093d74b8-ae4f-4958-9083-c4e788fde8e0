package com.chidhagni.donationreceipt.donorsimport.importbatch;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.ImportBatchDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.donorsimport.dto.response.ImportBatchResponse;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportPaginationRequest;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch.IMPORT_BATCH;
import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;

@Repository
@RequiredArgsConstructor
public class ImportBatchRepository {

    private final ImportBatchDao importBatchDao;
    private final OrganizationRepository organizationRepository;
    private final IndividualRoleDao individualRoleDao;

    @Value("${roles.superadmin}")
    private UUID superAdminRoleId;

    private boolean isSuperAdmin(UUID individualId) {
        return individualRoleDao.ctx().fetchExists(
                individualRoleDao.ctx().selectOne()
                        .from(INDIVIDUAL_ROLE)
                        .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(individualId)
                                .and(INDIVIDUAL_ROLE.ROLE_ID.eq(superAdminRoleId)))
        );
    }

    private Condition buildWhereCondition(UserPrincipal userPrincipal, Condition finalCondition, boolean isSuperAdmin) {
        if (!isSuperAdmin) {
            Organisation organisation = organizationRepository.fetchOrganizationByIndividualId(userPrincipal.getId());
            return IMPORT_BATCH.TENANT_ORG_ID.eq(organisation.getId())
                    .and(IMPORT_BATCH.CATEGORY.eq(OrganisationEnums.DONOR.name()))
                    .and(finalCondition != null ? finalCondition : DSL.trueCondition());
        } else {
            return finalCondition != null ? finalCondition : DSL.trueCondition();
        }
    }

    public List<ImportBatchResponse> getAllImportBatch(Condition finalCondition, List<SortField<?>> sortFields,
                                                       ImportPaginationRequest paginationRequest,
                                                       UserPrincipal userPrincipal) {
        UUID individualId = userPrincipal.getId();
        boolean isSuperAdmin = isSuperAdmin(individualId);


        // Build the query
        var query = importBatchDao.ctx().select(
                        IMPORT_BATCH.ID.as("id"),
                        IMPORT_BATCH.TENANT_ORG_ID.as("tenantOrgId"),
                        IMPORT_BATCH.CATEGORY.as("category"),
                        IMPORT_BATCH.FILE_NAME.as("fileName"),
                        IMPORT_BATCH.TOTAL_RECORDS.as("totalRecords"),
                        IMPORT_BATCH.PROCESSED_RECORDS.as("processedRecords"),
                        IMPORT_BATCH.STATUS.as("status"),
                        IMPORT_BATCH.CREATED_ON.as("createdOn"),
                        IMPORT_BATCH.UPDATED_ON.as("updatedOn"),
                        IMPORT_BATCH.IS_ACTIVE.as("isActive"),
                        INDIVIDUAL.as("i1").EMAIL.as("createdBy"),
                        INDIVIDUAL.as("i2").EMAIL.as("updatedBy")
                )
                .from(IMPORT_BATCH)
                .join(INDIVIDUAL.as("i1")).on(IMPORT_BATCH.CREATED_BY.eq(INDIVIDUAL.as("i1").ID))
                .join(INDIVIDUAL.as("i2")).on(IMPORT_BATCH.UPDATED_BY.eq(INDIVIDUAL.as("i2").ID))
                .where(buildWhereCondition(userPrincipal, finalCondition, isSuperAdmin));

        if (sortFields.isEmpty()) {
            sortFields.add(IMPORT_BATCH.CREATED_ON.desc());
        }

        return query.orderBy(sortFields)
                .limit(paginationRequest.getPageSize())
                .offset((paginationRequest.getPage() - 1) * paginationRequest.getPageSize())
                .fetchInto(ImportBatchResponse.class);
    }


    public ImportBatch getById(UUID id) {
        return importBatchDao.fetchOneById(id);
    }
}
