package com.chidhagni.donationreceipt.donorsimport.importstaging;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.ImportStagingDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.donorsimport.constants.ImportStagingEnums;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportPaginationRequest;
import com.chidhagni.donationreceipt.donorsimport.dto.response.ImportStagingResponse;
import com.chidhagni.donationreceipt.donorsimport.importbatch.ImportBatchRepository;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.Tables.IMPORT_STAGING;

@Repository
@RequiredArgsConstructor
public class ImportStagingRepository {

    private final ImportStagingDao importStagingDao;
    private final DonorsRepository donorsRepository;
    private final ImportBatchRepository importBatchRepository;

    public List<ImportStagingResponse> fetchByImportBatchId(UUID batchId,
                                                            ImportPaginationRequest importPaginationRequest,
                                                            Condition finalCondition, List<SortField<?>> sortFields) {
        Integer pageNo = (importPaginationRequest.getPage() - 1) * importPaginationRequest.getPageSize();

        ImportBatch importBatch = importBatchRepository.getById(batchId);
        List<ImportStagingResponse> responses=importStagingDao.ctx().select(
                        IMPORT_STAGING.ID.as("id"),
                        IMPORT_STAGING.IMPORT_BATCH_ID.as("batchImportId"),
                        IMPORT_STAGING.CATEGORY.as("category"),
                        IMPORT_STAGING.NAME.as("name"),
                        IMPORT_STAGING.MOBILE_NUMBER.as("mobileNumber"),
                        IMPORT_STAGING.EMAIL.as("email"),
                        IMPORT_STAGING.PAN_NO.as("panNo"),
                        IMPORT_STAGING.STATUS.as("status"),
                        IMPORT_STAGING.META_DATA.as("donorMetaData")
                )
                .from(IMPORT_STAGING)
                .where(IMPORT_STAGING.IMPORT_BATCH_ID.eq(batchId)
                        .and(IMPORT_STAGING.IS_ACTIVE.eq(Boolean.TRUE))
                        .and(IMPORT_STAGING.STATUS.eq(ImportStagingEnums.PENDING.name())
                                .or(IMPORT_STAGING.STATUS.eq(ImportStagingEnums.DUPLICATED.name())))
                        .and(finalCondition != null ?
                        finalCondition : DSL.trueCondition()))
                .orderBy(sortFields)
                .limit(importPaginationRequest.getPageSize())
                .offset(pageNo)
                .fetchInto(ImportStagingResponse.class);


        for (ImportStagingResponse response : responses) {
            if (ImportStagingEnums.DUPLICATED.name().equals(response.getStatus())) {
                List<String> duplicatedFields = donorsRepository.getDuplicatedFields(
                        importBatch.getTenantOrgId(),
                        response.getEmail(),
                        response.getPanNo()
                );
                duplicatedFields.addAll(donorsRepository.getDuplicatesFromImportStaging(
                        importBatch.getTenantOrgId(),
                        response.getEmail(),
                        response.getPanNo()
                ));
                response.setDuplicatedFields(duplicatedFields);
            } else {
                response.setDuplicatedFields(Collections.emptyList());
            }
        }
        return responses;
    }


    public ImportStaging getById(UUID id) {
        return importStagingDao.fetchOneById(id);
    }

    public void update(ImportStaging importStaging) {
        try{
            importStagingDao.update(importStaging);
        }
        catch (Exception ex) {
            throw new InternalServerError("Exception occurred while updating the ImportStaging", ex);
        }
    }

    public boolean existsByEmailAndPanNoInBatch(UUID batchId, String email, String panNo) {
        try {
            return importStagingDao.ctx().selectCount()
                    .from(IMPORT_STAGING)
                    .where(IMPORT_STAGING.IMPORT_BATCH_ID.eq(batchId)
                            .and(IMPORT_STAGING.EMAIL.eq(email))
                            .or(panNo != null ? IMPORT_STAGING.PAN_NO.eq(panNo) : IMPORT_STAGING.PAN_NO.isNull()))
                    .fetchOneInto(Integer.class) > 0;
        }
        catch (Exception ex) {
            throw new InternalServerError("Exception occurred while updating the ImportStaging", ex);
        }
    }
}
