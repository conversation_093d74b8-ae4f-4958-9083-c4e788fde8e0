package com.chidhagni.donationreceipt.donorsimport.importstaging;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportPaginationRequest;
import com.chidhagni.donationreceipt.donorsimport.dto.response.GetAllImportStagingResponses;
import com.chidhagni.donationreceipt.donorsimport.dto.response.ImportStagingResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ImportStagingService {

    private final ImportStagingRepository importStagingRepository;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;


    public GetAllImportStagingResponses getAllImportStagingRecords(UUID batchId, ImportPaginationRequest paginationRequest) {
        applyDefaults(paginationRequest);
        Condition finalCondition = null;
        List<SortField<?>> sortFields = new ArrayList<>();

        List<ImportStagingResponse> importStagingResponses= importStagingRepository.
                fetchByImportBatchId(batchId,paginationRequest,finalCondition,sortFields);
        return GetAllImportStagingResponses.builder()
                .importStagingResponsesList(importStagingResponses)
                .rowCount(importStagingResponses.size())
                .build();
    }

    public void applyDefaults(ImportPaginationRequest paginationRequest) {
        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }
    }


    public void deactivateImportStagingById(UUID id, UserPrincipal userPrincipal)
    {
        ImportStaging importStaging=importStagingRepository.getById(id);
        if(importStaging==null)
        {
            throw new IllegalArgumentException("Import Staging doesn't exists");
        }
        importStaging.setIsActive(false);
        importStaging.setUpdatedBy(userPrincipal.getId());
        importStaging.setUpdatedOn(DateUtils.currentDatetime());
        importStagingRepository.update(importStaging);
    }
}
