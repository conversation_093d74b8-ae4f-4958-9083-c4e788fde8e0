package com.chidhagni.donationreceipt.donorsimport;


import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportDonorExcelDto;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportPaginationRequest;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportStagingPatchRequest;
import com.chidhagni.donationreceipt.donorsimport.dto.response.ApproveResponse;
import com.chidhagni.donationreceipt.donorsimport.dto.response.ErrorResponse;
import com.chidhagni.donationreceipt.donorsimport.dto.response.GetAllImportBatchResponses;
import com.chidhagni.donationreceipt.donorsimport.dto.response.GetAllImportStagingResponses;
import com.chidhagni.donationreceipt.donorsimport.importbatch.ImportBatchService;
import com.chidhagni.donationreceipt.donorsimport.importstaging.ImportStagingService;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/donor-import")
@RequiredArgsConstructor
@Slf4j
public class DonorImportExportController {

    private final DonorImportExportService donorImportExportService;
    private final ExcelParseDonorImportDto excelParseDonorImportDto;
    private final ImportBatchService importBatchService;
    private final ImportStagingService importStagingService;

    @GetMapping("/import-template")
    public ResponseEntity<byte[]> downloadTemplate(@RequestParam OrganisationEnums category,
                                                   @CurrentUser UserPrincipal userPrincipal) {

        try {
            ByteArrayInputStream in = donorImportExportService.createImportTemplateFile(userPrincipal);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(
                    MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            headers.set(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=" + category.name().toLowerCase() + "_" + "import_template");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(in.readAllBytes());
        } catch (IllegalStateException | IOException e) {
            return ResponseEntity.status(500)
                    .body(("{\"error\": \"Server error: " + e.getMessage() + "\"}").getBytes());
        }
    }


    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> uploadExcel(@RequestParam("file") MultipartFile file, @CurrentUser UserPrincipal userPrincipal) {
        if (file.isEmpty() || !Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xlsx")) {
            return ResponseEntity.badRequest().body("Please upload a valid Excel file.");
        }
        try {
            List<ImportDonorExcelDto> rows = excelParseDonorImportDto.parseExcel(file.getInputStream());

            donorImportExportService.insertIntoImportStagingAndImportBatch(rows, userPrincipal);
            return ResponseEntity.ok("Data imported successfully.");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Failed to import data: " + e.getMessage());
        }
    }


    @PostMapping("/import-batch/all")
    public ResponseEntity<GetAllImportBatchResponses> getAllImportBatch(
            @RequestBody(required = false) ImportPaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = ImportPaginationRequest.builder().build();
        }
        return new ResponseEntity<>(importBatchService.getAllImportBatch(paginationRequest, userPrincipal), HttpStatus.OK);
    }


    @PostMapping("/import-staging/all/{batchId}")
    public ResponseEntity<GetAllImportStagingResponses> getAllImportStagingRecords(
            @PathVariable UUID batchId,
            @RequestBody(required = false) ImportPaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = ImportPaginationRequest.builder().build();
        }
        return new ResponseEntity<>(importStagingService.getAllImportStagingRecords(batchId, paginationRequest), HttpStatus.OK);
    }

    @PatchMapping("/patch-import-staging/{id}")
    public ResponseEntity<UUID> patchImportStaging(
            @PathVariable UUID id,
            @RequestBody ImportStagingPatchRequest patchRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        UUID updatedId = donorImportExportService.patchImportStaging(id, patchRequest, userPrincipal);
        return new ResponseEntity<>(updatedId, HttpStatus.OK);
    }


    @PostMapping("/approved-donors")
    public ResponseEntity<?> saveToDonors(
            @RequestBody List<UUID> stagingIds,
            @CurrentUser UserPrincipal userPrincipal) {
        try {
            int processedCount = donorImportExportService.saveApprovedDonorsByStagingIds(stagingIds, userPrincipal);
            if (processedCount == 0) {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ErrorResponse("No approved records found for batch"));
            }
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApproveResponse(processedCount, "Successfully processed "
                            + processedCount + " approved records"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ErrorResponse(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("Server error: " + e.getMessage()));
        }
    }

    @PostMapping(value = "/export-donors", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> exportDonors(@RequestBody List<UUID> donorIds,@CurrentUser UserPrincipal userPrincipal) {
        try {
            byte[] excelData = donorImportExportService.exportDonorsToExcel(donorIds);
            if (excelData.length == 0) {
                return ResponseEntity.status(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(new ErrorResponse("No donor records found for the tenant"));
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "donors_export_" + LocalDate.now()+".xlsx");
            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(new ErrorResponse(e.getMessage()));
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(new ErrorResponse("Failed to process Excel file: " + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(new ErrorResponse("Server error: " + e.getMessage()));
        }
    }

    @DeleteMapping("/deactivate-import-staging/{id}")
    public void deactivateImportStagingById(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
        importStagingService.deactivateImportStagingById(id, userPrincipal);
    }
}
