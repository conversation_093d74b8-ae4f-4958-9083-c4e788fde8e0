package com.chidhagni.donationreceipt.donorsimport;

import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportDonorExcelDto;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class ExcelParseDonorImportDto {


    public List<ImportDonorExcelDto> parseExcel(InputStream inputStream) throws Exception {
        List<ImportDonorExcelDto> rows = new ArrayList<>();
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                log.info("Checking row: {}", row.getRowNum());
                if (row.getRowNum() == 0) continue;

                try {
                    ImportDonorExcelDto data = new ImportDonorExcelDto();
                    data.setName(getCellValue(row.getCell(1)));
                    data.setEmail(getCellValue(row.getCell(2)));
                    data.setMobileNumber(getCellValue(row.getCell(3)));
                    data.setPanNo(getCellValue(row.getCell(4)));
                    data.setState(getCellValue(row.getCell(5)));
                    data.setPinCode(getCellValue(row.getCell(6)));
                    data.setAddress(getCellValue(row.getCell(7)));
                    rows.add(data);
                    log.info(" rows{} ", rows.size());
                } catch (Exception e) {
                    log.info(String.valueOf(row.getCell(2)));
                    log.error("Skipping row {} due to error: {}", row.getRowNum(), e.getMessage());
                }
            }
        }
        return rows;
    }

    private String getCellValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return null;
            default:
                return null;
        }
    }
}
