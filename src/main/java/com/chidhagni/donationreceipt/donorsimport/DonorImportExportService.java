package com.chidhagni.donationreceipt.donorsimport;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportBatchRecord;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;
import com.chidhagni.donationreceipt.donorsimport.constants.ImportBatchEnums;
import com.chidhagni.donationreceipt.donorsimport.constants.ImportStagingEnums;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportDonorExcelDto;
import com.chidhagni.donationreceipt.donorsimport.dto.request.ImportStagingPatchRequest;
import com.chidhagni.donationreceipt.donorsimport.importbatch.ImportBatchRepository;
import com.chidhagni.donationreceipt.donorsimport.importstaging.ImportStagingRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.services.listvalues.ListValuesRepository;
import com.chidhagni.utils.Aes256EncryptionUtils;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.EncryptedData;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFName;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Donors.DONORS;
import static com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch.IMPORT_BATCH;
import static com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging.IMPORT_STAGING;

@Service
@RequiredArgsConstructor
@Slf4j
public class DonorImportExportService {


    private final IndividualRoleRepository individualRoleRepository;
    private final OrganizationRepository organizationRepository;
    private final ListValuesRepository listValuesRepository;
    private final DSLContext dsl;
    private final DonorsRepository donorsRepository;
    private final ObjectMapper objectMapper;
    private final ImportStagingRepository importStagingRepository;
    private final ImportBatchRepository importBatchRepository;
    private final Aes256EncryptionUtils encryptionUtils;

    @Value("${listnames.id.states}")
    private UUID stateListNamesId;


    public ByteArrayInputStream createImportTemplateFile(UserPrincipal userPrincipal) throws IOException {
        List<String> stateValues = listValuesRepository.
                getListValuesStringByListNameId(stateListNamesId);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Import_Donors");

            Sheet hiddenSheet = workbook.createSheet("Hidden_States");
            workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);

            for (int i = 0; i < stateValues.size(); i++) {
                Row row = hiddenSheet.createRow(i);
                Cell cell = row.createCell(0);
                cell.setCellValue(stateValues.get(i));
            }

            XSSFName stateName = (XSSFName) workbook.createName();
            stateName.setNameName("StateList");
            String stateRange = String.format("Hidden_States!$A$1:$A$%d", stateValues.size());
            stateName.setRefersToFormula(stateRange);

            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);

            String[] headers = {
                    "S.No", "Name", "Email", "Mobile Number",
                    "Pan No", "State", "Pincode", "Address"
            };
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            Row sampleRow = sheet.createRow(1);
            sampleRow.createCell(0).setCellValue(1); // S.No

            DataValidationHelper validationHelper = sheet.getDataValidationHelper();
            DataValidationConstraint stateConstraint = validationHelper.createFormulaListConstraint("StateList");
            CellRangeAddressList stateAddressList = new CellRangeAddressList(1, 1000, 5, 5);
            DataValidation stateValidation = validationHelper.createValidation(stateConstraint, stateAddressList);
            stateValidation.setShowErrorBox(true);
            stateValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            stateValidation.createErrorBox("Invalid State", "Please select a state from the dropdown list.");
            sheet.addValidationData(stateValidation);


            DataValidationConstraint mobileConstraint = validationHelper.createCustomConstraint(
                    "AND(ISNUMBER(D2),LEN(D2)=10)"
            );
            CellRangeAddressList mobileAddressList = new CellRangeAddressList(1, 1000, 3, 3); // Column D (Mobile Number)
            DataValidation mobileValidation = validationHelper.createValidation(mobileConstraint, mobileAddressList);
            mobileValidation.setShowErrorBox(true);
            mobileValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            mobileValidation.createErrorBox("Invalid Mobile Number", "Contact number must be exactly 10 digits.");
            sheet.addValidationData(mobileValidation);


            // Email validation (basic email format)
            DataValidationConstraint emailConstraint = validationHelper.createCustomConstraint(
                    "AND(ISNUMBER(FIND(\"@\",C2)),ISNUMBER(FIND(\".\",C2,FIND(\"@\",C2))),LEN(C2)-FIND(\".\",C2,FIND(\"@\",C2))>=2)"
            );
            CellRangeAddressList emailAddressList = new CellRangeAddressList(1, 1000, 2, 2); // Column C (Email)
            DataValidation emailValidation = validationHelper.createValidation(emailConstraint, emailAddressList);
            emailValidation.setShowErrorBox(true);
            emailValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            emailValidation.createErrorBox("Invalid Email", "Enter a valid email address (e.g., <EMAIL>).");
            sheet.addValidationData(emailValidation);

            // Pan No validation (5 letters, 4 digits, 1 letter)
            DataValidationConstraint panConstraint = validationHelper.createCustomConstraint(
                    "AND(LEN(E2)=10,EXACT(UPPER(LEFT(E2,5)),LEFT(E2,5)),ISNUMBER(VALUE(MID(E2,6,4)))," +
                            "EXACT(UPPER(RIGHT(E2,1)),RIGHT(E2,1)))"
            );
            CellRangeAddressList panAddressList = new CellRangeAddressList(1, 1000, 4, 4);
            DataValidation panValidation = validationHelper.createValidation(panConstraint, panAddressList);
            panValidation.setShowErrorBox(true);
            panValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            panValidation.createErrorBox("Invalid PAN Number", "PAN number must be 10 characters:" +
                    " first 5 uppercase letters, 4 digits, 1 uppercase letter (e.g., **********).");
            sheet.addValidationData(panValidation);


            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                workbook.write(out);
                return new ByteArrayInputStream(out.toByteArray());
            }
        } catch (IOException e) {
            throw new IOException("Failed to create Excel template: " + e.getMessage(), e);
        }
    }


    public void insertIntoImportStagingAndImportBatch(List<ImportDonorExcelDto> rows, UserPrincipal userPrincipal) {

        if (rows == null || rows.isEmpty()) {
            log.error("No rows provided for import");
            throw new IllegalArgumentException("No rows provided for import");
        }
        List<IndividualRole> individualRoleList = individualRoleRepository.getByIndivdiualId(userPrincipal.getId());
        if (individualRoleList.isEmpty()) {
            log.error("No roles found for user: {}", userPrincipal.getId());
            throw new IllegalStateException("No roles found for user");
        }

        Organisation organisation = organizationRepository.getById(individualRoleList.get(0).getOrgId());
        if (organisation == null) {
            log.error("Organisation not found for orgId: {}", individualRoleList.get(0).getOrgId());
            throw new IllegalStateException("Organisation not found");
        }
        UUID tenantOrgId = organisation.getId();
        dsl.transaction(configuration -> {
            DSLContext tx = DSL.using(configuration);

            UUID batchId = UUID.randomUUID();
            String fileName = "donors_import_" + LocalDateTime.now().toString().replace(":", "-") + ".xlsx";
            tx.insertInto(IMPORT_BATCH)
                    .set(IMPORT_BATCH.ID, batchId)
                    .set(IMPORT_BATCH.CATEGORY, OrganisationEnums.DONOR.name())
                    .set(IMPORT_BATCH.TENANT_ORG_ID, tenantOrgId)
                    .set(IMPORT_BATCH.FILE_NAME, fileName)
                    .set(IMPORT_BATCH.TOTAL_RECORDS, 0)
                    .set(IMPORT_BATCH.PROCESSED_RECORDS, 0)
                    .set(IMPORT_BATCH.STATUS, ImportBatchEnums.PENDING.name())
                    .set(IMPORT_BATCH.CREATED_BY, userPrincipal.getId())
                    .set(IMPORT_BATCH.CREATED_ON, DateUtils.currentTimeIST())
                    .set(IMPORT_BATCH.UPDATED_BY, userPrincipal.getId())
                    .set(IMPORT_BATCH.UPDATED_ON, DateUtils.currentTimeIST())
                    .execute();

            for (ImportDonorExcelDto row : rows) {
                try {
                    if (row.getEmail() == null || row.getEmail().isBlank()) {
                        log.warn("Skipping row with missing email");
                        continue;
                    }

                    boolean isDuplicate = donorsRepository.existsByTenantOrgIdAndEmailAndPanNo(tenantOrgId,
                            row.getEmail(), row.getPanNo());

                    boolean isDuplicateInImportBatch = importStagingRepository.existsByEmailAndPanNoInBatch(batchId,
                            row.getEmail(), row.getPanNo());

                    DonorMetaData donorMetaData = new DonorMetaData();
                    donorMetaData.setAddress(row.getAddress());
                    donorMetaData.setState(row.getState());
                    donorMetaData.setPinCode(row.getPinCode());

                    JSONB metaData;
                    try {
                        metaData = JSONB.valueOf(objectMapper.writeValueAsString(donorMetaData));
                    } catch (Exception e) {
                        log.error("Failed to serialize meta_data for email {}: {}", row.getEmail(), e.getMessage());
                        continue;
                    }


                    // Encrypt the PAN number if present
                    String panNo = row.getPanNo();
                    String panNoNonce = null;
                    if (panNo != null && !panNo.isEmpty()) {
                        EncryptedData encryptedPan = encryptionUtils.encrypt(panNo);
                        panNo=encryptedPan.getCiphertext();
                        panNoNonce=encryptedPan.getNonce();
                    } else {
                        panNo=null;
                        panNoNonce=null;

                    }

                    tx.insertInto(IMPORT_STAGING)
                            .set(IMPORT_STAGING.ID, UUID.randomUUID())
                            .set(IMPORT_STAGING.IMPORT_BATCH_ID, batchId)
                            .set(IMPORT_STAGING.CATEGORY, OrganisationEnums.DONOR.name())
                            .set(IMPORT_STAGING.NAME, row.getName())
                            .set(IMPORT_STAGING.EMAIL, row.getEmail())
                            .set(IMPORT_STAGING.PAN_NO, row.getPanNo())
                            .set(IMPORT_STAGING.ENCRYPTED_PAN_NO, panNo)
                            .set(IMPORT_STAGING.PAN_NO_NONCE, panNoNonce)
                            .set(IMPORT_STAGING.MOBILE_NUMBER, row.getMobileNumber())
                            .set(IMPORT_STAGING.STATUS, isDuplicate || isDuplicateInImportBatch ? ImportStagingEnums.DUPLICATED.name()
                                    : ImportStagingEnums.PENDING.name())
                            .set(IMPORT_STAGING.META_DATA, metaData)
                            .set(IMPORT_STAGING.CREATED_BY, userPrincipal.getId())
                            .set(IMPORT_STAGING.CREATED_ON, LocalDateTime.now())
                            .set(IMPORT_STAGING.UPDATED_BY, userPrincipal.getId())
                            .set(IMPORT_STAGING.UPDATED_ON, LocalDateTime.now())
                            .execute();


                    int totalRecords = tx.selectCount()
                            .from(IMPORT_STAGING)
                            .where(IMPORT_STAGING.IMPORT_BATCH_ID.eq(batchId))
                            .fetchOneInto(Integer.class);
                    log.info("Total records in batch: {}", totalRecords);
                    tx.update(IMPORT_BATCH)
                            .set(IMPORT_BATCH.TOTAL_RECORDS, totalRecords)
                            .set(IMPORT_BATCH.UPDATED_BY, userPrincipal.getId())
                            .set(IMPORT_BATCH.UPDATED_ON, DateUtils.currentTimeIST())
                            .where(IMPORT_BATCH.ID.eq(batchId))
                            .execute();

                } catch (Exception e) {
                    log.error("Failed to insert row with email {}: {}", row.getEmail(), e.getMessage());
                }
            }
        });
    }

    public UUID patchImportStaging(UUID id, ImportStagingPatchRequest patchRequest, UserPrincipal userPrincipal) {
        ImportStaging importStaging = importStagingRepository.getById(id);
        if (importStaging == null) {
            log.error("No import staging found for id: {}", id);
            throw new IllegalStateException("No import staging found");
        }
        ImportBatch importBatch = importBatchRepository.getById(importStaging.getImportBatchId());

        boolean emailUpdated = patchRequest.getEmail() != null;
        boolean panNoUpdated = patchRequest.getPanNo() != null;

        if (emailUpdated) {
            importStaging.setEmail(patchRequest.getEmail());
        }
        if (patchRequest.getName() != null) {
            importStaging.setName(patchRequest.getName());
        }
        if (patchRequest.getMobileNumber() != null) {
            importStaging.setMobileNumber(patchRequest.getMobileNumber());
        }
        if (panNoUpdated) {
            importStaging.setPanNo(patchRequest.getPanNo());
        }
        if (panNoUpdated) {
            EncryptedData encryptedPan = encryptionUtils.encrypt(patchRequest.getPanNo());
            importStaging.setEncryptedPanNo(encryptedPan.getCiphertext());
            importStaging.setPanNoNonce(encryptedPan.getNonce());
        }

        if (patchRequest.getDonorMetaData() != null) {
            DonorMetaData donorPatch = patchRequest.getDonorMetaData();
            JSONB donorMetaData = importStaging.getMetaData();
            if (donorMetaData == null) {
                donorMetaData = JSONB.valueOf("{}");
            }
            try {
                DonorMetaData existingDonorMetaData = objectMapper.readValue(donorMetaData.toString(), DonorMetaData.class);
                if (donorPatch.getAddress() != null) {
                    existingDonorMetaData.setAddress(donorPatch.getAddress());
                }
                if (donorPatch.getPinCode() != null) {
                    existingDonorMetaData.setPinCode(donorPatch.getPinCode());
                }
                if (donorPatch.getState() != null) {
                    existingDonorMetaData.setState(donorPatch.getState());
                }
                donorMetaData = JSONB.valueOf(objectMapper.writeValueAsString(existingDonorMetaData));
            } catch (Exception e) {
                log.error("Failed to deserialize meta_data for staging id {}: {}", id, e.getMessage());
            }
            importStaging.setMetaData(donorMetaData);
        }
        if (emailUpdated || panNoUpdated) {
            String emailToCheck = importStaging.getEmail();
            String panNoToCheck = importStaging.getPanNo();
            UUID tenantOrgId = importBatch.getTenantOrgId();

            if (emailToCheck != null && panNoToCheck != null && tenantOrgId != null) {
                boolean isDuplicate = donorsRepository.existsByTenantOrgIdAndEmailAndPanNo(
                        tenantOrgId, emailToCheck, panNoToCheck
                );

                if (isDuplicate && !importStaging.getStatus().equals(ImportStagingEnums.DUPLICATED.name())) {
                    importStaging.setStatus(ImportStagingEnums.DUPLICATED.name());
                } else if (!isDuplicate && importStaging.getStatus().equals(ImportStagingEnums.DUPLICATED.name())) {
                    importStaging.setStatus(ImportStagingEnums.PENDING.name());
                }
            }
        }

        importStaging.setUpdatedBy(userPrincipal.getId());
        importStaging.setUpdatedOn(DateUtils.currentTimeIST());
        importStagingRepository.update(importStaging);
        return id;
    }


    public Integer saveApprovedDonorsByStagingIds(List<UUID> stagingIds, UserPrincipal userPrincipal) {
        if (stagingIds == null || stagingIds.isEmpty()) {
            throw new IllegalArgumentException("Staging ID list cannot be empty");
        }
        return dsl.transactionResult(configuration -> {
            DSLContext tx = DSL.using(configuration);

            List<Record> approvedRecords = tx.select()
                    .from(IMPORT_STAGING)
                    .where(IMPORT_STAGING.ID.in(stagingIds))
                    .fetch();
            if (approvedRecords.isEmpty()) {
                log.info("No approved records found for provided staging IDs");
                return 0;
            }

            UUID batchId = approvedRecords.get(0).get(IMPORT_STAGING.IMPORT_BATCH_ID);

            ImportBatchRecord importBatch = tx.selectFrom(IMPORT_BATCH)
                    .where(IMPORT_BATCH.ID.eq(batchId))
                    .fetchOne();
            if (importBatch == null) {
                log.error("Import batch not found for batchId: {}", batchId);
                throw new IllegalStateException("Import batch not found");
            }
            UUID tenantOrgId = importBatch.getTenantOrgId();

            int processedCount = 0;
            for (Record record : approvedRecords) {
                try {
                    DonorMetaData metaData;
                    try {
                        metaData = objectMapper.readValue(record.get(IMPORT_STAGING.META_DATA, String.class),
                                DonorMetaData.class);
                    } catch (Exception e) {
                        log.error("Failed to deserialize meta_data for staging id {}: {}",
                                record.get(IMPORT_STAGING.ID), e.getMessage());
                        continue;
                    }

                    tx.update(IMPORT_STAGING)
                            .set(IMPORT_STAGING.STATUS, ImportStagingEnums.APPROVED.name())
                            .set(IMPORT_STAGING.UPDATED_BY, userPrincipal.getId())
                            .set(IMPORT_STAGING.UPDATED_ON, LocalDateTime.now())
                            .where(IMPORT_STAGING.ID.eq(record.get(IMPORT_STAGING.ID)))
                            .execute();

                    tx.insertInto(DONORS)
                            .set(DONORS.ID, UUID.randomUUID())
                            .set(DONORS.NAME, record.get(IMPORT_STAGING.NAME))
                            .set(DONORS.TENANT_ORG_ID, tenantOrgId)
                            .set(DONORS.EMAIL, record.get(IMPORT_STAGING.EMAIL))
                            .set(DONORS.MOBILE_NUMBER, record.get(IMPORT_STAGING.MOBILE_NUMBER))
                            .set(DONORS.PAN_NO, record.get(IMPORT_STAGING.PAN_NO))
                            .set(DONORS.ENCRYPTED_PAN_NO, record.get(IMPORT_STAGING.ENCRYPTED_PAN_NO))
                            .set(DONORS.PAN_NO_NONCE, record.get(IMPORT_STAGING.PAN_NO_NONCE))
                            .set(DONORS.META_DATA, metaData)
                            .set(DONORS.IS_ACTIVE, Boolean.TRUE)
                            .set(DONORS.CREATED_BY, userPrincipal.getId())
                            .set(DONORS.CREATED_ON, DateUtils.currentTimeIST())
                            .set(DONORS.UPDATED_BY, userPrincipal.getId())
                            .set(DONORS.UPDATED_ON, DateUtils.currentTimeIST())
                            .execute();

                    processedCount++;
                } catch (Exception e) {
                    log.error("Failed to insert donor for staging id {}: {}", record.get(IMPORT_STAGING.ID), e.getMessage());
                }
            }

            Integer totalProcessedCount = importBatch.getProcessedRecords() + processedCount;
            if (totalProcessedCount > importBatch.getTotalRecords()) {
                log.error("Total processed count {} exceeds total records {} for batchId: {}", totalProcessedCount, importBatch.getTotalRecords(), batchId);
                throw new IllegalStateException("Processed records cannot exceed total records");
            }

            if (totalProcessedCount.equals(importBatch.getTotalRecords())) {
                tx.update(IMPORT_BATCH)
                        .set(IMPORT_BATCH.STATUS, ImportBatchEnums.COMPLETED.name())
                        .set(IMPORT_BATCH.PROCESSED_RECORDS, totalProcessedCount)
                        .set(IMPORT_BATCH.UPDATED_BY, userPrincipal.getId())
                        .set(IMPORT_BATCH.UPDATED_ON, LocalDateTime.now())
                        .where(IMPORT_BATCH.ID.eq(batchId))
                        .execute();
            } else {
                tx.update(IMPORT_BATCH)
                        .set(IMPORT_BATCH.STATUS, ImportBatchEnums.PARTIAL.name())
                        .set(IMPORT_BATCH.PROCESSED_RECORDS, importBatch.getProcessedRecords() + processedCount)
                        .set(IMPORT_BATCH.UPDATED_BY, userPrincipal.getId())
                        .set(IMPORT_BATCH.UPDATED_ON, LocalDateTime.now())
                        .where(IMPORT_BATCH.ID.eq(batchId))
                        .execute();
            }

            log.info("Processed {} approved records for batchId: {}", processedCount, batchId);
            return processedCount;
        });
    }

    public byte[] exportDonorsToExcel(List<UUID> donorIds) throws IOException {
        if (donorIds == null || donorIds.isEmpty()) {
            throw new IllegalArgumentException("Staging ID list cannot be empty");
        }
        List<Record> donorRecords = dsl.select()
                .from(DONORS)
                .where(DONORS.ID.in(donorIds))
                .fetch();

        if (donorRecords.isEmpty()) {
            log.info("No donor records found for staging IDs: {}", donorIds);
            return new byte[0];
        }

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Exported_DONORs");
            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);

            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("Name");
            headerRow.createCell(1).setCellValue("Email");
            headerRow.createCell(2).setCellValue("Mobile Number");
            headerRow.createCell(3).setCellValue("Pan No");
            headerRow.createCell(4).setCellValue("State");
            headerRow.createCell(5).setCellValue("Pincode");
            headerRow.createCell(6).setCellValue("Address");

            int rowNum = 1;
            for (Record record : donorRecords) {
                String encryptedPan=null;
                String panNoNonce=null;
                String decryptedPan;
                encryptedPan=record.get(DONORS.ENCRYPTED_PAN_NO);
                panNoNonce=record.get(DONORS.PAN_NO_NONCE);
                if (encryptedPan != null && panNoNonce != null) {
                    decryptedPan = encryptionUtils.decrypt(encryptedPan, panNoNonce);
                } else {
                    decryptedPan = null;
                }
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(record.get(DONORS.NAME));
                row.createCell(1).setCellValue(record.get(DONORS.EMAIL));
                row.createCell(2).setCellValue(record.get(DONORS.MOBILE_NUMBER));
                row.createCell(3).setCellValue(decryptedPan);
                row.createCell(4).setCellValue(record.get(DONORS.META_DATA, DonorMetaData.class).getState());
                row.createCell(5).setCellValue(record.get(DONORS.META_DATA, DonorMetaData.class).getPinCode());
                row.createCell(6).setCellValue(record.get(DONORS.META_DATA, DonorMetaData.class).getAddress());
            }

            for (int i = 0; i < 7; i++) {
                sheet.autoSizeColumn(i);
            }
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            log.error("Failed to generate Excel file: {}", e.getMessage());
            throw new RuntimeException("Failed to generate Excel file", e);
        }
    }
}
