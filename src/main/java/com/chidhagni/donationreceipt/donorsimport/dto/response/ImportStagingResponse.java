package com.chidhagni.donationreceipt.donorsimport.dto.response;


import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportStagingResponse {

    private UUID id;
    private UUID batchImportId;
    private String category;
    private String name;
    private String mobileNumber;
    private String email;
    private String panNo;
    private String status;
    private DonorMetaData donorMetaData;
    private List<String> duplicatedFields;
    private UUID tenantOrgId;
}
