package com.chidhagni.donationreceipt.donorsimport.dto.request;


import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportDonorExcelDto {

    private String name;
    private String email;
    private String mobileNumber;
    private OrganisationEnums category;
    private String organisationName;
    private String panNo;
    private String state;
    private String address;
    private String pinCode;
    private String status;
}
