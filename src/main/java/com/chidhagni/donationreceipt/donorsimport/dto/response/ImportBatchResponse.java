package com.chidhagni.donationreceipt.donorsimport.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportBatchResponse {

    private UUID id;
    private String fileName;
    private Integer totalRecords;
    private Integer processedRecords;
    private String status;
    private String createdBy;
    private String updatedBy;
    private String isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
