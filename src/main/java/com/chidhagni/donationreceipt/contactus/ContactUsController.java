package com.chidhagni.donationreceipt.contactus;

import com.chidhagni.donationreceipt.contactus.dto.request.ContactUsRequestDTO;
import com.chidhagni.donationreceipt.contactus.dto.response.ContactUsResponseDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.mail.MessagingException;
import jakarta.validation.Valid;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping("/auth/contact-us")
public class ContactUsController {
    private final ContactUsService contactUsService;

    public ContactUsController(ContactUsService contactUsService) {
        this.contactUsService = contactUsService;
    }

    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<ContactUsResponseDTO> create(@RequestBody @Valid ContactUsRequestDTO contactUsRequestDTO) throws MessagingException {
        UUID id = contactUsService.create(contactUsRequestDTO);
        return ResponseEntity.ok(ContactUsResponseDTO.builder().id(id).build());
    }


}
