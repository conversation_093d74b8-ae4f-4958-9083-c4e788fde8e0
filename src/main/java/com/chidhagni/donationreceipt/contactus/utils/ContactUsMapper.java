package com.chidhagni.donationreceipt.contactus.utils;

import com.chidhagni.donationreceipt.contactus.dto.request.ContactUsRequestDTO;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ContactUsMapper {

    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "contactNumber", source = "contactNumber")
    @Mapping(target = "message", source = "message")
    @Mapping(target = "ipAddress", source = "ipAddress")
    @Mapping(target = "createdOn", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "updatedOn", expression = "java(java.time.LocalDateTime.now())")
    ContactUs contactUsDtoToContactUs(ContactUsRequestDTO contactUsRequestDTO);
}
