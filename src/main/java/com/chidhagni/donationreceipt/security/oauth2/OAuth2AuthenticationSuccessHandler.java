package com.chidhagni.donationreceipt.security.oauth2;

import com.chidhagni.config.AppProperties;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualSessionsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.security.service.TokenProvider;
import com.chidhagni.utils.CookieUtils;
import com.chidhagni.utils.JwtTokenUtil;
import com.chidhagni.utils.Token;
import com.chidhagni.web.BadRequestException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.chidhagni.donationreceipt.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME;

@Component
@RequiredArgsConstructor
public class OAuth2AuthenticationSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    private final TokenProvider tokenProvider;
    private final JwtTokenUtil jwtTokenUtil;
    private final AppProperties appProperties;
    private final IndividualDao individualDao;
    private final IndividualRoleDao individualRoleDao;
    private final IndividualSessionsDao userSessionsDao;
    private final HttpCookieOAuth2AuthorizationRequestRepository httpCookieOAuth2AuthorizationRequestRepository;


    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException, IOException {
        String targetUrl = determineTargetUrl(request, response, authentication);

        if (response.isCommitted()) {
            logger.debug("Response has already been committed. Unable to redirect to " + targetUrl);
            return;
        }

        clearAuthenticationAttributes(request, response);
        getRedirectStrategy().sendRedirect(request, response, targetUrl);
    }

    protected String determineTargetUrl(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        Optional<String> redirectUri = CookieUtils.getCookie(request, REDIRECT_URI_PARAM_COOKIE_NAME)
                .map(cookie -> cookie.getValue());

        if (redirectUri.isPresent() && !isAuthorizedRedirectUri(redirectUri.get())) {
            throw new BadRequestException("Sorry! We've got an Unauthorized Redirect URI and can't proceed with the authentication");
        }

        // Assuming UserPrincipal contains necessary information to retrieve Users object
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        // Retrieve the Users object based on information available in UserPrincipal
        Individual individual = individualDao.fetchOneById(userPrincipal.getId());
        List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(individual.getId());
        UUID orgId = individualRoles.get(0).getOrgId();

        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        } else {
            ipAddress = ipAddress.split(",")[0];
        }


        String accessToken = jwtTokenUtil.generateAccessToken(individual, orgId);

        String refreshToken = jwtTokenUtil.generateRefreshToken(individual, orgId);

        String targetUrl = UriComponentsBuilder.fromUriString(redirectUri.orElse(getDefaultTargetUrl()))
                .queryParam("accessToken", accessToken)
                .queryParam("refreshToken", refreshToken)
                .build().toUriString();

        Token token =
                Token.builder()
                        .accessToken(jwtTokenUtil.generateAccessToken(individual, orgId))
                        .refreshToken(jwtTokenUtil.generateRefreshToken(individual, orgId))
                        .build();

        IndividualSessions userSession = buildSession(userPrincipal.getId(), ipAddress, token);
        userSessionsDao.insert(userSession);

        return targetUrl;
    }

    protected void clearAuthenticationAttributes(HttpServletRequest request, HttpServletResponse response) {
        super.clearAuthenticationAttributes(request);
        httpCookieOAuth2AuthorizationRequestRepository.removeAuthorizationRequestCookies(request, response);
    }

    private boolean isAuthorizedRedirectUri(String uri) {
        URI clientRedirectUri = URI.create(uri);

        return appProperties.getOauth2().getAuthorizedRedirectUris()
                .stream()
                .anyMatch(authorizedRedirectUri -> {
                    // Only validate host and port. Let the clients use different paths if they want to
                    URI authorizedURI = URI.create(authorizedRedirectUri);
                    if (authorizedURI.getHost().equalsIgnoreCase(clientRedirectUri.getHost())
                            && authorizedURI.getPort() == clientRedirectUri.getPort()) {
                        return true;
                    }
                    return false;
                });
    }

    public IndividualSessions buildSession(UUID individualId, String ipAddress, Token token) {
        IndividualSessions userSessions = new IndividualSessions();

        userSessions.setId(UUID.randomUUID());
        userSessions.setIndividualId(individualId);

        userSessions.setAccesstoken(token.getAccessToken());
        userSessions.setAccesstokenExpirytime(jwtTokenUtil.getExpiryTimestamp(token.getAccessToken()));
        userSessions.setAccesstokenGeneratedOn(jwtTokenUtil.getIssuedAtTimestamp(token.getAccessToken()));

        userSessions.setRefreshtoken(token.getRefreshToken());
        userSessions.setRefreshtokenExpirytime(jwtTokenUtil.getExpiryTimestamp(token.getRefreshToken()));
        userSessions.setRefreshtokenGeneratedOn(jwtTokenUtil.getIssuedAtTimestamp(token.getRefreshToken()));

        userSessions.setIpaddress(ipAddress);

        return userSessions;
    }
}
