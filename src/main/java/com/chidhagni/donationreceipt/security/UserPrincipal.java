package com.chidhagni.donationreceipt.security;

//import com.chidhagni.donation_receipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.utils.DateUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.time.LocalDateTime;
import java.util.*;


public class UserPrincipal implements OAuth2User, UserDetails {
    private UUID id;
    private String email;
    private String password;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private Collection<? extends GrantedAuthority> authorities;

    private Map<String, Object> attributes;

    public UserPrincipal(UUID id, String email, String password, LocalDateTime createdOn,
                         LocalDateTime updatedOn, Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.email = email;
        this.password = password;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.authorities = authorities;
    }

    public UserPrincipal(UUID id) {
        this.id=id;
    }

    public UserPrincipal(UUID id, String email) {
        this.id=id;
        this.email=email;
    }


    public static UserPrincipal create(Individual individual, List<GrantedAuthority> userAuthorities) {

        return new UserPrincipal(
                individual.getId(),
                individual.getEmail(),
                individual.getPassword(),
                individual.getCreatedOn(),
                individual.getUpdatedOn(),
                userAuthorities
        );
    }

    public static UserPrincipal create(Individual individual, Map<String, Object> attributes) {
        List<GrantedAuthority> authorities = Collections.
                singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        UserPrincipal userPrincipal = UserPrincipal.create(individual, authorities);
        userPrincipal.createdOn = DateUtils.currentDatetime();
        userPrincipal.updatedOn = DateUtils.currentDatetime();
        userPrincipal.setAttributes(attributes);
        return userPrincipal;
    }

    public String getEmail() {
        return email;
    }

    public UUID getId() {
        return id;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return email;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public <A> A getAttribute(String name) {
        return OAuth2User.super.getAttribute(name);
    }


    @Override
    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getName() {
        return String.valueOf(id);
    }
}
