package com.chidhagni.donationreceipt.resources;



import com.chidhagni.donationreceipt.db.jooq.tables.daos.ResourceDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Resource;
import com.chidhagni.donationreceipt.resources.constants.ResourceTypeEnum;
import com.chidhagni.donationreceipt.resources.dto.ResourceDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.tables.Resource.RESOURCE;



@Repository
@RequiredArgsConstructor
@Slf4j
public class ResourceRepository {

    private final ResourceDao resourceDao;


    public void createResource(Resource resource) {
        try {
            resourceDao.insert(resource);
            log.info("Resource created successfully");
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while inserting Resource", ex);
        }
    }


    public ResourceDTO getCompleteTree(UUID parentId) throws JsonProcessingException {
        log.info("Starting to build the complete tree for parent ID: {}", parentId);

        ResourceDTO resourceDTO = resourceDao.ctx().select(
                        RESOURCE.ID.as("id"),
                        RESOURCE.NAME.as("name"),
                        RESOURCE.DESCRIPTION.as("description"),
                        RESOURCE.TYPE.as("type"),
                        RESOURCE.PARENT_RESOURCE_ID.as("parentResourceId"),
                        RESOURCE.IS_ACTIVE.as("isActive"),
                        RESOURCE.CREATED_BY,
                        RESOURCE.UPDATED_BY,
                        RESOURCE.CREATED_ON,
                        RESOURCE.UPDATED_ON
                ).from(RESOURCE)
                .where(RESOURCE.ID.eq(parentId))
                .fetchOneInto(ResourceDTO.class);

        List<ResourceDTO> rootElements = resourceDao.ctx().select(
                        RESOURCE.ID.as("id"),
                        RESOURCE.NAME.as("name"),
                        RESOURCE.DESCRIPTION.as("description"),
                        RESOURCE.TYPE.as("type"),
                        RESOURCE.PARENT_RESOURCE_ID.as("parentResourceId"),
                        RESOURCE.IS_ACTIVE.as("isActive"),
                        RESOURCE.CREATED_BY,
                        RESOURCE.UPDATED_BY,
                        RESOURCE.CREATED_ON,
                        RESOURCE.UPDATED_ON
                ).from(RESOURCE)
                .where(RESOURCE.PARENT_RESOURCE_ID.eq(parentId))
                .fetchInto(ResourceDTO.class);

        if (rootElements != null && !rootElements.isEmpty()) {
            log.debug("Found {} root elements for parent ID: {}", rootElements.size(), parentId);
            resourceDTO.setChildDTO(rootElements.stream()
                    .map(x -> {
                        try {
                            return this.buildTree(x);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList()));
        } else {
            log.warn("No root elements found for parent ID: {}", parentId);
        }

        return resourceDTO;
    }

    private ResourceDTO buildTree(ResourceDTO elementRelation) throws JsonProcessingException {
        UUID parentId = elementRelation.getId();
        log.debug("Building tree for element ID: {}", parentId);

        List<ResourceDTO> childElements = resourceDao.ctx().select(
                        RESOURCE.ID.as("id"),
                        RESOURCE.NAME.as("name"),
                        RESOURCE.DESCRIPTION.as("description"),
                        RESOURCE.TYPE.as("type"),
                        RESOURCE.PARENT_RESOURCE_ID.as("parentResourceId"),
                        RESOURCE.IS_ACTIVE.as("isActive"),
                        RESOURCE.CREATED_BY,
                        RESOURCE.UPDATED_BY,
                        RESOURCE.CREATED_ON,
                        RESOURCE.UPDATED_ON
                ).from(RESOURCE)
                .where(RESOURCE.PARENT_RESOURCE_ID.eq(parentId))
                .fetchInto(ResourceDTO.class);

        elementRelation.setChildDTO(childElements.stream()
                .map(x -> {
                    try {
                        return buildTree(x);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList()));

        return elementRelation;
    }

    public Boolean validResourceId(UUID resourceID){
        try {
            return resourceDao.ctx().fetchExists(resourceDao.ctx().select(RESOURCE.ID).from(RESOURCE)
                    .where(RESOURCE.ID.eq(resourceID)));
        }
        catch (Exception ex){
            throw new IllegalArgumentException("Exception occurred while validating resource entry by id");
        }
    }

    public UUID fetchResourceIdOfTypeCategoryByName(String name){
        try {
            return resourceDao.ctx().select(RESOURCE.ID).from(RESOURCE).where(RESOURCE.NAME.eq(name))
                    .and(RESOURCE.TYPE.eq(ResourceTypeEnum.CATEGORY.name())).fetchOneInto(UUID.class);
        }catch (Exception ex){
            throw new IllegalArgumentException("Exception occurred while fetching resource id by name and type is category");
        }
    }

    public UUID fetchResourceIdOfTypeSubCategoryByName(String name){
        try {
            return resourceDao.ctx().select(RESOURCE.ID).from(RESOURCE).where(RESOURCE.NAME.eq(name))
                    .and(RESOURCE.TYPE.eq(ResourceTypeEnum.SUB_CATEGORY.name())).fetchOneInto(UUID.class);
        }catch (Exception ex){
            throw new IllegalArgumentException("Exception occurred while fetching resource id by name and type is sub-category");
        }
    }

}
