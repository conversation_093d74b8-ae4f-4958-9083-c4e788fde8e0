package com.chidhagni.donationreceipt.resources.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jooq.JSONB;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResourceDTO {
    private UUID id;
    private String name;
    private String description;
    private String type;
    private UUID parentResourceId;
    private JSONB validations;
    private Boolean isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
    private List<ResourceDTO> childDTO;

}
