package com.chidhagni.donationreceipt.donors.dto.response;


import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorsResponse {

    private UUID id;
    private String name;
    private String contactNumber;
    private String email;
    private UUID tenantOrgId;
    private String panNo;
    private DonorMetaData donorMetaData;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
    private Boolean isActive;
}
