package com.chidhagni.donationreceipt.donors.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorMetaData {

    private String address;
    private String pinCode;
    private String state;
    private String city;
    private List<UUID> tags;
}
