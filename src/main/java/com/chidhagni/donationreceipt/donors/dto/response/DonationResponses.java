package com.chidhagni.donationreceipt.donors.dto.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationResponses {

    private String name;
    private String email;
    private String mobileNumber;
    private String donationAmount;
    private String orgName;
    private LocalDate dateOfDonation;
    private String paymentMode;
    private String paymentType;
    private String donationHead;

}
