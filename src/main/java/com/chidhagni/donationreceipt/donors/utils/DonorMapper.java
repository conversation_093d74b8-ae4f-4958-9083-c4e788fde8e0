package com.chidhagni.donationreceipt.donors.utils;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsDTO;
import com.chidhagni.donationreceipt.donors.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.Aes256EncryptionUtils;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.EncryptedData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class DonorMapper {

    @Autowired
    private Aes256EncryptionUtils encryptionUtils;


    public Donors mapToDonors(DonorsDTO donorsDTO, UserPrincipal userPrincipal) {

        Donors donors=new Donors();
        donors.setId(UUID.randomUUID());
        donors.setTenantOrgId(donorsDTO.getTenantOrgId());
        // Encrypt the PAN number if present
        String panNo = donorsDTO.getPanNo();
        if (panNo != null && !panNo.isEmpty()) {
            EncryptedData encryptedPan = encryptionUtils.encrypt(panNo);
            donors.setEncryptedPanNo(encryptedPan.getCiphertext());
            donors.setPanNoNonce(encryptedPan.getNonce());
        } else {
            donors.setEncryptedPanNo(null);
            donors.setPanNoNonce(null);
        }
        donors.setName(donorsDTO.getName());
        donors.setEmail(donorsDTO.getEmail());
        donors.setMobileNumber(donorsDTO.getContactNumber());
        donors.setMetaData(donorsDTO.getDonorMetaData());
        donors.setIsActive(Boolean.TRUE);
        donors.setCreatedBy(userPrincipal.getId());
        donors.setCreatedOn(DateUtils.currentTimeIST());

        return donors;
    }

    public DonorsResponse mapToDonorsResponse(Donors donors) {
        DonorsResponse response = new DonorsResponse();
        response.setId(donors.getId());
        response.setName(donors.getName());
        response.setContactNumber(donors.getMobileNumber());
        response.setEmail(donors.getEmail());
        response.setTenantOrgId(donors.getTenantOrgId());
        // Decrypt the PAN number if present
        String encryptedPan = donors.getEncryptedPanNo()!=null? donors.getEncryptedPanNo() : null;
        String panNonce = donors.getPanNoNonce()!=null?donors.getPanNoNonce():null;
        if (encryptedPan!=null&&!encryptedPan.isEmpty() && panNonce!=null&&!panNonce.isEmpty()) {
            String decryptedPan = encryptionUtils.decrypt(encryptedPan, panNonce);
            response.setPanNo(decryptedPan);
        } else {
            response.setPanNo(null);
        }
        response.setIsActive(donors.getIsActive());
        response.setDonorMetaData(donors.getMetaData());
        response.setCreatedBy(donors.getCreatedBy());
        response.setUpdatedBy(donors.getUpdatedBy());
        response.setCreatedOn(donors.getCreatedOn());
        response.setUpdatedOn(donors.getUpdatedOn());
        return response;
    }
}
