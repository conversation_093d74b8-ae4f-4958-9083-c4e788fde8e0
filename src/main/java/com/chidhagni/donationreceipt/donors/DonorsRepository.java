package com.chidhagni.donationreceipt.donors;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonorsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.ImportBatchDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.ImportStagingDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonorDropdownResponse;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsPaginationRequest;
import com.chidhagni.donationreceipt.donors.dto.response.DonationResponses;
import com.chidhagni.donationreceipt.donors.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.donors.utils.DonorMapper;
import com.chidhagni.donationreceipt.organisation.dto.response.DonorEmailAndPanNoResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS;
import static com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS;
import static com.chidhagni.donationreceipt.db.jooq.tables.Donors.DONORS;
import static com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch.IMPORT_BATCH;
import static com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging.IMPORT_STAGING;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.ListValues.LIST_VALUES;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static org.jooq.impl.DSL.trueCondition;

@Repository
@RequiredArgsConstructor
public class DonorsRepository {

    private final DonorsDao donorsDao;
    private final IndividualRoleDao individualRoleDao;
    private final ImportStagingDao importStagingDao;
    private final ImportBatchDao importBatchDao;
    private final DonorMapper donorMapper;

    @Value("${roles.superadmin}")
    private UUID superAdminRoleId;

    public Donors create(Donors donors) {
        try {
            donorsDao.insert(donors);
        } catch (Exception ex) {
            throw new InternalServerError("Exception while inserting the donors", ex);
        }

        return donors;
    }

    public boolean existsByTenantOrgIdAndEmailAndPanNo(UUID tenantOrgId, String email, String panNo) {
        try {
            return donorsDao.ctx().selectCount()
                    .from(DONORS)
                    .where(DONORS.TENANT_ORG_ID.eq(tenantOrgId)
                            .and(DONORS.EMAIL.eq(email))
                            .or(panNo != null ? DONORS.PAN_NO.eq(panNo) : DONORS.PAN_NO.isNull()))
                    .fetchOneInto(Integer.class) > 0;
        } catch (Exception e) {
            throw new DataAccessException("Failed to check duplicate donor", e);
        }
    }

    public Donors getByDonorId(UUID id) {
        Donors donors = null;
        try {
            donors = donorsDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching Donors by it's id", ex);
        }
        return donors;
    }

    public boolean existsByTenantOrgIdAndEmailAndPanNoExcludingId(
            UUID id, UUID tenantOrgId, String email, String panNo) {
        try {
            return donorsDao.ctx().selectCount()
                    .from(DONORS)
                    .where(DONORS.TENANT_ORG_ID.eq(tenantOrgId)
                            .and(DONORS.EMAIL.eq(email))
                            .and(panNo != null ? DONORS.PAN_NO.eq(panNo) : DONORS.PAN_NO.isNull())
                            .and(DONORS.ID.ne(id)))
                    .fetchOneInto(Integer.class) > 0;
        } catch (Exception e) {
            throw new DataAccessException("Failed to check duplicate donor", e);
        }
    }

    public void update(Donors donors) {
        try {
            donorsDao.update(donors);
        } catch (Exception ex) {
            throw new InternalServerError("Exception while updating the individual", ex);
        }
    }

    public List<DonorsResponse> getAllDonors(Condition condition, List<SortField<?>> sortFields,
                                             DonorsPaginationRequest donorsPaginationRequest, UserPrincipal userPrincipal) {

        Integer pageNo = (donorsPaginationRequest.getPage() - 1) * donorsPaginationRequest.getPageSize();

        try {
            Condition finalCondition = buildDonorAccessCondition(condition, userPrincipal);

            // Fetch Donors records with encrypted PAN fields
            List<Donors> donorsList = donorsDao.ctx().select(
                            DONORS.ID,
                            DONORS.NAME,
                            DONORS.MOBILE_NUMBER,
                            DONORS.EMAIL,
                            DONORS.TENANT_ORG_ID,
                            DONORS.ENCRYPTED_PAN_NO,
                            DONORS.PAN_NO_NONCE,
                            DONORS.META_DATA,
                            DONORS.CREATED_ON,
                            DONORS.UPDATED_ON,
                            DONORS.CREATED_BY,
                            DONORS.UPDATED_BY,
                            DONORS.IS_ACTIVE
                    )
                    .from(DONORS)
                    .where(finalCondition)
                    .orderBy(sortFields)
                    .limit(donorsPaginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(Donors.class);

            // Map Donors to DonorsResponse, decrypting the PAN number
            return Optional.ofNullable(donorsList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(donorMapper::mapToDonorsResponse)
                    .collect(Collectors.toList());


        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching all Donors", ex);
        }
    }


    public Integer getDonorCount(Condition condition, UserPrincipal userPrincipal) {
        try {
            boolean hasSuperRole = individualRoleDao.ctx().selectCount()
                    .from(INDIVIDUAL_ROLE)
                    .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userPrincipal.getId())
                            .and(INDIVIDUAL_ROLE.ROLE_ID.eq(superAdminRoleId)))
                    .fetchOneInto(Integer.class) > 0;

            Condition finalCondition;
            if (hasSuperRole) {
                finalCondition = condition != null ? condition : trueCondition();
            } else {
                List<UUID> userOrgIds = individualRoleDao.ctx().select(INDIVIDUAL_ROLE.ORG_ID)
                        .from(INDIVIDUAL_ROLE)
                        .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userPrincipal.getId()))
                        .fetchInto(UUID.class);

                if (userOrgIds.isEmpty()) {
                    return 0;
                }

                Condition orgCondition = DONORS.TENANT_ORG_ID.in(userOrgIds);
                finalCondition = condition != null ? condition.and(orgCondition) : orgCondition;
            }

            return donorsDao.ctx().selectCount()
                    .from(DONORS)
                    .where(finalCondition)
                    .fetchOneInto(Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while counting Donors", ex);
        }
    }

    private Condition buildDonorAccessCondition(Condition baseCondition, UserPrincipal userPrincipal) {
        UUID userId = userPrincipal.getId();

        boolean isSuperAdmin = individualRoleDao.ctx().fetchExists(
                individualRoleDao.ctx().selectOne()
                        .from(INDIVIDUAL_ROLE)
                        .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userId)
                                .and(INDIVIDUAL_ROLE.ROLE_ID.eq(superAdminRoleId)))
        );

        if (isSuperAdmin) {
            return baseCondition != null ? baseCondition : DSL.trueCondition();
        }

        List<UUID> userOrgIds = individualRoleDao.ctx().select(INDIVIDUAL_ROLE.ORG_ID)
                .from(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userId))
                .fetchInto(UUID.class);

        if (userOrgIds.isEmpty()) {
            return DSL.falseCondition();
        }

        Condition orgCondition = DONORS.TENANT_ORG_ID.in(userOrgIds);
        return baseCondition != null ? baseCondition.and(orgCondition) : orgCondition;
    }

    public List<DonorEmailAndPanNoResponse> getAllDonorEmailPanNoResponsesByOrgId(UUID orgId) {
        return donorsDao.ctx().select(DONORS.EMAIL.as("email"), DONORS.ENCRYPTED_PAN_NO.as("encryptedPanNo"),
                        DONORS.PAN_NO_NONCE.as("panNoNonce"))
                .from(DONORS)
                .where(DONORS.TENANT_ORG_ID.eq(orgId))
                .fetchInto(DonorEmailAndPanNoResponse.class);
    }


    public List<String> getDuplicatedFields(UUID tenantOrgId, String email, String panNo) {
        List<String> duplicatedFields = new ArrayList<>();
        boolean emailExists = donorsDao.ctx().selectCount()
                .from(DONORS)
                .where(DONORS.TENANT_ORG_ID.eq(tenantOrgId)
                        .and(DONORS.EMAIL.eq(email)))
                .fetchOneInto(Integer.class) > 0;
        if (emailExists) {
            duplicatedFields.add("emailDuplicatedInDonors");
        }

        if (panNo != null) {
            boolean panNoExists = donorsDao.ctx().selectCount()
                    .from(DONORS)
                    .where(DONORS.TENANT_ORG_ID.eq(tenantOrgId)
                            .and(DONORS.PAN_NO.eq(panNo)))
                    .fetchOneInto(Integer.class) > 0;
            if (panNoExists) {
                duplicatedFields.add("panNoDuplicatedInDonors");
            }
        }

        return duplicatedFields;
    }

    public List<String>getDuplicatesFromImportStaging(UUID tenantOrgId, String email, String panNo) {
        List<String> duplicatedFields = new ArrayList<>();
        boolean emailExists = importStagingDao.ctx().selectCount()
                .from(IMPORT_STAGING)
                .join(IMPORT_BATCH)
                .on(IMPORT_STAGING.IMPORT_BATCH_ID.eq(IMPORT_BATCH.ID))
                .where(IMPORT_BATCH.TENANT_ORG_ID.eq(tenantOrgId)
                        .and(IMPORT_STAGING.EMAIL.eq(email)))
                .fetchOneInto(Integer.class) > 0;
        if (emailExists) {
            duplicatedFields.add("emailDuplicatedInImportStaging");
        }

        if (panNo != null) {
            boolean panNoExists = importStagingDao.ctx().selectCount()
                    .from(IMPORT_STAGING)
                    .join(IMPORT_BATCH)
                    .on(IMPORT_STAGING.IMPORT_BATCH_ID.eq(IMPORT_BATCH.ID))
                    .where(IMPORT_BATCH.TENANT_ORG_ID.eq(tenantOrgId)
                            .and(IMPORT_STAGING.PAN_NO.eq(panNo)))
                    .fetchOneInto(Integer.class) > 0;
            if (panNoExists) {
                duplicatedFields.add("panNoDuplicatedInImportStaging");
            }
        }
        return duplicatedFields;

    }



    public List<DonorDropdownResponse> getDonorDropdown() {

        return donorsDao.ctx().select(DONORS.ID.as("id"),DONORS.NAME.as("name"),DONORS.EMAIL.as("email"))
                .from(DONORS)
                .where(DONORS.IS_ACTIVE.eq(Boolean.TRUE))
                .fetchInto(DonorDropdownResponse.class);
    }

    public List<DonorDropdownResponse> getTenantDonorDropdown(UUID tenantOrgId) {
        return donorsDao.ctx().select(DONORS.ID.as("id"),DONORS.NAME.as("name"),DONORS.EMAIL.as("email"))
                .from(DONORS)
                .where(DONORS.TENANT_ORG_ID.eq(tenantOrgId).and(DONORS.IS_ACTIVE.eq(Boolean.TRUE)))
                .fetchInto(DonorDropdownResponse.class);
    }

    public List<DonationResponses> getAllDonationsByDonorId(UUID donorId)
    {
        var lvMode = LIST_VALUES.as("lv_mode");
        var lvType = LIST_VALUES.as("lv_type");
        return donorsDao.ctx().select(
                        DONORS.NAME.as("name"),
                        DONORS.EMAIL.as("email"),
                        DONORS.MOBILE_NUMBER.as("mobileNumber"),
                        DSL.field("DONATION_RECEIPTS.meta_data ->> 'amount'", String.class).as("donationAmount"),
                        ORGANISATION.NAME.as("orgName"),
                        DONATION_RECEIPTS.RECEIPT_DATE.as("dateOfDonation"),
                        lvMode.NAME.as("paymentMode"),
                        lvType.NAME.as("paymentType"),
                        DONATION_HEADS.NAME.as("donationHead")
                )
                .from(DONATION_RECEIPTS)
                .join(DONORS).on(DONATION_RECEIPTS.DONOR_ID.eq(DONORS.ID))
                .join(ORGANISATION).on(DONATION_RECEIPTS.TENANT_ORG_ID.eq(ORGANISATION.ID))
                .leftJoin(DONATION_HEADS).on(DONATION_RECEIPTS.DONATION_HEAD_ID.eq(DONATION_HEADS.ID))
                .leftJoin(lvMode)
                .on(lvMode.ID.eq(
                        DSL.field("DONATION_RECEIPTS.meta_data ->> 'paymentMode'", String.class).cast(UUID.class)
                ))
                .leftJoin(lvType)
                .on(lvType.ID.eq(
                        DSL.field("DONATION_RECEIPTS.meta_data ->> 'paymentType'", String.class).cast(UUID.class)
                ))
                .where(DONATION_RECEIPTS.DONOR_ID.eq(donorId))
                .fetchInto(DonationResponses.class);
    }

}
