package com.chidhagni.donationreceipt.individual.dto.response;

import com.chidhagni.donationreceipt.individual.dto.IndividualDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAllIndividualsForContacts {
    private List<IndividualDTO> individuals;
    private Integer rowCount;

}
