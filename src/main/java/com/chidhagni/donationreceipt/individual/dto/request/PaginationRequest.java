package com.chidhagni.donationreceipt.individual.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
public class PaginationRequest {
    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer pageSize = 10;

    @Builder.Default
    private String nameFilter = "";

    @Builder.Default
    private String emailFilter = "";

    @Builder.Default
    private String mobileFilter = "";

    @Builder.Default
    private UUID roleFilter = null;


    @Builder.Default
    private UUID orgId=null;

    private boolean sortByNameAsc;

    private boolean sortByNameDesc;

    private boolean sortByEmailAsc;

    private boolean sortByEmailDesc;

    private boolean sortByMobileAsc;

    private boolean sortByMobileDesc;

    private boolean sortByCreatedDateAsc;

}
