package com.chidhagni.donationreceipt.individual.dto.request;

import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualRequest {
    private UUID roleId;
    private List<RoleNode> permissionsDTOList;
}
