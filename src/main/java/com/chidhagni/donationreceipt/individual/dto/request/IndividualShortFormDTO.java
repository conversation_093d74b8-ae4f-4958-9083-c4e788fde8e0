package com.chidhagni.donationreceipt.individual.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndividualShortFormDTO {
    @NotNull
    private String firstName;
    private String lastName;
    @NotNull
    private String orgName;

    @Email(regexp = "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,6}", flags = Pattern.Flag.CASE_INSENSITIVE)
    private String email;

    @Pattern(regexp = "^[6-9]\\d{9}$", message = "Invalid mobile number format")
    private String mobileNumber;

    @Pattern(
            regexp = "^((25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)$",
            message = "Invalid IP address format"
    )
    private String ipAddress;

    private UUID designation;
    private String otherDesignation;
}
