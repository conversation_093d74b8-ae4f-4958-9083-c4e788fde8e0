package com.chidhagni.donationreceipt.individual.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualResponseDTO {
    private UUID id;
    private String name;
    private String email;
    private String mobileNumber;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String createdBy;
    private String updatedBy;
    private UUID roleId;
    private String roleName;
    private UUID organisationId;
    private Boolean isActive;
}
