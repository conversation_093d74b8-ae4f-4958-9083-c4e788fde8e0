package com.chidhagni.donationreceipt.individual.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContactsGroupPaginationRequest {
    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 10;
    @Builder.Default
    private String searchKeyword = "";
    @Builder.Default
    private String category = "";
    @Builder.Default
    private List<UUID> serviceTypeIds= List.of();
    @Builder.Default
    private List<UUID> location = List.of();
    @Builder.Default
    private List<UUID> assignedTo = List.of();
    @Builder.Default
    private boolean isStrategicPartner = false;
    @Builder.Default
    private boolean isListingEmpanelled = false;
    @Builder.Default
    private boolean isMicroSiteEmpanelled = false;
    @Builder.Default
    private List<UUID> leadStatus = List.of();
    @Builder.Default
    private List<UUID> leadPriority = List.of();
    @Builder.Default
    private List<UUID> designation = List.of();
    @Builder.Default
    private String searchByOrgName = "";
}
