package com.chidhagni.donationreceipt.individual.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndividualDTO {
    private UUID id;
    private String firstName;
    private String lastName;
    private String contactType;
    private String mobileNumber;
    private String email;
    private String createdOn;
    private String updatedOn;
    private String createdBy;
    private String updatedBy;
    private String orgName;
}
