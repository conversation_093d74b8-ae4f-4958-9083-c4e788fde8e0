package com.chidhagni.donationreceipt.individual;



import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.individual.dto.request.PaginationRequest;
import com.chidhagni.donationreceipt.individual.dto.response.IndividualResponseDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static com.chidhagni.donationreceipt.db.jooq.tables.Roles.ROLES;

@lombok.extern.slf4j.Slf4j
@Slf4j
@Repository
@RequiredArgsConstructor
public class IndividualRepository {
    private final IndividualDao individualDao;
    private final IndividualRoleDao individualRoleDao;
    private final IndividualPermissionDao individualPermissionDao;

    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;


    @Value("${roles.donor}")
    private UUID donorRoleId;

    public List<IndividualResponseDTO> getAllIndividuals(Condition condition, List<SortField<?>> sortFields,
                                                         PaginationRequest paginationRequest,
                                                         UserPrincipal userPrincipal) {
        Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        try {

            UUID loggedUser = userPrincipal.getId();
            List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

            Condition finalCondition = condition != null ? condition : DSL.noCondition();

            boolean isTenantAdmin = individualRoles.stream()
                    .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

            UUID orgId = null;
            if (isTenantAdmin) {

                orgId = individualRoles.get(0).getOrgId();
                finalCondition = finalCondition.and(INDIVIDUAL_ROLE.ORG_ID.eq(orgId));
            }


            return individualDao.ctx().select(
                            INDIVIDUAL.ID.as("id"),
                            INDIVIDUAL.NAME.as("name"),
                            INDIVIDUAL.EMAIL.as("email"),
                            INDIVIDUAL.MOBILE_NUMBER.as("mobileNumber"),
                            INDIVIDUAL.CREATED_ON.as("createdOn"),
                            INDIVIDUAL.UPDATED_ON.as("updatedOn"),
                            INDIVIDUAL.as("r0").EMAIL.as("createdBy"),
                            INDIVIDUAL.as("r1").EMAIL.as("updatedBy"),
                            INDIVIDUAL_ROLE.ROLE_ID.as("roleId"),
                            ROLES.NAME.as("roleName"),
                            INDIVIDUAL_ROLE.ORG_ID.as("organisationId"),
                            INDIVIDUAL.IS_ACTIVE.as("isActive")
                    )
                    .from(INDIVIDUAL)
                    .leftJoin(INDIVIDUAL.as("r0")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("r0").ID))
                    .leftJoin(INDIVIDUAL.as("r1")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("r1").ID))
                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(ROLES).on(ROLES.ID.eq(INDIVIDUAL_ROLE.ROLE_ID))
                    .where(finalCondition)
                    .orderBy(sortFields)
                    .limit(paginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(IndividualResponseDTO.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching all Individuals", ex);
        }
    }


public Integer getIndividualsCount(Condition condition, UserPrincipal userPrincipal) {
    try {
        UUID loggedUser = userPrincipal.getId();
        List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

        Condition finalCondition = condition != null ? condition : DSL.noCondition();

        boolean isTenantAdmin = individualRoles.stream()
                .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

        if (isTenantAdmin) {
            UUID orgId = individualRoles.get(0).getOrgId();
            finalCondition = finalCondition.and(INDIVIDUAL_ROLE.ORG_ID.eq(orgId));
        }

        return individualDao.ctx()
                .selectCount()
                .from(INDIVIDUAL)
                .leftJoin(INDIVIDUAL.as("r0")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("r0").ID))
                .leftJoin(INDIVIDUAL.as("r1")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("r1").ID))
                .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                .leftJoin(ROLES).on(ROLES.ID.eq(INDIVIDUAL_ROLE.ROLE_ID))
                .where(finalCondition)
                .fetchOne(0, Integer.class);
    } catch (Exception ex) {
        throw new InternalServerError("Exception occurred while fetching all Individuals count", ex);
    }
}



    /**
     * Retrieves an individual by their unique ID.
     *
     * <p>This method fetches an individual from the database using their ID.</p>
     *
     * @param id The unique identifier of the individual.
     * @return An `Individual` object representing the fetched individual.
     * @throws InternalServerError If an error occurs while fetching the individual.
     */

    public Individual getById(UUID id)
    {
        Individual individual= null;
        try {
            individual=individualDao.fetchOneById(id);
        }catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching individual by it's id", ex);
        }
        return individual;
    }
//
    /**
     * Retrieves the roles assigned to an individual.
     *
     * <p>This method fetches the individual roles associated with a given individual ID.</p>
     *
     * @param id The unique identifier of the individual.
     * @return A list of `IndividualRole` objects representing the assigned roles actually list of size 1.
     * @throws InternalServerError If an error occurs while fetching roles.
     */
    public List<IndividualRole> getIndividualRoleByIndividualId(UUID id)
    {
        List<IndividualRole> individualRoleList;
        try {
            individualRoleList=individualRoleDao.fetchByIndividualId(id);
        }catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching individual in individual role by it's id", ex);
        }
        return individualRoleList;
    }

    public List<IndividualPermission> getIndividualPermissionsByIndividualId(UUID id)
    {
        List<IndividualPermission> individualPermissionList;
        try {
            individualPermissionList=individualPermissionDao.fetchByIndividualId(id);
        }catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching individual in individual role by it's id", ex);
        }
        return individualPermissionList;
    }

    public Individual getByEmail(String email) {
        Individual individual = null;
        try {
            individual = individualDao.fetchOneByEmail(email);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching individual by email", ex);
        }
        return individual;
    }

    public boolean isIndividualExist(UUID id){
        return individualDao.ctx().fetchExists(individualDao.ctx().select(INDIVIDUAL.ID).from(INDIVIDUAL)
                .where(INDIVIDUAL.ID.eq(id)));
    }

      public Individual insertIndividual(Individual individual){
          try{
              individualDao.insert(individual);
          }catch (Exception ex){
              throw new InternalServerError("Exception while inserting the individual", ex);
          }
          return individual;
      }

    public void update(Individual individual) {

          try{
              individualDao.update(individual);
          }
          catch (Exception ex){
              throw new InternalServerError("Exception while updating the individual", ex);
          }
        }

    public List<DonorsResponse> getAllIndividualsDonors(Condition condition, List<SortField<?>> sortFields,
                                                        PaginationRequest paginationRequest,
                                                        UserPrincipal userPrincipal) {
        Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        try {
            return individualDao.ctx()
                    .select(
                            INDIVIDUAL.ID.as("id"),
                            INDIVIDUAL.NAME.as("name"),
                            INDIVIDUAL.EMAIL.as("email"),
                            INDIVIDUAL.MOBILE_NUMBER.as("contactNumber"),
                            DSL.field("INDIVIDUAL.meta_data->>'panNo'").as("panNo"),
                            DSL.field("INDIVIDUAL.meta_data->>'donorType'", UUID.class).as("donorType"),
                            DSL.field("INDIVIDUAL.meta_data->>'donorReferralSource'", UUID.class).as("donorReferralSource"),
                            DSL.field("INDIVIDUAL.meta_data->>'donorReferralSourceAnyOther'",UUID.class).as("donorReferralSourceAnyOther"),
                            INDIVIDUAL_ROLE.ROLE_ID.as("roleId"),
                            INDIVIDUAL_ROLE.ORG_ID.as("donorOrgId"),
                            ORGANISATION.NAME.as("donorOrgName")
                    )
                    .from(INDIVIDUAL)
                    .leftJoin(INDIVIDUAL_ROLE)
                    .on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(ROLES)
                    .on(INDIVIDUAL_ROLE.ROLE_ID.eq(ROLES.ID))
                    .leftJoin(ORGANISATION)
                    .on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .where(condition.and(INDIVIDUAL_ROLE.ROLE_ID.eq(donorRoleId)))
                    .orderBy(sortFields)
                    .limit(paginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(DonorsResponse.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching all Donors", ex);
        }
    }


    public Integer getIndividualsCountDonor(Condition condition, UserPrincipal userPrincipal) {
        try {
            return individualDao.ctx()
                    .select(DSL.countDistinct(INDIVIDUAL.ID))
                    .from(INDIVIDUAL)
                    .leftJoin(INDIVIDUAL_ROLE)
                    .on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(ROLES)
                    .on(INDIVIDUAL_ROLE.ROLE_ID.eq(ROLES.ID))
                    .leftJoin(ORGANISATION)
                    .on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .where(condition.and(INDIVIDUAL_ROLE.ROLE_ID.eq(donorRoleId)))
                    .fetchOne(0, Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while counting Donors", ex);
        }
    }


    public boolean existsByMobile(String mobileNumber) {
        try {
            List<Individual> individuals = individualDao.fetchByMobileNumber(mobileNumber);
            return !individuals.isEmpty();
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching individual by mobile number", ex);
        }
    }

}
