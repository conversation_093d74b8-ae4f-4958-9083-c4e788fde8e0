package com.chidhagni.donationreceipt.individual.util;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDTO;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorBasicProfileDto;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.UUID;

@Mapper(componentModel = "spring")
public interface IndividualMapperI {


    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
    @Mapping(target = "name", source = "individualDTO.name")
    @Mapping(target = "email", source = "individualDTO.email")
    @Mapping(target = "mobileNumber", source = "individualDTO.mobileNumber")
    @Mapping(target = "createdOn", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "updatedOn", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "createdBy", expression = "java(userPrincipal.getId())")
    @Mapping(target = "updatedBy", expression = "java(userPrincipal.getId())")
    @Mapping(target = "isActive", expression = "java(true)")
    Individual mapIndividualDtoToIndividual(IndividualDTO individualDTO, UserPrincipal userPrincipal);







    @Mapping(target = "id", source = "individualId")
    @Mapping(target = "email",source = "dto.email")
    //@Mapping(target = "isActive", source = "dto.isActive")
    @Mapping(target = "updatedOn", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "updatedBy", expression = "java(userPrincipal.getId())")
    @Mapping(target = "name", source = "dto.name")
    @Mapping(target = "mobileNumber", source = "dto.contactNumber")
    @Mapping(target = "metaData.panNo", source = "dto.panNo")
    @Mapping(target = "metaData.address", source = "dto.address")
    @Mapping(target = "metaData.pinCode", source = "dto.pinCode")
    @Mapping(target = "metaData.state",source = "dto.state")
    Individual updateIndividualFromDto(UUID individualId, IndividualDonorBasicProfileDto dto, UserPrincipal userPrincipal);

}
