package com.chidhagni.donationreceipt.individual.util;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.individual.IndividualRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class IndividualHelperMethods {

    private final IndividualRepository individualRepository;



    public void isIndividualAlreadyExistByEmail(String email) {
        if (email == null) {
            throw new IllegalArgumentException("Email Cannot Be Null");
        }
        Individual individualByEmail = individualRepository.getByEmail(email);
        if (individualByEmail != null) {
            throw new IllegalArgumentException(String.format("Individual Already Exist With Given Email :: %s", email));
        }
    }


}
