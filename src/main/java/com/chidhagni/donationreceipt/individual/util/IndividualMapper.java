package com.chidhagni.donationreceipt.individual.util;



import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDTO;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorBasicProfileDto;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorDTO;
import com.chidhagni.donationreceipt.individual.dto.response.IndividualResponseDTO;
import com.chidhagni.donationreceipt.individual.dto.response.IndividualResponseWithRolesDTO;
import com.chidhagni.donationreceipt.organisation.OrganisationDonorMetaDataDto;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

public class IndividualMapper {


    public static IndividualResponseWithRolesDTO toRolesResponseDto(IndividualResponseDTO individualResponseDTO, String companyName) {
        IndividualResponseWithRolesDTO responseWithRolesDTO = new IndividualResponseWithRolesDTO();
        responseWithRolesDTO.setId(individualResponseDTO.getId());
        responseWithRolesDTO.setName(individualResponseDTO.getName());
        responseWithRolesDTO.setEmail(individualResponseDTO.getEmail());
        responseWithRolesDTO.setMobileNumber(individualResponseDTO.getMobileNumber());
        responseWithRolesDTO.setCreatedOn(individualResponseDTO.getCreatedOn());
        responseWithRolesDTO.setUpdatedOn(individualResponseDTO.getUpdatedOn());
        responseWithRolesDTO.setCreatedBy(individualResponseDTO.getCreatedBy());
        responseWithRolesDTO.setUpdatedBy(individualResponseDTO.getUpdatedBy());
        responseWithRolesDTO.setIsActive(individualResponseDTO.getIsActive());
        responseWithRolesDTO.setOrganisationId(individualResponseDTO.getOrganisationId());
        responseWithRolesDTO.setCompanyName(companyName);
        responseWithRolesDTO.setRoleId(individualResponseDTO.getRoleId());
        responseWithRolesDTO.setRoleName(individualResponseDTO.getRoleName());
        return responseWithRolesDTO;
    }


    public static IndividualDTO map(Individual individual, UUID roleId, Organisation organisation,
                                    List<RoleNode> permissionsDTOList) {
        if (individual == null) {
            return null;
        }

        return IndividualDTO.builder()
                .id(individual.getId())
                .name(individual.getName())
                .email(individual.getEmail())
                .mobileNumber(individual.getMobileNumber())
                .roleId(roleId)
                .orgId(organisation.getId() != null ? organisation.getId() : null)
                .organisationCategory(OrganisationEnums.valueOf(organisation.getCategory()))
                .permissionsDTOList(permissionsDTOList)
                .build();
    }


    public static void checkIfIndividualIsNull(Individual individual) {
        if (individual == null) {
            throw new IllegalArgumentException("Exception Occurred while fetching user by its id, as individual is not available");
        }
    }

    public static void checkIfIndividualRoleListIsEmpty(List<IndividualRole> individualRoleList, UUID id) {
        if (CollectionUtils.isEmpty(individualRoleList)) {
            throw new IllegalArgumentException("No individual role is found for individual => " + id);
        }
    }

    public static void checkIfIndividualPermissionListIsEmpty(List<IndividualPermission> individualPermissionList, UUID id) {
        if (CollectionUtils.isEmpty(individualPermissionList)) {
            throw new IllegalArgumentException("No individual Permissions is found for individual => " + id);
        }
    }



    public static IndividualDonorDTO mapDonor(Individual individual, UUID roleId,Organisation organisation) {
        if (individual == null) {
            return null;
        }
        OrganisationDonorMetaDataDto donorMetaData = null;
        if (organisation.getMetaData() instanceof OrganisationDonorMetaDataDto) {
             donorMetaData = (OrganisationDonorMetaDataDto) organisation.getMetaData();
        }

        return IndividualDonorDTO.builder()
                .id(individual.getId())
                .name(individual.getName())
                .email(individual.getEmail())
                .address(individual.getMetaData().getAddress() != null ? individual.getMetaData().getAddress(): "")
                .panNo(individual.getMetaData().getPanNo()!=null?individual.getMetaData().getPanNo():"")
                .contactNumber(individual.getMobileNumber())
                .state(individual.getMetaData().getState())
                .pinCode(individual.getMetaData().getPinCode())
                .donorType(donorMetaData!=null?donorMetaData.getDonorType():null)
                .donorReferralSource(donorMetaData!=null?donorMetaData.getDonorReferralSource():null)
                .donorReferralSourceAnyOther(donorMetaData!=null?donorMetaData.getDonorReferralSourceAnyOther():null)
                .roleId(roleId)
                .donorOrgName(organisation.getName())
                .donorOrgId(organisation.getId())
                .build();
    }


    public static IndividualDonorBasicProfileDto mapToIndividualBasicProfileDto(Individual individual,
                                                                                Organisation organisation,
                                                                                UUID roleId) {
        if (individual == null) {
            return null;
        }
        IndividualDonorBasicProfileDto dto = IndividualDonorBasicProfileDto.builder()
                .id(individual.getId())
                .name(individual.getName())
                .email(individual.getEmail())
                .contactNumber(individual.getMobileNumber())
                .address(individual.getMetaData() != null ? individual.getMetaData().getAddress() : null)
                .panNo(individual.getMetaData() != null ? individual.getMetaData().getPanNo() : null)
                .donorOrgName(organisation != null ? organisation.getName() : null)
                .roleId(roleId)
                .build();

        if (organisation.getMetaData() != null && organisation.getMetaData() instanceof OrganisationDonorMetaDataDto) {
            OrganisationDonorMetaDataDto donorMetaData = (OrganisationDonorMetaDataDto) organisation.getMetaData();
            dto.setDonorType(donorMetaData.getDonorType());
            dto.setDonorReferralSource(donorMetaData.getDonorReferralSource());
            dto.setDonorReferralSourceAnyOther(donorMetaData.getDonorReferralSourceAnyOther());
        }
        return dto;
    }
}
