package com.chidhagni.donationreceipt.individual.jooq;

import com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import org.jetbrains.annotations.NotNull;

public class IndividualMetaDataJsonConverter extends BaseJsonBJooqConverter<IndividualMetaDataDTO> {

    @Override
    public @NotNull Class<IndividualMetaDataDTO> toType() {

        return IndividualMetaDataDTO.class;
    }
}
