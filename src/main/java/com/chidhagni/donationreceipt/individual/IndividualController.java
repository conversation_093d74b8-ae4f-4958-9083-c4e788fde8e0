package com.chidhagni.donationreceipt.individual;

import com.chidhagni.donationreceipt.individual.dto.request.IndividualDTO;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualRequest;
import com.chidhagni.donationreceipt.individual.dto.request.PaginationRequest;
import com.chidhagni.donationreceipt.individual.dto.response.GetAllIndividualsResponse;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllDonors;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static com.chidhagni.donationreceipt.individual.constants.IndividualMetaData.INDIVIDUAL_GET_RES_V1;


@RestController
@RequestMapping("/api/v1/individual")
@RequiredArgsConstructor
public class IndividualController {
    private final IndividualService individualService;

    @PostMapping(value = "/all")
    public ResponseEntity<GetAllIndividualsResponse> getAllIndividuals(
            @RequestBody(required = false) PaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().build();
        }
        return new ResponseEntity<>(individualService.getAllIndividuals(paginationRequest,userPrincipal), HttpStatus.OK);
    }
    @GetMapping(value= "/{individualId}",produces = INDIVIDUAL_GET_RES_V1)
    public ResponseEntity<IndividualDTO> getIndividualById(@PathVariable UUID individualId) {
        IndividualDTO individualDTO = individualService.getByIndividualId(individualId);
        if (individualDTO != null) {
            return ResponseEntity.ok(individualDTO);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping(value = "/permissions", produces = INDIVIDUAL_GET_RES_V1)
    public ResponseEntity<IndividualDTO> getIndividualById(@CurrentUser UserPrincipal userPrincipal) {
        IndividualDTO individualDTO = individualService.getByIndividualId(userPrincipal.getId());
        if (individualDTO != null) {
            return ResponseEntity.ok(individualDTO);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping(value= "/{individualId}/role-permissions")
    public ResponseEntity<IndividualRequest> updateRoleAndPermissions(
            @PathVariable UUID individualId,
            @RequestBody IndividualRequest individualRequest) {
        individualService.updateRoleAndPermissionsInIndividual(individualId, individualRequest);
        return ResponseEntity.ok(individualRequest);
    }

    @PatchMapping(path = {"/activate/{individualId}"})
    @ResponseStatus(HttpStatus.OK)
    public void activateIndividual(@PathVariable("individualId")UUID individualId)
    {
        individualService.activateIndividual(individualId);
    }

    @PatchMapping(path = {"/deactivate/{individualId}"})
    @ResponseStatus(HttpStatus.OK)
    public void deactivateIndividual(@PathVariable("individualId")UUID individualId)
    {
        individualService.deActivateIndividual(individualId);
    }


    @PostMapping(value = "/all/donors")
    public ResponseEntity<GetAllDonors> getAllIndividualsDonors(
            @RequestBody(required = false) PaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().build();
        }
        return new ResponseEntity<>(individualService.getAllIndividualsDonor(paginationRequest,userPrincipal), HttpStatus.OK);
    }
}
