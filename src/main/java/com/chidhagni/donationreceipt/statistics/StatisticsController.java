package com.chidhagni.donationreceipt.statistics;


import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.statistics.dto.DonationHeadResponse;
import com.chidhagni.donationreceipt.statistics.dto.DonationReceiptsCardsResponse;
import com.chidhagni.donationreceipt.statistics.dto.MonthlyDonationResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.exception.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.chidhagni.donationreceipt.statistics.dto.DonationAmount;
import com.chidhagni.donationreceipt.statistics.dto.DonationHeadDistribution;
import com.chidhagni.donationreceipt.statistics.dto.GraphType;
import com.chidhagni.donationreceipt.statistics.dto.SignupGraphData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import java.util.UUID;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/api/v1/statistics")
@RequiredArgsConstructor
public class StatisticsController {
    private final StatisticsService statisticsService;

    @GetMapping(value = "/donation-distribution")
    public ResponseEntity<List<DonationHeadDistribution>> getDonationHeadDistribution() {
        List<DonationHeadDistribution> response = statisticsService.getDonationHeadDistribution();
        return ResponseEntity.ok(response);
    }


    @GetMapping(value = "/org-signup-data")
    public ResponseEntity<List<SignupGraphData>> getSignUpDataOfOrganisation(@RequestParam GraphType graphType) {
        List<SignupGraphData> response = statisticsService.getSignupGraphData(graphType);
        return ResponseEntity.ok(response);
    }


    @GetMapping(value = "/donation-amount")
    public ResponseEntity<List<DonationAmount>> getDonationAmount(@RequestParam GraphType graphType) {
        List<DonationAmount> response = statisticsService.getDonationAmount(graphType);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/cards")
    public DonationReceiptsCardsResponse getDashboardData(
            @RequestParam String actor,
            @RequestParam(required = false) UUID orgId) {

        if (OrganisationEnums.SUPER_ADMIN.name().equalsIgnoreCase(actor)) {
            return statisticsService.getSuperAdminDashboard();
        } else if (OrganisationEnums.TENANT.name().equalsIgnoreCase(actor) && orgId != null) {
            return statisticsService.getTenantDashboard(orgId);
        }
        return new DonationReceiptsCardsResponse();
    }

    @GetMapping("/monthly-donations")
    public ResponseEntity<MonthlyDonationResponse> getMonthlyDonations(
            @RequestParam(required = false) UUID orgId) {
            return ResponseEntity.ok(statisticsService.getMonthlyDonations(orgId));
    }

    @GetMapping("/donations-by-head")
    public ResponseEntity<DonationHeadResponse> getDonationsByHead(
            @RequestParam(required = false) UUID orgId) {
        try {
            return ResponseEntity.ok(statisticsService.getDonationsByHead(orgId));
        } catch (DataAccessException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }



}
