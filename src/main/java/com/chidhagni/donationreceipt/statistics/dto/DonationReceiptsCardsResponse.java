package com.chidhagni.donationreceipt.statistics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationReceiptsCardsResponse {

    private Long totalTenantsRegistered;
    private Long totalTenantsDonationsReceived;
    private Long last30DaysDonations;
    private Double averageDonation;
    private Long uniqueDonors;
    private Long totalDonations;
}

