package com.chidhagni.donationreceipt.mail;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
public class EmailVerificationController {

//    @Autowired
//    private MemberService memberService;
//
//    @Autowired
//    private EmailVerificationService emailVerificationService;
//
//    public EmailVerificationController(MemberService memberService) {
//        this.memberService = memberService;
//    }
//
//    @PostMapping("/verify")
//    public ResponseEntity<MemberDTO> verifyEmailAddress(@RequestBody EmailVerificationDTO emailVerificationDTO) {
//
//        return ResponseEntity.ok().body(null);
//    }
//
//    @PostMapping("/reset")
//    public void verifyResetPassword(@Valid @RequestBody PasswordResetDTO passwordResetDTO) {
//        memberService.resetPassword(passwordResetDTO.getEmailId(), passwordResetDTO.getResetCode(), passwordResetDTO.getPassword());
//    }
//
//    @PostMapping("/forgotPassword")
//    public void forgotPassword(@Valid @RequestParam String email) throws MessagingException {
//        memberService.forgotPassword(email);
//    }
//
//    @PostMapping("/verifyOTP")
//    public Boolean verify(@Valid @RequestBody OtpVerifyDTO otpVerifyDTO) {
//        return memberService.verifyOTP(otpVerifyDTO);
//    }
//
//    @PostMapping("/otp")
//    public void otp(@Valid @RequestParam String email) throws MessagingException {
//        memberService.sendOtp(email);
//    }
//
////    @PatchMapping("/unSubscribe")
////    public EmailSubscribedDTO unSubscribe(@Valid @RequestBody UnsubscribeDataDTO unSubscribeDataDTO) throws MessagingException {
////        return emailVerificationService.unSubscribeNewsletter(unSubscribeDataDTO);
////    }
////
////    @PostMapping("/emailVerify")
////    public ResponseEntity<EmailVerificationResponseDTO> emailVerification(@RequestBody EmailVerifyDTO emailVerifyDTO) throws MessagingException {
////        EmailVerificationResponseDTO emailVerificationResponseDTO = emailVerificationService.create(emailVerifyDTO);
////        return ResponseEntity.ok().body(emailVerificationResponseDTO);
////    }
//
//    /**
//     * For Employees page.
//     * This is called to verify the email address.
//     * @param otpVerifyDTO
//     * @param isMember
//     * @return
//     * @throws JsonProcessingException
//     * @throws MessagingException
//     */
//    @PostMapping("/verifyOTPV2")
//    public OTPVerifiedDTO verifyOTPV2(@Valid @RequestBody OtpVerifyDTO otpVerifyDTO, @RequestParam(value = "isMember", defaultValue = "false") boolean isMember) throws JsonProcessingException, MessagingException {
//        return memberService.verifyOTPV2(otpVerifyDTO, isMember);
//    }
//
//    /**
//     * Signup Page, All Profiles page (Both for SP and CHS)
//     * This is called to verify the email address.
//     * @param otpVerifyDTO
//     * @param isMember
//     * @return
//     * @throws JsonProcessingException
//     * @throws MessagingException
//     */
//    @PostMapping("/verifyOTPV3")
//    public OTPVerifiedDTO verifyOTPV3(@Valid @RequestBody OtpVerifyDTO otpVerifyDTO, @RequestParam(value = "isMember", defaultValue = "false") boolean isMember) throws JsonProcessingException, MessagingException {
//        return memberService.verifyOTPV3(otpVerifyDTO, isMember);
//    }
//
////    @PostMapping("/newsletter")
////    public ResponseEntity<EmailSubscribedDTO> subscribedVerification(@Valid @RequestParam String email) throws MessagingException {
////        EmailSubscribedDTO emailSubscribedDTO = emailVerificationService.subscriptionNewsletter(email);
////        return ResponseEntity.ok().body(emailSubscribedDTO);
////    }
////
////    @PostMapping("/checkIsVerified")
////    public EmailVerificationResponseDTO checkIsVerified(@RequestParam String email) {
////        return emailVerificationService.checkIsVerified(email);
////    }
}
