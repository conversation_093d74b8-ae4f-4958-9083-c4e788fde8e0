package com.chidhagni.donationreceipt.mail;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmailVerifyDTO  {

    private UUID id;
    private String name;
    private String societyName;
    private String email;
    private String contactNumber;
//   private DefaultRolesEnums role;
    private String attribute1;
    private String attribute2;
    private String attribute3;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String ipAddress;
    private UUID createdBy;
    private UUID updatedBy;


}
