package com.chidhagni.donationreceipt.mail;

import com.chidhagni.donationreceipt.organisation.constants.DefaultOrganisationCategoryEnums;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;


@Data
@Builder
public class StagingEmailVerifyDTO {

    private UUID id;
    private String name;
    private String email;
    private String contactNumber;

    private DefaultOrganisationCategoryEnums role;

    private String attribute3;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String ipAddress;
    private UUID createdBy;
    private UUID updatedBy;

    //Fields Used In SignUp Flow
    private String organisationName;
    private UUID companyType;

    private String googleLocation;

}
