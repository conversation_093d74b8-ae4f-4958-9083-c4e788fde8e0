package com.chidhagni.donationreceipt.mail;


import org.springframework.stereotype.Component;

@Component
public class EmailVerifyDTOMapper {

//    public EmailVerifyDTO map(StagingEmailVerification stagingEmailVerification) {
//        EmailVerifyDTO.EmailVerifyDTOBuilder builder = EmailVerifyDTO.builder();
//
//        builder.id(stagingEmailVerification.getId())
//                .attribute1(stagingEmailVerification.getOrganisationName())
//                .attribute3(stagingEmailVerification.getAttribute3())
//                .name(stagingEmailVerification.getName())
//                .societyName(stagingEmailVerification.getSocietyName())
//                .email(stagingEmailVerification.getEmail())
//                .role(DefaultRolesEnums.valueOf(stagingEmailVerification.getRole()))
//                .contactNumber(stagingEmailVerification.getContactNumber())
//                .createdBy(stagingEmailVerification.getCreatedBy())
//                .updatedBy(stagingEmailVerification.getUpdatedBy())
//                .createdOn(stagingEmailVerification.getCreatedOn())
//                .updatedOn(stagingEmailVerification.getUpdatedOn());
//
//
//        return builder.build();
//    }
//
//
//    public StagingEmailVerification map(EmailVerifyDTO emailVerifyDTO) {
//
//        StagingEmailVerification stagingEmailVerification = new StagingEmailVerification();
//
//        if (emailVerifyDTO.getId() == null) {
//            stagingEmailVerification.setId(UUID.randomUUID());
//            stagingEmailVerification.setCreatedOn(DateUtils.currentTimeIST());
//        } else {
//            stagingEmailVerification.setId(emailVerifyDTO.getId());
//            stagingEmailVerification.setCreatedOn(emailVerifyDTO.getCreatedOn());
//        }
//
//        stagingEmailVerification.setIsVerified(false);
//        stagingEmailVerification.setEmail(emailVerifyDTO.getEmail());
//        stagingEmailVerification.setName(emailVerifyDTO.getName());
//        stagingEmailVerification.setIpAddress(emailVerifyDTO.getIpAddress());
//        stagingEmailVerification.setSocietyName(emailVerifyDTO.getSocietyName());
//        stagingEmailVerification.setContactNumber(emailVerifyDTO.getContactNumber());
//        stagingEmailVerification.setRole(String.valueOf(emailVerifyDTO.getRole()));
//        stagingEmailVerification.setOrganisationName(emailVerifyDTO.getAttribute1());
//        stagingEmailVerification.setOrganisationName(emailVerifyDTO.getAttribute2());
//        stagingEmailVerification.setAttribute3(emailVerifyDTO.getAttribute3());
//
//        if (emailVerifyDTO.getUpdatedOn() == null) {
//            stagingEmailVerification.setUpdatedOn(DateUtils.currentTimeIST());
//        } else {
//            stagingEmailVerification.setUpdatedOn(emailVerifyDTO.getUpdatedOn());
//        }
//        return stagingEmailVerification;
//    }
//
//
//
//    public StagingEmailVerification mapStagingEmailDTO(StagingEmailVerifyDTO stagingEmailVerifyDTO) {
//
//        StagingEmailVerification stagingEmailVerification = new StagingEmailVerification();
//
//        if (stagingEmailVerifyDTO.getId() == null) {
//            stagingEmailVerification.setId(UUID.randomUUID());
//            stagingEmailVerification.setCreatedOn(DateUtils.currentTimeIST());
//        } else {
//            stagingEmailVerification.setId(stagingEmailVerifyDTO.getId());
//            stagingEmailVerification.setCreatedOn(stagingEmailVerifyDTO.getCreatedOn());
//        }
//
//        stagingEmailVerification.setIsVerified(false);
//        stagingEmailVerification.setRole(String.valueOf(stagingEmailVerifyDTO.getRole()));
//        stagingEmailVerification.setEmail(stagingEmailVerifyDTO.getEmail());
//        stagingEmailVerification.setName(stagingEmailVerifyDTO.getName());
//        stagingEmailVerification.setIpAddress(stagingEmailVerifyDTO.getIpAddress());
//
//        stagingEmailVerification.setContactNumber(stagingEmailVerifyDTO.getContactNumber());
//
//        stagingEmailVerification.setOrganisationName(stagingEmailVerifyDTO.getOrganisationName());
//        stagingEmailVerification.setOrganisationType(stagingEmailVerifyDTO.getCompanyType());
//        stagingEmailVerification.setGoogleLocation(stagingEmailVerifyDTO.getGoogleLocation());
//
//        stagingEmailVerification.setAttribute3(stagingEmailVerifyDTO.getAttribute3());
//
//        if (stagingEmailVerifyDTO.getUpdatedOn() == null) {
//            stagingEmailVerification.setUpdatedOn(DateUtils.currentTimeIST());
//        } else {
//            stagingEmailVerification.setUpdatedOn(stagingEmailVerifyDTO.getUpdatedOn());
//        }
//        return stagingEmailVerification;
//    }
//
//    public EmailVerifyDTO maps(SignUpRequest signUpRequest) {
//        String fullName;
//        if (signUpRequest.getLastName() != null && !signUpRequest.getLastName().isEmpty()) {
//            fullName = signUpRequest.getFirstName() + " " + signUpRequest.getLastName();
//        } else {
//            fullName = signUpRequest.getFirstName();
//        }
//
//        return EmailVerifyDTO.builder()
//                .id(UUID.randomUUID())
//                .name(fullName)
//                .email(signUpRequest.getEmail())
//                .ipAddress(signUpRequest.getIpAddress())
//                .contactNumber(signUpRequest.getMobileNo())
//                .updatedOn(DateUtils.currentTimeIST())
//                .createdOn(DateUtils.currentTimeIST())
//                .build();
//    }
//
//    public StagingEmailVerifyDTO mapSignUpDTO(SignUpDTO signUpDTO) {
//
//            String lastName = signUpDTO.getLastName();
//            String fullName = signUpDTO.getFirstName();
//            if(lastName != null && !lastName.isEmpty()){
//                fullName += " " + signUpDTO.getLastName();
//            }
//            return StagingEmailVerifyDTO.builder()
//                    .id(UUID.randomUUID())
//                    .name(fullName)
//                    .email(signUpDTO.getEmail())
//                    .role(signUpDTO.getRole())
//                    .ipAddress(signUpDTO.getIpAddress())
//                    .contactNumber(signUpDTO.getMobileNo())
//                    .organisationName(signUpDTO.getOrganisationName())
//                    .companyType(signUpDTO.getCompanyType())
//                    .updatedOn(DateUtils.currentTimeIST())
//                    .createdOn(DateUtils.currentTimeIST())
//                    .googleLocation(signUpDTO.getGoogleLocation())
//                    .build();
//        }

}
