package com.chidhagni.donationreceipt.mail;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualVerificationAuditDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.IndividualPasswordResetRepository;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualSignUpDTO;
import com.chidhagni.donationreceipt.individualsignup.dto.request.ProcessEnum;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Random;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class EmailHelperService {

    @Value("${server.hostUrl}")
    public String siteUrl;

    @Value("${server.hostUrl}")
    public String resetUrl;

    private final IndividualVerificationAuditDao individualVerificationAuditDao;
    private final IndividualPasswordResetRepository individualPasswordResetAuditRepository;


    private final Random random = new Random();

    public IndividualPasswordResetAudit generateNewResetCode(IndividualPasswordResetAudit individualPasswordResetAudit) {

        String resetCode = RandomStringUtils.random(64, true, true);
        individualPasswordResetAudit.setResetLink(resetCode);
        individualPasswordResetAudit.setResetLinkRequestedAt(DateUtils.currentDatetime());
        individualPasswordResetAudit.setResetLinkExpiresAt(DateUtils.currentTimeIST().plusDays(1));
        return individualPasswordResetAudit;
    }


    public String generateActivationLink(String  email, UUID roleId) {

        String baseUrl = resetUrl + "/password";
        String fullLink = baseUrl + "?emailId=" + email
                + "&roleId=" + roleId
                + "&process=" + ProcessEnum.account_activation;
        return encodeLink(fullLink);
    }

    private String encodeLink(String link) {
        return UriComponentsBuilder.fromUriString(link).build().encode().toString();
    }

    public String generatePasswordResetLink(String resetLink, UUID roleId, String email) {
        String baseUrl = resetUrl + "/password";

        String fullLink = baseUrl + "?resetCode=" + resetLink
                + "&emailId=" + email
                + "&roleId=" + roleId
                + "&process=" + ProcessEnum.reset_password;

        return encodeLink(fullLink);
    }

}

