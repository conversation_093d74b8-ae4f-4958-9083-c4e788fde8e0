package com.chidhagni.donationreceipt.mail;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

@Configuration
public class MailConfig {

    @Value("${email.professionals}")
    public String professionalsEmail;

    @Value("${email.professionals.password}")
    public String professionalsEmailPassword;

    @Value("${email.societies}")
    public String societiesEmail;

    @Value("${email.societies.password}")
    public String societiesEmailPassword;

    @Value("${email.developers}")
    public String developersEmail;

    @Value("${email.developers.password}")
    public String developersEmailPassword;

    @Value("${email.registration}")
    public String registrationEmail;

    @Value("${email.registration.password}")
    public String registrationEmailPassword;

    @Value("${email.port}")
    public Integer emailPort;

    @Value("${email.host}")
    public String emailHost;

    @Primary
    @Bean("mailSetupProfessionals")
    public JavaMailSender mailSetupProfessionals() {
        return createMailSender(professionalsEmail, professionalsEmailPassword);
    }

    @Bean("mailSetupSocieties")
    public JavaMailSender mailSetupSocieties() {
        return createMailSender(societiesEmail, societiesEmailPassword);
    }

    @Bean("mailSetupDevelopers")
    public JavaMailSender mailSetupDevelopers() {
        return createMailSender(developersEmail, developersEmailPassword);
    }

    @Bean("mailSetupRegistration")
    public JavaMailSender mailSetupRegistration() { return createMailSender(registrationEmail,registrationEmailPassword);}

    private JavaMailSender createMailSender(String username, String password) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(emailHost);
        mailSender.setPort(emailPort);
        mailSender.setUsername(username);
        mailSender.setPassword(password);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.debug", "false");
        return mailSender;
    }
}
