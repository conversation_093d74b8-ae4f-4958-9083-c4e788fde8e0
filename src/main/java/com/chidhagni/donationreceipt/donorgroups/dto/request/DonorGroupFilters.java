package com.chidhagni.donationreceipt.donorgroups.dto.request;


import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

//@Data
@Builder
public class DonorGroupFilters {
    private final Map<String, Object> filters = new HashMap<>();


    @JsonAnyGetter
    public Map<String, Object> getFilters() {
        return filters;
    }

    @JsonAnySetter
    public void setFilter(String key, Object value) {
        this.filters.put(key, value);
    }
}
