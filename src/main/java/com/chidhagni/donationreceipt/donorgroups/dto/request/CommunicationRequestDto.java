package com.chidhagni.donationreceipt.donorgroups.dto.request;


import com.chidhagni.donationreceipt.donorgroups.constants.CommunicationModeEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunicationRequestDto {
    private CommunicationModeEnums communicationModeEnums;
    private String templateIdOrName;
}
