package com.chidhagni.donationreceipt.donorgroups.dto.response;


import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupFilters;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDonorGroupsResponse {
    private UUID id;
    private String name;
    private String description;
    private DonorGroupFilters filters;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private Boolean isActive;
    private UUID orgId;
    private List<DonorResponse> donorResponseList;

}
