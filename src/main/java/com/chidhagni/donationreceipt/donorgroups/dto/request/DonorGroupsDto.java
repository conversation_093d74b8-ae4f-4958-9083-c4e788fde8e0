package com.chidhagni.donationreceipt.donorgroups.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorGroupsDto {
    private String name;
    private String description;
    private DonorGroupFilters filters;
    private UUID orgId;
    private List<UUID> donorIds;
}
