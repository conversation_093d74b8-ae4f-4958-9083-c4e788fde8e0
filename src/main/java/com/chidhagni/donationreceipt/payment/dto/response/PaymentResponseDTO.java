package com.chidhagni.donationreceipt.payment.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentResponseDTO {
    private UUID id;
    private UUID subscriptionId;
    private UUID packageId;
    private UUID upgradedForm;
    private String failureDescription;
    private UUID orgId;
    private String orgName;
    private UUID individualId;
    private String individualName;
    private String paymentMethod;
    private String paymentGateWay;
    private String orderID;
    private String status;
    private Double price;
    private Boolean isPaid;
    private Boolean isActive;
    private String createdByEmail;
    private String updatedByEmail;
    private LocalDateTime updatedOn;
    private LocalDateTime createdOn;
    private String packageName;
}
