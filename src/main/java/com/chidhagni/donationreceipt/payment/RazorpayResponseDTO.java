package com.chidhagni.donationreceipt.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RazorpayResponseDTO {
        private String id;
        private String entity;
        private Integer amount;
        private String currency;
        private String status;
        private String order_id;
        private String invoice_id;
        private Boolean international;
        private String method;
        private Integer amount_refunded;
        private String refund_status;
        private Boolean captured;
        private String description;
        private String card_id;
        private String bank;
        private String wallet;
        private String email;
        private String contact;
        private String customer_id;
        private String token_id;
        private String[] notes;
        private Integer fee;
        private Integer tax;
        private String error_code;
        private String errorDescription;
        private String error_source;
        private String error_step;
        private String error_reason;
        private AcquirerData acquirer_data;
        private Integer created_at;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @Builder
        public static  class AcquirerData {
                private String auth_code;
                private String arn;
                private String rrn;
        }
}

