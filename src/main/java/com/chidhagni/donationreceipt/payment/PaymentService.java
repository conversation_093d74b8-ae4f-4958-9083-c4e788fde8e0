
package com.chidhagni.donationreceipt.payment;

import org.springframework.stereotype.Service;

@Service
public class PaymentService {
//    private static final Logger log = LoggerFactory.getLogger(PaymentService.class);
//    @Autowired
//    private RazorpayClient razorpayClient;
//    @Autowired
//    private RazorPayConfig razorpayConfig;
//    @Autowired
//    private SubscriptionsDao subscriptionsDao;
//    @Autowired
//    private PaymentMapper paymentMapper;
//    @Autowired
//    private PaymentsDao paymentsDao;
//    @Autowired
//    private SubscriptionService subscriptionService;
//    @Autowired
//    private DSLContext dslContext;
//    @Autowired
//    private RazorpayMapper razorpayMapper;
//    @Autowired
//    private PaymentsRepository paymentsRepository;
//    private Integer pageNo = 1;
//    private Integer pageSize = 10;
//
//
//    /**
//     * Processes and creates a new payment transaction.
//     *
//     *
//     * <p>This method handles the creation of a new payment transaction by verifying the payment,
//     * validating the subscription, storing payment details, and updating the subscription status.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Retrieves the user ID from 'userPrincipal'.</li>
//     *   <li>Extracts subscription details and failure description from `paymentPayloadDTO`.</li>
//     *   <li>Verifies if the payment was successful using 'verifyPayment()'.</li>
//     *   <li>Validates the subscription using 'findIfValid()'.</li>
//     *   <li>Maps the payment payload to a 'Payments' entity and inserts it into the database.</li>
//     *   <li>Logs the payment transaction and updates the subscription status via 'subscriptionService'.</li>
//     *   <li>Converts and returns the payment entity as a 'PaymentDTO'.</li>
//     * </ul>
//     *
//     * <p><b>Request Parameters:</b></p>
//     * <ul>
//     *   <li>'paymentPayloadDTO' - Contains payment details, subscription ID, and failure description.</li>
//     *   <li>'userPrincipal' - Represents the authenticated user initiating the payment.</li>
//     * </ul>
//     *
//     * @param paymentPayloadDTO The request payload containing payment details.
//     * @param userPrincipal The authenticated user performing the transaction.
//     * @return A `PaymentDTO` containing the created payment details.
//     * @throws RazorpayException If an error occurs during payment verification or processing.
//     */
//    public PaymentDTO createPayment(PaymentPayloadDTO paymentPayloadDTO, UserPrincipal userPrincipal)
//            throws RazorpayException {
//        UUID userId = userPrincipal.getId();
//        UUID subscriptionID = paymentPayloadDTO.getSubscriptionId();
//        String failureDescription = paymentPayloadDTO.getFailureDescription();
//        Boolean isPaid = verifyPayment(paymentPayloadDTO);
//        Boolean ifValid = findIfValid(subscriptionID);
//        String paymentGateway = paymentPayloadDTO.getPaymentGateway();
//        Payments payments = paymentMapper.payloadDTOToPayment(paymentPayloadDTO, isPaid, ifValid, userId);
//        paymentsDao.insert(payments);
//        log.info("Updating the Subscription after the payment");
//        subscriptionService.updateStatusAndGateway(subscriptionID, failureDescription, isPaid, paymentGateway);
//        return paymentMapper.paymentToPaymentDTO(payments);
//    }
//
//    /**
//     * Retrieves the details of a specific payment transaction by paymentId.
//     *
//     *
//     * <p>This method fetches the details of a payment transaction using its ID.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Attempts to retrieve the payment record from the database using 'paymentId'.</li>
//     *   <li>If the payment is not found, logs a warning and throws an 'IllegalArgumentException'.</li>
//     *   <li>Maps the retrieved 'Payments' entity to a 'PaymentDTO'.</li>
//     *   <li>Returns the payment details in 'PaymentDTO' format.</li>
//     * </ul>
//     *
//     *
//     * @param paymentId The unique identifier of the payment transaction.
//     * @return A 'PaymentDTO' containing the payment details.
//     * @throws IllegalArgumentException If the payment transaction is not found.
//     *
//     */
//    public PaymentDTO getPaymentDetails(UUID paymentId) {
//        Optional<Payments> optionalSubscription = Optional.ofNullable(paymentsDao.fetchOneById(paymentId));
//        Payments payments = optionalSubscription.orElseThrow(() -> {
//                    log.warn("Payment with given id not found::{}", paymentId);
//                    return new IllegalArgumentException("Payment with the given id is not found");
//                }
//        );
//        return paymentMapper.paymentToPaymentDTO(payments);
//    }
//
//    /**
//     * Retrieves the Razorpay response details for a specific payment.
//     *
//     * <p>This method fetches the payment response from Razorpay using the provided ID.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Uses 'razorpayClient' to fetch the payment details from Razorpay.</li>
//     *   <li>Maps the 'Payment' object from Razorpay to a 'RazorpayResponseDTO'.</li>
//     *   <li>Returns the Razorpay payment response in 'RazorpayResponseDTO' format.</li>
//     * </ul>
//     *
//     *
//     * @param razorpayPaymentId The unique identifier of the Razorpay payment transaction.
//     * @return A `RazorpayResponseDTO` containing the Razorpay payment details.
//     * @throws Exception If an error occurs while retrieving the Razorpay response.
//     */
//    public RazorpayResponseDTO getRazorpayResponse(String razorpayPaymentId) throws Exception {
//        Payment payment = razorpayClient.Payments.fetch(razorpayPaymentId);
//        return razorpayMapper.razorpayResponseToDTO(payment);
//    }
//
//
//    /**
//     * Retrieves a paginated list of all payment transactions.
//     *
//     *
//     * <p>This method fetches all active and paid payment transactions from the database
//     * with pagination support.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Ensures 'searchKeyword', `page`, and `pageSize` have default values if not provided.</li>
//     *   <li>Calculates the offset based on the current page number.</li>
//     *   <li>Performs a database query to retrieve payment records, including related subscription and user details.</li>
//     *   <li>Filters only active and paid payments.</li>
//     *   <li>Orders results by the creation date in descending order.</li>
//     *   <li>Fetches the total count of matching payment records.</li>
//     *   <li>Constructs and returns a 'GetAllResponseDTO' containing the payment list, pagination details, and total count.</li>
//     * </ul>
//     *
//     *
//     * @param paginationRequest The request object containing pagination and filtering details.
//     * @return A `GetAllResponseDTO` containing the list of payments, pagination details, and total count.
//     */
//    public GetAllResponseDTO getAllPayments(PaginationRequest paginationRequest) {
//
//        if (paginationRequest.getSearchKeyword() == null || paginationRequest.getSearchKeyword().isBlank()) {
//            paginationRequest.setSearchKeyword("");
//        }
//
//        if (paginationRequest.getPage() == null || paginationRequest.getPage() == 0) {
//            paginationRequest.setPage(1);
//        }
//
//        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() == 0) {
//            paginationRequest.setPageSize(10);
//        }
//
//        int pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
//
//        List<PaymentDTO> paymentDTOList = dslContext.select(
//                        PAYMENTS.ID.as("id")
//                        , PAYMENTS.SUBSCRIPTION_ID.as("subscriptionId")
//                        , PAYMENTS.PAYMENT_METHOD.as("paymentMethod")
//                        , PAYMENTS.PAYMENT_GATEWAY.as("paymentGateway")
//                        , PAYMENTS.ORDER_ID.as("orderId")
//                        , PAYMENTS.STATUS.as("status")
//                        , PAYMENTS.PRICE.as("price")
//                        , PAYMENTS.IS_PAID.as("isPaid")
//                        , PAYMENTS.IS_ACTIVE.as("isActive")
//                        , PAYMENTS.CREATED_BY.as("createdBy")
//                        , PAYMENTS.UPDATED_BY.as("updatedBy")
//                        , PAYMENTS.CREATED_ON.as("createdOn")
//                        , PAYMENTS.UPDATED_ON.as("updatedOn")
//                        , INDIVIDUAL.EMAIL.as("email")
//                )
//                .from(PAYMENTS)
//                .join(SUBSCRIPTIONS).on(PAYMENTS.SUBSCRIPTION_ID.eq(SUBSCRIPTIONS.ID))
//                .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(SUBSCRIPTIONS.SP_ORGANISATION_ID))
//                .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
//                .where(PAYMENTS.IS_ACTIVE.eq(true).and(PAYMENTS.IS_PAID.eq(true)))
//                .orderBy(PAYMENTS.CREATED_ON.desc())
//                .limit(paginationRequest.getPageSize())
//                .offset(pageNo)
//                .fetchInto(PaymentDTO.class);
//
//        Integer count = dslContext.selectCount()
//                .from(PAYMENTS)
//                .join(SUBSCRIPTIONS).on(PAYMENTS.SUBSCRIPTION_ID.eq(SUBSCRIPTIONS.ID))
//                .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(SUBSCRIPTIONS.SP_ORGANISATION_ID))
//                .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
//                .where(PAYMENTS.IS_ACTIVE.eq(true).and(PAYMENTS.IS_PAID.eq(true)))
//                .fetchOne(0, Integer.class);
//
//        return GetAllResponseDTO.builder()
//                .paymentDTOList(paymentDTOList)
//                .paginationRequest(paginationRequest)
//                .rowCount(count == null ? 0 : count)
//                .build();
//    }
//
//    /**
//     * Soft deletes a payment transaction by marking it as inactive.
//     *
//     * <p>This method deactivates a payment record by setting its 'isActive' flag to `false`,
//     * effectively performing a soft delete.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Attempts to retrieve the payment record from the database using `paymentId`.</li>
//     *   <li>If the payment is not found, logs a warning and throws an 'IllegalArgumentException'.</li>
//     *   <li>Updates the 'isActive' flag of the payment record to 'false'.</li>
//     *   <li>Commits the update operation to the database.</li>
//     * </ul>
//     *
//     *
//     * @param paymentId The unique identifier of the payment transaction.
//     * @throws IllegalArgumentException If the payment transaction is not found.
//     */
//    public void deletePayment(UUID paymentId) {
//        Optional<Payments> optionalSubscription = Optional.ofNullable(paymentsDao.fetchOneById(paymentId));
//        Payments payments = optionalSubscription.orElseThrow(() -> {
//                    log.warn("Payment with the given id is not found::{}", paymentId);
//                    return new IllegalArgumentException("Payment with the given id is not found");
//                }
//        );
//        payments.setIsActive(false);
//        paymentsDao.update(payments);
//    }
//
//    /**
//     * Verifies the payment signature for a Razorpay transaction.
//     *
//     *
//     * <p>This method validates a Razorpay payment by verifying the provided order ID,
//     * payment ID, and signature using Razorpay's client secret.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Checks if 'razorpayPaymentId', 'razorpaySignature', and 'orderId' are present and non-blank.</li>
//     *   <li>Extracts payment details from 'paymentPayloadDTO'.</li>
//     *   <li>Logs the extracted Razorpay order ID, payment ID, and signature for debugging purposes.</li>
//     *   <li>Creates a JSON object containing the extracted Razorpay details.</li>
//     *   <li>Uses 'Utils.verifyPaymentSignature()' to validate the payment signature with Razorpay's client secret.</li>
//     *   <li>Returns `true` if the payment verification is successful; otherwise, returns `false`.</li>
//     * </ul>
//     *
//     *
//     * @param paymentPayloadDTO The request payload containing Razorpay payment details.
//     * @return `true` if the payment signature is verified successfully, otherwise `false`.
//     * @throws RazorpayException If an error occurs during Razorpay verification.
//     */
//
//    public boolean verifyPayment(PaymentPayloadDTO paymentPayloadDTO) throws RazorpayException {
//        boolean isVerified = false;
//
//        if (StringUtils.isNotBlank(paymentPayloadDTO.getRazorpayPaymentId())
//                && StringUtils.isNotBlank(paymentPayloadDTO.getRazorpaySignature())
//                && StringUtils.isNotBlank(paymentPayloadDTO.getOrderId())) {
//            String razorpay_order_id = paymentPayloadDTO.getOrderId();
//            String razorpay_payment_id = paymentPayloadDTO.getRazorpayPaymentId();
//            String razorpay_signature = paymentPayloadDTO.getRazorpaySignature();
//
//            log.info("razorpay_order_id::{}", razorpay_order_id);
//            log.info("razorpay_payment_id::{}", razorpay_payment_id);
//            log.info("razorpay_signature::{}", razorpay_signature);
//
//            JSONObject options = new JSONObject();
//            options.put("razorpay_order_id", razorpay_order_id);
//            options.put("razorpay_payment_id", razorpay_payment_id);
//            options.put("razorpay_signature", razorpay_signature);
//
//            isVerified = Utils.verifyPaymentSignature(options, razorpayConfig.getRazorpay_clientSecret());
//        }
//        return isVerified;
//    }
//
//    public boolean findIfValid(UUID subscriptionId) {
//        // Fetch the subscription from the database
//        Subscriptions subscriptions = subscriptionsDao.fetchOneById(subscriptionId);
//
//        // Check if subscriptions is null or its endDate is null
//        if (subscriptions == null || subscriptions.getEndDate() == null) {
//            return false;
//        }
//
//        // Compare current date with endDate
//        return DateUtils.currentDate().isAfter(subscriptions.getEndDate()) ? false : true;
//    }
//
//
//    /**
//     * Retrieves a paginated list of all payment transactions for admin.
//     *
//     *
//     * <p>This method allows admin to fetch a list of all payments in the system,
//     * applying pagination settings if provided.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Applies default pagination values using 'applyDefaults()' if necessary.</li>
//     *   <li>Fetches the paginated list of payments from 'paymentsRepository.getAllPaymentsByAdmin()'.</li>
//     *   <li>Retrieves the total count of payments from 'paymentsRepository.getAllPaymentsCountByAdmin()'.</li>
//     *   <li>Constructs and returns a 'GetAllPaymentsResponseDTO' containing the payment list and total record count.</li>
//     * </ul>
//     *
//     *
//     *
//     * @param paginationRequest The request object containing pagination and filtering details.
//     * @return A 'GetAllPaymentsResponseDTO' containing the list of payments and total count.
//     */
//    public GetAllPaymentsResponseDTO getAllPaymentsResponseDTO(PaginationRequest paginationRequest) {
//        applyDefaults(paginationRequest);
//
//        List<PaymentResponseDTO> allPaymentsResponseDTO = paymentsRepository.getAllPaymentsByAdmin(paginationRequest);
//        Integer allPaymentsResponseCount = paymentsRepository.getAllPaymentsCountByAdmin();
//        return GetAllPaymentsResponseDTO.builder()
//                .paymentsDTOList(allPaymentsResponseDTO)
//                .rowCount(allPaymentsResponseCount)
//                .build();
//    }
//
//    private void applyDefaults(PaginationRequest request) {
//        if (request.getPage() == null || request.getPage() < 1) {
//            request.setPage(pageNo);
//        }
//
//        if (request.getPageSize() == null || request.getPageSize() < 1) {
//            request.setPageSize(pageSize);
//        }
//    }

}

