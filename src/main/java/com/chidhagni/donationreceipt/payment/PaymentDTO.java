package com.chidhagni.donationreceipt.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentDTO {
    private UUID id;
    private UUID subscriptionId;
    private String paymentMethod;
    private String paymentGateway;
    private String orderId;
    private String status;
    private BigDecimal price;
    private Boolean isPaid;
    private Boolean isActive;
    private String email;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
