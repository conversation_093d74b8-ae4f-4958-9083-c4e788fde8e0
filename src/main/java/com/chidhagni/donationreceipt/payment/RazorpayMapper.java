package com.chidhagni.donationreceipt.payment;

import com.razorpay.Payment;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

@Component
public class RazorpayMapper {

    public RazorpayResponseDTO razorpayResponseToDTO(Payment payment) {

        RazorpayResponseDTO dto = new RazorpayResponseDTO();

        Object methodObj = payment.get("method");
        Object errorDescriptionObj = payment.get("error_description");
        String method = null;
        String errorDescription= null;

        if (methodObj != JSONObject.NULL) {
            method = (String) methodObj;
        } else {
            method = null;
        }
        if(errorDescriptionObj != JSONObject.NULL){
            errorDescription=(String) errorDescriptionObj;
        }
        dto.setMethod(method);
        dto.setErrorDescription(errorDescription);
        System.out.println("Error Desacription @@@@@@@@@@@@!!!!!!!!"+errorDescription+" "+ dto.getErrorDescription());
        return dto;
    }
}
