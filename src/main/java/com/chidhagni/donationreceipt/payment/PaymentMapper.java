
package com.chidhagni.donationreceipt.payment;


import org.springframework.stereotype.Component;

@Component
public class PaymentMapper {
//    public Payments payloadDTOToPayment(PaymentPayloadDTO paymentPayloadDTO,Boolean isVerified, Boolean isValid, UUID userId){
//        Payments payments=new Payments();
//        String status=getStatus(isValid);
//        UUID subscriptionId=paymentPayloadDTO.getSubscriptionId();
//
//        BigDecimal amountInPaisa = paymentPayloadDTO.getPrice();
//        BigDecimal amountInRupees = amountInPaisa.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
//
//
//        UUID paymentId=UUID.randomUUID();
//        payments.setId(paymentId);
//        payments.setSubscriptionId(subscriptionId);
//        payments.setPaymentMethod(paymentPayloadDTO.getPaymentMethod());
//        payments.setPaymentGateway(paymentPayloadDTO.getPaymentGateway());
//        payments.setOrderId(paymentPayloadDTO.getOrderId());
//        payments.setStatus(status);
//        payments.setPrice(amountInRupees);
//        payments.setIsPaid(isVerified);
//        payments.setIsActive(isValid);
//        payments.setCreatedBy(userId);
//        payments.setUpdatedBy(userId);
//        payments.setCreatedOn(DateUtils.currentTimeIST());
//        payments.setUpdatedOn(DateUtils.currentTimeIST());
//        return payments;
//    }
//
//    public PaymentDTO paymentToPaymentDTO(Payments payments){
//
//        return PaymentDTO.builder()
//                .id(payments.getId())
//                .subscriptionId(payments.getSubscriptionId())
//                .paymentMethod(payments.getPaymentMethod())
//                .paymentGateway(payments.getPaymentGateway())
//                .orderId(payments.getOrderId())
//                .status(payments.getStatus())
//                .price(payments.getPrice())
//                .isPaid(payments.getIsPaid())
//                .isActive(payments.getIsActive())
//                .createdOn(payments.getCreatedOn())
//                .updatedOn(payments.getUpdatedOn())
//                .createdBy(payments.getUpdatedBy())
//                .updatedBy(payments.getUpdatedBy())
//                .build();
//    }
//
//    public Payments PaymentDTOToPayment(PaymentDTO paymentDTO){
//        Payments payments=new Payments();
//        payments.setId(paymentDTO.getId());
//        payments.setSubscriptionId(paymentDTO.getSubscriptionId());
//        payments.setPaymentMethod(paymentDTO.getPaymentMethod());
//        payments.setPaymentGateway(paymentDTO.getPaymentGateway());
//        payments.setOrderId(paymentDTO.getOrderId());
//        payments.setStatus(paymentDTO.getStatus());
//        payments.setPrice(paymentDTO.getPrice());
//        payments.setIsPaid(paymentDTO.getIsPaid());
//        payments.setIsActive(paymentDTO.getIsActive());
//        payments.setCreatedBy(paymentDTO.getCreatedBy());
//        payments.setUpdatedBy(paymentDTO.getUpdatedBy());
//        payments.setCreatedOn(paymentDTO.getCreatedOn());
//        payments.setUpdatedOn(paymentDTO.getUpdatedOn());
//        return payments;
//    }
//    public SubscriptionResponseDTO subscriptionToResponseDTO(Subscriptions subscriptions){
//        return SubscriptionResponseDTO.builder()
//                .subscriptionId(subscriptions.getId())
//                .orderId(subscriptions.getOrderId())
//                .price(subscriptions.getPrice())
//                .build();
//    }

    public String getStatus(Boolean isValid){
        return isValid?PaymentEnums.ACTIVE.name():PaymentEnums.INACTIVE.name();
    }
}

