package com.chidhagni.donationreceipt.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentPayloadDTO {
    private UUID subscriptionId;
    private BigDecimal price;
    private String currency;
    private String paymentGateway;
    private String paymentMethod;
    private String  orderId;
    private String  razorpayPaymentId;
    private String razorpaySignature;
    private String failureDescription;
}
