package com.chidhagni.donationreceipt.IndividualLogin;


import com.chidhagni.donationreceipt.IndividualLogin.dto.request.IndividualLoginRequest;
import com.chidhagni.donationreceipt.security.LoginResponse;
import com.chidhagni.utils.Token;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class IndividualLoginController {

    private final IndividualLoginService individualLoginService;

    @PostMapping("/api/v1/individual-login")
    public ResponseEntity<?> individualSignUp(@RequestBody IndividualLoginRequest individualLoginRequest) {

        Token token = individualLoginService.login(individualLoginRequest);
        return ResponseEntity.ok(new LoginResponse(token, null));
    }
}

