package com.chidhagni.donationreceipt.IndividualLogin.dto.request;

import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@Data
@Builder
public class IndividualLoginRequest {

    @Email(regexp = "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,3}", flags = Pattern.Flag.CASE_INSENSITIVE)
    @NotNull
    private String email;
    @NotNull
    @Pattern(
            regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,64}$",
            message = "Password must be 8-64 characters long and include at least one digit, one uppercase letter, " +
                        "one lowercase letter, and one special character."
    )
    private String password;

    @Builder.Default
    private boolean overrideExistingLogins = Boolean.TRUE;

    //private Boolean overrideExistingLogins = Boolean.TRUE;


    @Pattern(
            regexp = "^((25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)$",
            message = "Invalid IP address format"
    )
    private String ipAddress;

}
