package com.chidhagni.donationreceipt.IndividualLogin;
import com.chidhagni.donationreceipt.IndividualLogin.dto.request.IndividualLoginRequest;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualSessionsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions;
import com.chidhagni.donationreceipt.individualLogout.IndividualLogoutService;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.JwtTokenUtil;
import com.chidhagni.utils.Token;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class IndividualLoginService {
    private final AuthenticationManager authenticationManager;
    private final IndividualDao individualDao;
    private final IndividualRoleRepository individualRoleRepository;
    private final IndividualSessionsDao userSessionsDao;
    private final IndividualLogoutService logoutService;
    private final JwtTokenUtil jwtTokenUtil;
    private final IndividualRoleDao individualRoleDao;


    @Value("${service.provider.roles.id}")
    public UUID serviceProviderRoleId;

    @Value("${society.roles.id}")
    public UUID societyRoleId;

    public Token login(IndividualLoginRequest loginRequest) {


        String email = loginRequest.getEmail();
        String password = loginRequest.getPassword();

        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(email, password));

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        Individual individual = individualDao.fetchOneById(userPrincipal.getId());

        if (!loginRequest.isOverrideExistingLogins()) {
            List<IndividualSessions> userSessions = userSessionsDao.fetchByIndividualId(individual.getId());

            if (!userSessions.isEmpty()) {
                if (userSessions.get(0).getRefreshtokenExpirytime().isAfter(LocalDateTime.now(ZoneOffset.UTC))) {
                    return Token.builder().message("Already Logged in").build();
                }
            }
        }

        //NOTE: It will execute if getOverrideExistingLogins is true, the fetched userSessions are empty, and the
        // refresh token has expired.
        logoutService.logout(userPrincipal);

        Token token =
                Token.builder()
                        .accessToken(jwtTokenUtil.generateAccessToken(individual, UUID.randomUUID()))
                        .refreshToken(jwtTokenUtil.generateRefreshToken(individual, UUID.randomUUID()))
                        .build();

        IndividualSessions individualSessions = buildUserSession(individual, token, loginRequest);
        userSessionsDao.insert(individualSessions);

        token.setMessage("Login Successful");


        //TODO: How session timeout is happening using the JWT token.  We can ask in the Udemy community support.
        return token;
    }

    private IndividualSessions buildUserSession(Individual individual, Token token, IndividualLoginRequest loginRequest) {
        IndividualSessions userSessions = new IndividualSessions();

        userSessions.setId(UUID.randomUUID());
        userSessions.setIndividualId(individual.getId());

        userSessions.setAccesstoken(token.getAccessToken());
        userSessions.setAccesstokenExpirytime(jwtTokenUtil.getExpiryTimestamp(token.getAccessToken()));
        userSessions.setAccesstokenGeneratedOn(jwtTokenUtil.getIssuedAtTimestamp(token.getAccessToken()));

        userSessions.setRefreshtoken(token.getRefreshToken());
        userSessions.setRefreshtokenExpirytime(jwtTokenUtil.getExpiryTimestamp(token.getRefreshToken()));
        userSessions.setRefreshtokenGeneratedOn(jwtTokenUtil.getIssuedAtTimestamp(token.getRefreshToken()));

        userSessions.setIpaddress(loginRequest.getIpAddress());

        return userSessions;
    }

}
