package com.chidhagni.donationreceipt.individualpermission;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Slf4j
@Repository
@RequiredArgsConstructor
public class IndividualPermissionRepository {
   private final IndividualPermissionDao individualPermissionDao;

    public List<IndividualPermission> getIndividualPermissionByIndividualId(UUID individualId) {
        try {
            return individualPermissionDao.fetchByIndividualId(individualId);
        } catch (Exception ex) {
            throw new InternalServerError("Exception while retrieving the individual permissions by individual id");
        }
    }

    public void upsertIndividualPermissions(List<IndividualPermission> individualPermissions) {
        try {
            individualPermissionDao.merge(individualPermissions);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while upsert individual permissions", ex);
        }
    }
//
    public void insertIndividualPermission(IndividualPermission individualPermission){
        try{
            individualPermissionDao.insert(individualPermission);
            log.info("successfully insert the individual permission with primary id :: {}", individualPermission.getId());
        }catch(Exception ex){
           throw new InternalServerError("Exception while inserting the individual permission", ex);
        }
    }


}
