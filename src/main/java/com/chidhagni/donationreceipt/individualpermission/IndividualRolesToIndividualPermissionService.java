package com.chidhagni.donationreceipt.individualpermission;



import com.chidhagni.donationreceipt.common.exception.PermissionsNotFound;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualPermission.INDIVIDUAL_PERMISSION;


@RequiredArgsConstructor
@Service
@Slf4j
public class IndividualRolesToIndividualPermissionService {


    private final IndividualPermissionDao individualPermissionDao;
    private final DSLContext dslContext;
    private final ObjectMapper objectMapper;




    // TODO need to add cache mechanism and get the data from cache
    public String getPermissionsByIndividualId(UUID individualId) {
        try {
            List<RoleNode> roleNodes = dslContext
                    .select(INDIVIDUAL_PERMISSION.PERMISSIONS)
                    .from(INDIVIDUAL_PERMISSION)
                    .where(INDIVIDUAL_PERMISSION.INDIVIDUAL_ID.eq(individualId))
                    .fetchOne(INDIVIDUAL_PERMISSION.PERMISSIONS);

            if (roleNodes == null) {
                log.warn("No permissions found for individual ID: {}", individualId);
                return "";
            }
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(roleNodes);
        } catch (Exception e) {
            log.error("Database access error while fetching permissions for individual ID: {}", individualId, e);
            throw new PermissionsNotFound("Failed to retrieve permissions due to database error.", e);
        }
    }


    /**
     * fetch the permissions by individual from individual permissions by primary id.
     * @param individualId
     * @return individual permission in form of tree (list of RoleNode)
     */
    public List<RoleNode> fetchPermissionsByIndividualId(UUID individualId) {
        return individualPermissionDao.ctx().select(INDIVIDUAL_PERMISSION.PERMISSIONS).from(INDIVIDUAL_PERMISSION)
                .where(INDIVIDUAL_PERMISSION.INDIVIDUAL_ID.eq(individualId))
                .fetchOne(INDIVIDUAL_PERMISSION.PERMISSIONS, List.class);
    }



}
