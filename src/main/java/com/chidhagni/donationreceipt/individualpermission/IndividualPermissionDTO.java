package com.chidhagni.donationreceipt.individualpermission;

import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IndividualPermissionDTO {
    private UUID individualId;
    private List<RoleNode> permission;
    private UUID orgId;
}
