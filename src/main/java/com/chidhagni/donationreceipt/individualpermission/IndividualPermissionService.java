package com.chidhagni.donationreceipt.individualpermission;

import com.chidhagni.donationreceipt.individualpermission.util.IIndividualPermissionMapper;
import com.chidhagni.donationreceipt.roles.RolesService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class IndividualPermissionService {
    private final RolesService rolesService;
    private final IIndividualPermissionMapper interfaceIndividualPermissionMapper;
    @Value("${service.provider.roles.id}")
    private UUID serviceProviderId;
    @Value("${society.roles.id}")
    private UUID societyId;
    @Value("${employee.roles.id}")
    private UUID employeeRoleId;
    private final IndividualPermissionRepository individualPermissionRepository;

}
