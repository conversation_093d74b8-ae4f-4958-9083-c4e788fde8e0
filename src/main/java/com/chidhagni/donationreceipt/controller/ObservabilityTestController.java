package com.chidhagni.donationreceipt.controller;

import com.chidhagni.donationreceipt.services.ObservabilityService;
import com.chidhagni.utils.TracingUtil;
import com.chidhagni.utils.OpenTelemetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Test controller for verifying observability functionality.
 * This controller provides endpoints to test tracing and metrics.
 */
@RestController
@RequestMapping("/pheart/observability")
@Slf4j
public class ObservabilityTestController {

    @Autowired
    private ObservabilityService observabilityService;
    
    @Autowired
    private TracingUtil tracingUtil;
    
    @Autowired
    private OpenTelemetryUtil openTelemetryUtil;

    /**
     * Test endpoint for donation receipt processing with tracing
     */
    @PostMapping("/test-donation-receipt")
    public ResponseEntity<Map<String, Object>> testDonationReceipt(
            @RequestParam String donorId,
            @RequestParam double amount) {
        
        log.info("Testing donation receipt processing for donor: {} with amount: {}", donorId, amount);
        
        try {
            String result = observabilityService.processDonationReceipt(donorId, amount);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", result);
            response.put("donorId", donorId);
            response.put("amount", amount);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error testing donation receipt processing", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Test endpoint for database operation tracing
     */
    @PostMapping("/test-database-operation")
    public ResponseEntity<Map<String, Object>> testDatabaseOperation(
            @RequestParam String tableName,
            @RequestParam String operation) {
        
        log.info("Testing database operation: {} on table: {}", operation, tableName);
        
        try {
            String result = observabilityService.simulateDatabaseOperation(tableName, operation);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", result);
            response.put("tableName", tableName);
            response.put("operation", operation);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error testing database operation", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Test endpoint for HTTP client operation tracing
     */
    @PostMapping("/test-http-client")
    public ResponseEntity<Map<String, Object>> testHttpClientOperation(
            @RequestParam String url,
            @RequestParam String method) {
        
        log.info("Testing HTTP client operation: {} {}", method, url);
        
        try {
            String result = observabilityService.simulateHttpClientOperation(url, method);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", result);
            response.put("url", url);
            response.put("method", method);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error testing HTTP client operation", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Comprehensive test endpoint that generates complex traces
     */
    @PostMapping("/test-comprehensive-trace")
    public ResponseEntity<Map<String, Object>> testComprehensiveTrace() {
        return tracingUtil.traceMethod("comprehensive_trace_test", () -> {
            String traceId = UUID.randomUUID().toString();
            
            try {
                log.info("Starting comprehensive trace test with ID: {}", traceId);
                openTelemetryUtil.logTraceEvent("comprehensive_test_started", traceId);
                
                // Add trace context
                tracingUtil.setAttribute("test.id", traceId);
                tracingUtil.setAttribute("test.type", "comprehensive");
                tracingUtil.setAttribute("test.version", "1.0");
                tracingUtil.addEvent("test.initialization.completed");
                
                // Simulate donation processing
                tracingUtil.addEvent("donation.processing.started");
                String donationResult = observabilityService.processDonationReceipt("TEST_DONOR_" + traceId.substring(0, 8), 1000.0);
                tracingUtil.addEvent("donation.processing.completed");
                
                // Simulate database operations
                tracingUtil.addEvent("database.operations.started");
                String dbResult1 = observabilityService.simulateDatabaseOperation("donation_receipts", "SELECT");
                String dbResult2 = observabilityService.simulateDatabaseOperation("donors", "UPDATE");
                tracingUtil.addEvent("database.operations.completed");
                
                // Simulate HTTP client calls
                tracingUtil.addEvent("external.calls.started");
                String httpResult1 = observabilityService.simulateHttpClientOperation("https://api.payment.com/verify", "POST");
                String httpResult2 = observabilityService.simulateHttpClientOperation("https://api.email.com/send", "POST");
                tracingUtil.addEvent("external.calls.completed");
                
                // Add business metrics
                tracingUtil.setAttribute("donation.amount", "1000.0");
                tracingUtil.setAttribute("donation.currency", "USD");
                tracingUtil.setAttribute("donor.type", "individual");
                tracingUtil.setAttribute("receipt.generated", "true");
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("traceId", traceId);
                response.put("donationResult", donationResult);
                response.put("databaseResults", Map.of("select", dbResult1, "update", dbResult2));
                response.put("httpResults", Map.of("payment", httpResult1, "email", httpResult2));
                response.put("timestamp", System.currentTimeMillis());
                response.put("message", "Comprehensive trace test completed successfully");
                
                tracingUtil.addEvent("test.completed.successfully");
                openTelemetryUtil.logTraceEvent("comprehensive_test_completed", traceId);
                
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                log.error("Error in comprehensive trace test", e);
                tracingUtil.addEvent("test.error.occurred", "error", e.getMessage());
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("traceId", traceId);
                response.put("error", e.getMessage());
                
                return ResponseEntity.internalServerError().body(response);
            }
        });
    }
    
    /**
     * Test endpoint for generating traces with different business scenarios
     */
    @PostMapping("/test-business-scenario")
    public ResponseEntity<Map<String, Object>> testBusinessScenario(
            @RequestParam(defaultValue = "donation") String scenario,
            @RequestParam(defaultValue = "individual") String donorType,
            @RequestParam(defaultValue = "1000") Double amount) {
        
        return tracingUtil.traceMethod("business_scenario_test", () -> {
            String sessionId = UUID.randomUUID().toString().substring(0, 8);
            
            // Set business context
            tracingUtil.setAttribute("business.scenario", scenario);
            tracingUtil.setAttribute("donor.type", donorType);
            tracingUtil.setAttribute("donation.amount", amount.toString());
            tracingUtil.setAttribute("session.id", sessionId);
            tracingUtil.addEvent("business.scenario.started");
            
            try {
                Map<String, Object> response = new HashMap<>();
                
                switch (scenario.toLowerCase()) {
                    case "donation":
                        tracingUtil.addEvent("donation.flow.started");
                        String donationResult = observabilityService.processDonationReceipt("DONOR_" + sessionId, amount);
                        response.put("result", donationResult);
                        tracingUtil.addEvent("donation.flow.completed");
                        break;
                        
                    case "payment":
                        tracingUtil.addEvent("payment.flow.started");
                        tracingUtil.setAttribute("payment.method", "credit_card");
                        String paymentResult = observabilityService.simulateHttpClientOperation("https://api.payment.com/process", "POST");
                        response.put("result", paymentResult);
                        tracingUtil.addEvent("payment.flow.completed");
                        break;
                        
                    case "email":
                        tracingUtil.addEvent("notification.flow.started");
                        tracingUtil.setAttribute("notification.type", "email");
                        String emailResult = observabilityService.simulateHttpClientOperation("https://api.email.com/send-receipt", "POST");
                        response.put("result", emailResult);
                        tracingUtil.addEvent("notification.flow.completed");
                        break;
                        
                    default:
                        tracingUtil.addEvent("default.flow.started");
                        response.put("result", "Default business scenario executed");
                        tracingUtil.addEvent("default.flow.completed");
                }
                
                response.put("success", true);
                response.put("scenario", scenario);
                response.put("sessionId", sessionId);
                response.put("timestamp", System.currentTimeMillis());
                
                tracingUtil.addEvent("business.scenario.completed");
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                tracingUtil.addEvent("business.scenario.error", "error", e.getMessage());
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("scenario", scenario);
                response.put("sessionId", sessionId);
                response.put("error", e.getMessage());
                
                return ResponseEntity.internalServerError().body(response);
            }
        });
    }

    /**
     * Health check endpoint for observability
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> observabilityHealth() {
        return tracingUtil.traceMethod("observability_health_check", () -> {
            tracingUtil.setAttribute("health.check.type", "observability");
            tracingUtil.addEvent("health.check.started");
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "UP");
            response.put("observability", "enabled");
            response.put("tracing", "enabled");
            response.put("metrics", "enabled");
            response.put("timestamp", System.currentTimeMillis());
            response.put("service", "donation-receipt-service");
            response.put("version", "1.0.0");
            
            // Check OpenTelemetry status
            try {
                Class.forName("io.opentelemetry.api.OpenTelemetry");
                response.put("opentelemetry.api", "available");
            } catch (ClassNotFoundException e) {
                response.put("opentelemetry.api", "not_available");
            }
            
            tracingUtil.addEvent("health.check.completed");
            return ResponseEntity.ok(response);
        });
    }
} 