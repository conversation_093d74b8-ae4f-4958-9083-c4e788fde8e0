package com.chidhagni.donationreceipt.roles.dto.response;

import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
@Builder
public class RolesPermissionsResponse {
    private UUID id;
    private String name;
    private String description;
    private UUID parentRoleId;
    private Boolean isActive;
    private List<RoleNode> permissions;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
    private UUID orgId;
    private Set<UUID> mismatchIndividualIds;
}
