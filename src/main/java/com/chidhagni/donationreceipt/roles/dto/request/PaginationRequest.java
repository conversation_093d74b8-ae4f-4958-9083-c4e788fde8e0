package com.chidhagni.donationreceipt.roles.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
public class PaginationRequest {


    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer pageSize = 10;

    @Builder.Default
    private String nameFilter = "";

    @Builder.Default
    private String descriptionFilter = "";

    @Builder.Default
    private UUID parentRoleFilter = null;

    private boolean sortByNameAsc;


    private boolean sortByNameDesc;


    private boolean sortByCreatedDateAsc;


    private UUID orgId;

}

