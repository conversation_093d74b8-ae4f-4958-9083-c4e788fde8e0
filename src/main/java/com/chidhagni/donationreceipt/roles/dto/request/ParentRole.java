package com.chidhagni.donationreceipt.roles.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.UUID;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParentRole {

    @NotBlank(message = "Name Is mandatory")
    @Size(min = 3, max = 100, message = "Name must be between 3 and 100 characters")
    private String name;
    @Size(min = 3, max = 100, message = "Description must be between 3 and 100 characters")
    private String description;
    private UUID orgId;
    private UUID parentRoleId;
    private List<RoleNode> permissions;

    private Boolean isActive;
}
