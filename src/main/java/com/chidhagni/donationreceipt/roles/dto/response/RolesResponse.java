package com.chidhagni.donationreceipt.roles.dto.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RolesResponse {
    private UUID id;
    private String name;
    private String description;
    private UUID parentRoleId;
    private Boolean isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String createdBy;
    private String updatedBy;
    private String parentRoleName;

    private UUID organisationId;
    private String organisationName;
}
