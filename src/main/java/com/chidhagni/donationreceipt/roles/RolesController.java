package com.chidhagni.donationreceipt.roles;

import com.chidhagni.donationreceipt.roles.dto.request.GetOrgRoles;
import com.chidhagni.donationreceipt.roles.dto.request.GetTenantRolePermissionsDTO;
import com.chidhagni.donationreceipt.roles.dto.request.PaginationRequest;
import com.chidhagni.donationreceipt.roles.dto.request.ParentRole;
import com.chidhagni.donationreceipt.roles.dto.response.GetAllRolesResponse;
import com.chidhagni.donationreceipt.roles.dto.response.RolesPermissionsResponse;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.CustomMethodSecurity;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;


@RestController
@RequestMapping("/admin/api/v1/roles")
@RequiredArgsConstructor
public class RolesController {
    private final RolesService rolesService;
    private final CustomMethodSecurity customMethodSecurity;


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public UUID createRole(@CurrentUser UserPrincipal userPrincipal, @Valid @RequestBody ParentRole rolesRequestDTO) {
        return rolesService.createRole(userPrincipal, rolesRequestDTO);
    }

    @PostMapping(path = {"/all"})
    @ResponseStatus(HttpStatus.OK)
    public GetAllRolesResponse getAllRoles(@RequestBody(required = false) PaginationRequest paginationRequest,
                                           @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().build();
        }
        return rolesService.getAllRoles(paginationRequest, userPrincipal);
    }

    @PatchMapping(path = "/{roleId}")
    public ResponseEntity<RolesPermissionsResponse> updateRole(
            @PathVariable("roleId") UUID roleId,
            @RequestBody ParentRole rolesDTO,
            @CurrentUser UserPrincipal userPrincipal) {

        RolesPermissionsResponse updatedRole = rolesService.updateRole(roleId, rolesDTO, userPrincipal);
        return ResponseEntity.ok(updatedRole);
    }

    @PatchMapping(path = {"/deactivate/{roleId}"})
    @ResponseStatus(HttpStatus.OK)
    public void inactivateRole(@PathVariable("roleId") UUID roleId) {
        rolesService.inactivateRole(roleId);
    }


    @PatchMapping(path = {"/activate/{roleId}"})
    @ResponseStatus(HttpStatus.OK)
    public void activateRole(@PathVariable("roleId") UUID roleId) {
        rolesService.activateRole(roleId);
    }


    @GetMapping(value = "/{roleId}")
    @ResponseStatus(HttpStatus.OK)
    public RolesPermissionsResponse getRoleById(@PathVariable UUID roleId) {
        return rolesService.getRoleById(roleId);
    }


    @GetMapping(value = "/get-roles-by-orgId/{orgId}")
    @ResponseStatus(HttpStatus.OK)
    public List<GetOrgRoles> getRolesByOrgId(@PathVariable UUID orgId) {
        return rolesService.getRolesByOrgId(orgId);
    }



    @GetMapping("/tenant-role-permissions")
    public ResponseEntity<GetTenantRolePermissionsDTO> getTenantRolePermissions() {
        GetTenantRolePermissionsDTO permissions = rolesService.getTenantRolePermissionsDTO();
        return ResponseEntity.ok(permissions);
    }

    @GetMapping("/roles-for-superadmin")
    public List<GetOrgRoles> getAllRoles(@CurrentUser UserPrincipal userPrincipal)
    {
        // Log the user's authorities to see what roles they have
        System.out.println("User authorities: " + userPrincipal.getAuthorities());
        System.out.println("User ID: " + userPrincipal.getId());
        System.out.println("User email: " + userPrincipal.getEmail());
        System.out.println("Endpoint accessed successfully!");
        return rolesService.getAllRolesForAdmin();
    }
}
