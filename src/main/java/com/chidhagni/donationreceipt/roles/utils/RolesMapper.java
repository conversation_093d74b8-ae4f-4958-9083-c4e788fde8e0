package com.chidhagni.donationreceipt.roles.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles;
import com.chidhagni.donationreceipt.roles.dto.request.ParentRole;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import com.chidhagni.donationreceipt.roles.dto.response.RolesPermissionsResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class RolesMapper {

    private RolesMapper() {
    }

    public static Roles mapToEntity(UserPrincipal userPrincipal, ParentRole rolesRequestDTO) {

        return new Roles()
                .setId(UUID.randomUUID())
                .setName(rolesRequestDTO.getName())
                .setDescription(rolesRequestDTO.getDescription())
                .setParentRoleId(rolesRequestDTO.getParentRoleId())
                .setPermissions(rolesRequestDTO.getPermissions())
                .setOrgId(rolesRequestDTO.getOrgId())
                .setIsActive(true)
                .setCreatedBy(userPrincipal.getId())
                .setUpdatedBy(userPrincipal.getId())
                .setCreatedOn(DateUtils.currentDatetime())
                .setUpdatedOn(DateUtils.currentDatetime());
    }

    public static RolesPermissionsResponse mapToPermissionDTO(Roles mappedRoleEntity, Set<UUID> mismatchIndividualIds) {

        return RolesPermissionsResponse.builder()
                .id(mappedRoleEntity.getId())
                .name(mappedRoleEntity.getName())
                .description(mappedRoleEntity.getDescription())
                .parentRoleId(mappedRoleEntity.getParentRoleId())
                .permissions(mappedRoleEntity.getPermissions())
                .orgId(mappedRoleEntity.getOrgId())
                .mismatchIndividualIds(mismatchIndividualIds)
                .createdBy(mappedRoleEntity.getCreatedBy())
                .createdOn(mappedRoleEntity.getCreatedOn())
                .updatedBy(mappedRoleEntity.getUpdatedBy())
                .updatedOn(mappedRoleEntity.getUpdatedOn())
                .build();
    }

    public static Roles map(Roles roleEntity, ParentRole rolesDTO, UUID loggedInUserId) {
        applyDefaultsToDTO(roleEntity, rolesDTO);
        roleEntity.setName(rolesDTO.getName());
        roleEntity.setDescription(rolesDTO.getDescription());
        roleEntity.setOrgId(rolesDTO.getOrgId());
        roleEntity.setPermissions(rolesDTO.getPermissions());
        roleEntity.setUpdatedBy(loggedInUserId);
        roleEntity.setUpdatedOn(DateUtils.currentTimeIST());
        return roleEntity;
    }

    public static void applyDefaultsToDTO(Roles roleEntity, ParentRole rolesDTO) {
        String name = rolesDTO.getName();
        String description = rolesDTO.getDescription();
        Boolean isActive = rolesDTO.getIsActive();
        if (name == null || name.trim().isEmpty()) {
            rolesDTO.setName(roleEntity.getName());
        }
        if (description == null || description.trim().isEmpty()) {
            rolesDTO.setDescription(roleEntity.getDescription());
        }
        if (isActive == null) {
            rolesDTO.setIsActive(true);
        }

    }

    public static RolesPermissionsResponse mapToDTO(Roles roleEntity) {

        return RolesPermissionsResponse.builder()
                .id(roleEntity.getId())
                .name(roleEntity.getName())
                .parentRoleId(roleEntity.getParentRoleId())
                .description(roleEntity.getDescription())
                .permissions(roleEntity.getPermissions())
                .orgId(roleEntity.getOrgId())

                .build();
    }

    public static List<GetRolesListResponse.RolesGetResponse> mapToRolesGetResponse(List<Roles> roles) {
        return roles.stream()
                .map(role -> GetRolesListResponse.RolesGetResponse.builder()
                        .id(role.getId())
                        .name(role.getName())
                        .description(role.getDescription())
                        .build())
                .collect(Collectors.toList());
    }

    public static void updateIndividualPermissionWithNewPermissions(
            Roles existingRolesPermission,
            IndividualPermission individualPermission,
            List<RoleNode> newPermissions,
            UUID loggedInUserId, Set<UUID> mismatchIndividualIds,
            UUID individualId) {

        List<RoleNode> individualPermissions = individualPermission.getPermissions();
        List<RoleNode> rolePermissions = existingRolesPermission.getPermissions();
        List<RoleNode> updatedPermissions = updatePermissionsIfEqual(rolePermissions, individualPermissions, newPermissions, mismatchIndividualIds, individualId);
        log.info("Setting the permissions for individuals");
        individualPermission.setPermissions(updatedPermissions);
        individualPermission.setUpdatedOn(DateUtils.currentTimeIST());
        individualPermission.setUpdatedBy(loggedInUserId);

    }

    private static List<RoleNode> updatePermissionsIfEqual(
            List<RoleNode> existingRolesPermissions,
            List<RoleNode> existingPermissions,
            List<RoleNode> newPermissions, Set<UUID> mismatchIndividualIds,
            UUID individualId) {

        Map<String, RoleNode> existingRolesPermissionsMap = existingRolesPermissions.stream()
                .collect(Collectors.toMap(RoleNode::getName, node -> node));
        Map<String, RoleNode> newPermissionsMap = newPermissions.stream()
                .collect(Collectors.toMap(RoleNode::getName, node -> node));

        List<RoleNode> updatedPermissions = new ArrayList<>();

        for (RoleNode existingNode : existingPermissions) {
            RoleNode roleNodeInExistingRoles = existingRolesPermissionsMap.get(existingNode.getName());
            RoleNode roleNodeInNewPermissions = newPermissionsMap.get(existingNode.getName());
            RoleNode updatedNode = new RoleNode(existingNode.getName());
            updatedNode.setDisplayNumber(existingNode.getDisplayNumber());
            updatedNode.setPermissions(existingNode.getPermissions());
            updatedNode.setType(existingNode.getType());

            if (roleNodeInExistingRoles != null && roleNodeInNewPermissions != null) {
                if (Objects.equals(existingNode.getPermissions(), roleNodeInExistingRoles.getPermissions())) {
                    log.info("Permissions updated for node '{}' to new permissions '{}'",
                            existingNode.getName(),
                            roleNodeInNewPermissions.getPermissions(),
                            existingNode.getPermissions()
                    );

                    updatedNode.setPermissions(roleNodeInNewPermissions.getPermissions());
                } else {
                    mismatchIndividualIds.add(individualId);
                    log.info("Permissions do not match for node '{}'. Skipping update for this node's permissions.", updatedNode.getName());
                }

                List<RoleNode> updatedChildren = updatePermissionsIfEqual(
                        roleNodeInExistingRoles.getChildren(),
                        existingNode.getChildren(),
                        roleNodeInNewPermissions.getChildren(), mismatchIndividualIds,
                        individualId
                );
                updatedNode.setChildren(updatedChildren);
            } else {
                log.info("Node '{}' is missing in either roles or new permissions - skipping update for this node.", updatedNode.getName());
                updatedNode.setChildren(existingNode.getChildren());
            }
            updatedPermissions.add(updatedNode);
        }

        return updatedPermissions;
    }

}

