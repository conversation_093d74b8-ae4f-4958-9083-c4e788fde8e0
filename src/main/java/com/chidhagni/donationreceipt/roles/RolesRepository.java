package com.chidhagni.donationreceipt.roles;

import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.RolesDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles;
import com.chidhagni.donationreceipt.roles.dto.request.*;
import com.chidhagni.donationreceipt.roles.dto.response.RolesResponse;
import com.chidhagni.donationreceipt.roles.utils.RolesMapper;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static com.chidhagni.donationreceipt.db.jooq.tables.Roles.ROLES;


@Repository
@RequiredArgsConstructor
@Slf4j
public class RolesRepository {

    private final RolesDao rolesDao;
    private final IndividualRoleDao individualRoleDao;
    private final IndividualPermissionDao individualPermissionDao;

    private static final String PARENT_ROLE_ID = "parent_role_id";
    private static final String ROLE_ID ="parentRoleId";
    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;
    public Roles getRoleById(UUID id) {
        Roles roles = null;
        try {
            roles = rolesDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching role by it's id", ex);
        }
        return roles;
    }

    public List<Roles> getRoleByRoleName(String name) {

        try {
            return rolesDao.fetchByName(name);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching role by name", ex);
        }
    }

    public void createRole(Roles role) {

        try {
            rolesDao.insert(role);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while creating role", ex);
        }
    }

public List<RolesResponse> getAllRoles(Condition condition, List<SortField<?>> sortFields, PaginationRequest paginationRequest,
                                       UserPrincipal userPrincipal) {
    Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
    try {
        UUID loggedUser = userPrincipal.getId();
        List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

        Condition finalCondition = condition != null ? condition : DSL.noCondition();

        boolean isTenantAdmin = individualRoles.stream()
                .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

        if (isTenantAdmin && !individualRoles.isEmpty()) {
            UUID orgId = individualRoles.get(0).getOrgId();
            if (orgId != null) {
                finalCondition = finalCondition.and(ROLES.ORG_ID.eq(orgId));
            }
        }

        return rolesDao.ctx().select(
                        ROLES.ID.as("id"),
                        ROLES.NAME.as("name"),
                        ROLES.DESCRIPTION.as("description"),
                        ROLES.PARENT_ROLE_ID.as("parentRoleId"),
                        ROLES.IS_ACTIVE.as("isActive"),
                        ROLES.CREATED_ON.as("createdOn"),
                        ROLES.UPDATED_ON.as("updatedOn"),
                        INDIVIDUAL.as("r1").EMAIL.as("createdBy"),
                        INDIVIDUAL.as("r2").EMAIL.as("createdBy"),
                        ROLES.as("r0").NAME.as("parentRoleName"),
                        ORGANISATION.ID.as("organisationId"),
                        ORGANISATION.NAME.as("organisationName")
                )
                .from(ROLES)
                .leftJoin(ROLES.as("r0")).on(ROLES.PARENT_ROLE_ID.eq(ROLES.as("r0").ID))
                .leftJoin(INDIVIDUAL.as("r1")).on(ROLES.CREATED_BY.eq(INDIVIDUAL.as("r1").ID))
                .leftJoin(INDIVIDUAL.as("r2")).on(ROLES.UPDATED_BY.eq(INDIVIDUAL.as("r2").ID))
                .leftJoin(ORGANISATION).on(ROLES.ORG_ID.eq(ORGANISATION.ID))
                .where(finalCondition)
                .orderBy(sortFields)
                .limit(paginationRequest.getPageSize())
                .offset(pageNo)
                .fetchInto(RolesResponse.class);
    } catch (Exception ex) {
        throw new InternalServerError("Exception Occurred while fetching all roles", ex);
    }
}


    public Integer getRolesCount(Condition condition,UserPrincipal userPrincipal) {
        try {
            UUID loggedUser = userPrincipal.getId();
            List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

            Condition finalCondition = condition != null ? condition : DSL.noCondition();

            boolean isTenantAdmin = individualRoles.stream()
                    .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

            if (isTenantAdmin) {
                UUID orgId = individualRoles.get(0).getOrgId();
                finalCondition = finalCondition.and(ROLES.ORG_ID.eq(orgId));
            }
            return rolesDao.ctx().selectCount().from(ROLES).where(finalCondition).fetchOne(0, Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching all roles count", ex);
        }
    }

    public void updateRole(Roles role) {
        try {
            rolesDao.update(role);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while updating role", ex);
        }
    }

    List<Roles> fetchRolesWithNullParentId() {

        try {
            return rolesDao.ctx().selectFrom(ROLES)
                    .where(ROLES.PARENT_ROLE_ID.isNull())
                    .fetchInto(Roles.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching Roles with Null ParentRoleId ", ex);

        }
    }

    public void updateIndividualPermissions(UUID roleId, ParentRole rolesDTO, UUID loggedInUserId, Set<UUID> mismatchIndividualIds) {

        List<IndividualRole> individualRoleList = individualRoleDao.fetchByRoleId(roleId);
        for (IndividualRole individualRole : individualRoleList) {
            UUID individualId = individualRole.getIndividualId();
            List<Roles> existingRolesPermissions = rolesDao.fetchById(roleId);
            List<IndividualPermission> individualPermissions = individualPermissionDao.fetchByIndividualId(individualId);
            if (CollectionUtils.isEmpty(individualPermissions)) {
                throw new IllegalArgumentException("No permissions were found for individual => " + individualId);
            }
            IndividualPermission individualPermission = individualPermissions.get(0);
            Roles roles = existingRolesPermissions.get(0);
            RolesMapper.updateIndividualPermissionWithNewPermissions(roles,
                    individualPermission, rolesDTO.getPermissions(), loggedInUserId, mismatchIndividualIds, individualId);
            individualPermissionDao.update(individualPermission);
            log.info("Permissions in IndividualPermissions for the role id :: {} is updated successfully ", roleId);
        }

    }

    public List<GetOrgRoles> getRolesByOrgId(UUID orgId) {
        return rolesDao.ctx().select(ROLES.ID.as("id"),ROLES.NAME.as("roleName"),ROLES.DESCRIPTION.as("roleDescription"))
                .from(ROLES)
                .where(ROLES.ORG_ID.eq(orgId).and(ROLES.IS_ACTIVE.eq(Boolean.TRUE)))
                .fetchInto(GetOrgRoles.class);

    }

    public GetTenantRolePermissionsDTO getRolePermissionsById(UUID tenantRoleId) {

        return rolesDao.ctx().select(ROLES.ID.as("tenantRoleId"),ROLES.PERMISSIONS.as("permissions"))
                .from(ROLES)
                .where(ROLES.ID.eq(tenantRoleId))
                .fetchOneInto(GetTenantRolePermissionsDTO.class);
    }

    public boolean roleWithNameAndOrgIdExists(String name, UUID orgId) {
        return rolesDao.ctx().selectCount()
                .from(ROLES)
                .where(ROLES.NAME.eq(name).and(ROLES.ORG_ID.eq(orgId)))
                .fetchOneInto(Integer.class) > 0;
    }


    public boolean existsByNameAndOrgIdExcludingId(String name, UUID orgId, UUID excludedRoleId) {
        return rolesDao.ctx().selectCount()
                .from(ROLES)
                .where(ROLES.NAME.eq(name)
                        .and(ROLES.ORG_ID.eq(orgId))
                        .and(ROLES.ID.ne(excludedRoleId)))
                .fetchOneInto(Integer.class) > 0;
    }

    public List<GetOrgRoles> getAllRolesForAdmin() {
        return rolesDao.ctx().select(ROLES.ID.as("id"),ROLES.NAME.as("roleName"),ROLES.DESCRIPTION
                .as("roleDescription"))
                .from(ROLES)
                .fetchInto(GetOrgRoles.class);


    }

}

