package com.chidhagni.donationreceipt.wati;

import com.chidhagni.utils.CommonOperations;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;

@Service
public class WatiService {
    private static final Logger logger = LoggerFactory.getLogger(WatiService.class);

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CommonOperations commonOperations;

    @Value("${token}")
    private String token;
    @Value("${templateMessageUrl}")
    private String templateMessageUrl;
    @Value("${sessionFileUrl}")
    private String sessionFileUrl;
    @Value("${templateMessagesUrl}")
    private String templateMessagesUrl;
    @Value("${sessionMessagesUrl}")
    private String sessionMessagesUrl;

        public HttpResponse<String> sendTemplateMessage(String whatsappNumber, WatiDTO watiDTO)
                throws IOException, InterruptedException {

            String jsonString = objectMapper.writeValueAsString(watiDTO);
            String url = templateMessageUrl + whatsappNumber;

            return commonOperations.sendTemplateMessage(url, jsonString);
        }

    public HttpResponse<String> sendTemplateMessages(TemplateMessagesDTO templateMessagesDTO)
            throws IOException, InterruptedException {

        String jsonString = objectMapper.writeValueAsString(templateMessagesDTO);

        return commonOperations.sendTemplateMessage(templateMessagesUrl, jsonString);
    }

    public HttpResponse<String> sendSessionMessages(String whatsappNumber, String message)
            throws IOException, InterruptedException {

        String encodedMessage = URLEncoder.encode(message, StandardCharsets.UTF_8.toString());
        String url = sessionMessagesUrl + whatsappNumber + "?messageText=" + encodedMessage;

        return commonOperations.sendTemplateMessage(url, "");  // Sending an empty body
    }

    public Response sendSessionFile(String whatsappNumber, MultipartFile file) throws IOException {
        String url = sessionFileUrl + whatsappNumber;

        return commonOperations.sendMultipartData(url, whatsappNumber, file);
    }

    public Response sendTemplateMessageWithDocument(String whatsappNumber, WatiDTO watiDTO, MultipartFile document)
            throws IOException {
        String url = templateMessageUrl + whatsappNumber;
        String jsonString = objectMapper.writeValueAsString(watiDTO);
        return commonOperations.sendTemplateMessageWithDocument(url, jsonString, document);
    }
}
