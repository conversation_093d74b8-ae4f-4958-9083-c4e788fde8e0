package com.chidhagni.donationreceipt.wati;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateMessagesDTO {
    private String template_name;
    private String broadcast_name;
    private List<Receivers> receivers;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Receivers {
        private String whatsappNumber;
        private List<CustomParams> customParams;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomParams {
        private String name;
        private String value;
    }
}
