package com.chidhagni.donationreceipt.mobileotp;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/mobile-otp")
@RequiredArgsConstructor
public class MobileOtpController {

    private final MobileOtpService mobileOtpService;


    @PostMapping("/send")
    public String sendOtp(@RequestParam String mobile,@RequestBody MobileOtpRequestDto mobileOtpRequestDto) {
        if (!mobile.matches("^\\d{10}$")) {
            throw new IllegalArgumentException("Mobile number must be exactly 10 digits.");
        }
        return mobileOtpService.sendOtp(mobile,mobileOtpRequestDto);
    }


    @GetMapping("/verify")
    public String verifyOtp(@RequestParam String mobile, @RequestParam String otp) {
        return mobileOtpService.verifyOtp(mobile, otp);
    }

    @GetMapping("/resend")
    public String resendOtp(@RequestParam String mobile) {
        return mobileOtpService.resendOtp(mobile);
    }
}
