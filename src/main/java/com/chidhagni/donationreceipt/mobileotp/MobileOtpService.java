package com.chidhagni.donationreceipt.mobileotp;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.IndividualVerificationAuditRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.ContactType;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.individualverificationaudit.utils.IndividualVerificationMapper;
import com.chidhagni.utils.DateUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Service
@RequiredArgsConstructor
@Slf4j
public class MobileOtpService {

    @Value("${msg91.auth-key}")
    private String authKey;

    @Value("${msg91.template-id}")
    private String templateId;

    @Value("${msg91.sender-id:}")
    private String senderId;

    @Value("${send.otp.url}")
    private String sendOtp;

    @Value("${verify.otp.url}")
    private String verifyOtp;

    @Value("${resend.otp.url}")
    private String resendOtp;

    private final ObjectMapper objectMapper;
    private final IndividualRepository individualRepository;


    private final IndividualVerificationMapper individualVerificationMapper;
    private final IndividualVerificationAuditRepository individualVerificationAuditRepository;
    private final RestTemplate restTemplate = new RestTemplate();

    public String sendOtp(String mobile,MobileOtpRequestDto mobileOtpRequestDto) {
        IndividualVerificationAudit existingAudit = individualVerificationAuditRepository
                .findByContactValueAndContactType(mobile, ContactType.MOBILE.name());

        if (existingAudit != null && VerificationStatusEnum.VERIFIED.name().equals(existingAudit.getVerificationStatus())) {
            // Mobile number is already verified
            // Check if an account exists for this mobile number
            boolean accountExists = individualRepository.existsByMobile(mobile);

            if (accountExists) {
                // Case 1: Mobile number verified, account created
                log.info("Mobile number {} is already verified and associated with an account.", mobile);
                throw new IllegalArgumentException("An account already exists for this mobile number.Please Login");
            } else {
                // Case 2: Mobile number verified, account not created
                log.info("Mobile number {} is verified but no account exists.", mobile);
                return "Mobile number verified, continue to signup.";
            }
        } else if (existingAudit == null) {
            // No existing verification record, create a new one
            log.info("No verification record found for mobile: {}. Creating new record.", mobile);
            String newOtp = String.valueOf(new Random().nextInt(900000) + 100000);
            IndividualVerificationAudit individualVerificationAudit = individualVerificationMapper
                    .createMobileNumberVerification(mobile, mobileOtpRequestDto, newOtp);
            individualVerificationAuditRepository.insertIndividualVerificationAudit(individualVerificationAudit);
            log.info("Inserted new verification record for mobile: {}", mobile);
        } else {
            // Existing record exists but not verified, update it
            log.info("Verification record exists for mobile: {} but not verified. Updating with new OTP.", mobile);
            String newOtp = String.valueOf(new Random().nextInt(900000) + 100000);
            existingAudit.setOtpCode(newOtp);
            existingAudit.setOtpCreatedAt(DateUtils.currentTimeIST());
            existingAudit.setOtpExpiresAt(DateUtils.currentTimeIST().plusMinutes(10));
            existingAudit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
            existingAudit.setOtpVerifiedAt(null);
            individualVerificationAuditRepository.updateIndividualVerificationAudit(existingAudit);
            log.info("Updated existing verification record for mobile: {}", mobile);
        }
        HttpHeaders headers = createAuthHeaders(authKey);
        String otp = String.valueOf(new Random().nextInt(900000) + 100000);
        Map<String, Object> body = new HashMap<>();
        body.put("mobile", "+91" + mobile);
        body.put("template_id", templateId);
        body.put("OTP", otp);
        if (!senderId.isEmpty()) {
            body.put("sender", senderId);
        }

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        log.info("Sending OTP to mobile: {}", mobile);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(sendOtp, request, String.class);
            log.info("OTP Send Response: Status={}, Body={}", response.getStatusCode(), response.getBody());
            return "OTP Sent: " + response.getBody();
        } catch (Exception e) {
            log.error("Error while sending OTP", e);
            return "Error sending OTP: " + e.getMessage();
        }
    }


    private HttpHeaders createAuthHeaders(String authKey) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("authkey", authKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }


    public String verifyOtp(String mobile, String otp) {
        String url = String.format(verifyOtp, "+91" + mobile, otp);
        HttpHeaders headers = createAuthHeaders(authKey);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        log.info("Verifying OTP for mobile: {}", mobile);
        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            log.info("OTP Verification Response: Status={}, Body={}", response.getStatusCode(), response.getBody());
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, String> responseBody = objectMapper.readValue(response.getBody(), Map.class);
                String responseType = responseBody.get("type");

                if ("success".equals(responseType)) {
                    IndividualVerificationAudit audit = individualVerificationAuditRepository
                            .findByContactValueAndContactType(mobile, ContactType.MOBILE.name());

                    if (audit != null) {
                        audit.setOtpVerifiedAt(DateUtils.currentTimeIST());
                        audit.setVerificationStatus(VerificationStatusEnum.VERIFIED.name());
                        audit.setUpdatedOn(DateUtils.currentTimeIST());
                        individualVerificationAuditRepository.updateIndividualVerificationAudit(audit);
                        log.info("Updated individual_verification_audit table: otp_verified_at set for mobile {}", mobile);
                    } else {
                        log.warn("No verification record found for mobile: {}", mobile);
                    }
                } else {
                    log.warn("OTP verification failed for mobile: {}. Message: {}", mobile, responseBody.get("message"));
                }
            } else {
                log.warn("Invalid response from MSG91 API for mobile: {}", mobile);
            }

            return "Verification Result: " + response.getBody();
        } catch (Exception e) {
            log.error("Error while verifying OTP", e);
            return "Error verifying OTP: " + e.getMessage();
        }
    }

    public String resendOtp(String mobile) {
        String retryType = "text";
        String url = String.format(resendOtp, "+91" + mobile, retryType);
        HttpHeaders headers = createAuthHeaders(authKey);
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        log.info("Resending OTP to mobile: {}", mobile);

        IndividualVerificationAudit audit = individualVerificationAuditRepository
                .findByContactValueAndContactType(mobile, ContactType.MOBILE.name());

        //TODO EXPLORE OTHER APPLICATION AND HANDLE CASE ACCORDINGLY
        if (audit == null) {
            log.warn("No verification record found for mobile: {}", mobile);
            return "Error: No verification record found for mobile: " + mobile;

        }
        audit.setOtpCreatedAt(DateUtils.currentTimeIST());
        audit.setOtpExpiresAt(DateUtils.currentTimeIST().plusMinutes(10));
        audit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
        audit.setOtpVerifiedAt(null);
        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            log.info("OTP Resend Response: Status={}, Body={}", response.getStatusCode(), response.getBody());
            return "OTP resent via text: " + response.getBody();
        } catch (Exception e) {
            log.error("Error while resending OTP", e);
            return "Error resending OTP: " + e.getMessage();
        }
    }

    /**
     * Send SMS without OTP verification logic - simplified version for general SMS sending
     * @param mobile Mobile number (10 digits)
     * @param templatId Template ID for SMS
     * @return Response message
     */
    public String sendSms(String mobile, String templatId) {
        HttpHeaders headers = createAuthHeaders(authKey);
        Map<String, Object> body = new HashMap<>();
        body.put("mobile", "+91" + mobile);
        body.put("template_id", templateId);
        if (!senderId.isEmpty()) {
            body.put("sender", senderId);
        }

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        log.info("Sending SMS to mobile: {} with template: {}", mobile, templateId);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(sendOtp, request, String.class);
            log.info("SMS Send Response: Status={}, Body={}", response.getStatusCode(), response.getBody());
            return "SMS Sent: " + response.getBody();
        } catch (Exception e) {
            log.error("Error while sending SMS", e);
            return "Error sending SMS: " + e.getMessage();
        }
    }
}
