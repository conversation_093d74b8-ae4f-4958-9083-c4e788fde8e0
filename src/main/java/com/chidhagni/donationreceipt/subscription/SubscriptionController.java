package com.chidhagni.donationreceipt.subscription;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/subscription")
public class SubscriptionController {
    @Autowired
    private SubscriptionService service;
//    @PostMapping
//    public ResponseEntity<SubscriptionResponseDTO> createSubscription(@RequestBody SubscriptionPayLoadDTO subscriptionPayLoadDTO, @CurrentUser UserPrincipal userPrincipal){
//
//        return new ResponseEntity<>(service.createSubscription(subscriptionPayLoadDTO,userPrincipal), HttpStatus.CREATED);
//    }
//    @GetMapping("/{id}")
//    public ResponseEntity<SubscriptionDTO> getSubscriptionDetails(@PathVariable("id") UUID subscriptionID){
//        return new ResponseEntity<>(service.getSubscriptionDetails(subscriptionID), HttpStatus.OK);
//    }
//
//    @GetMapping
//    public ResponseEntity<UUID> getPackageId(@CurrentUser UserPrincipal userPrincipal){
//        UUID userId=userPrincipal.getId();
//        return new ResponseEntity<>(service.getSubscriptionDetailsByUserId(userId), HttpStatus.OK);
//    }
//
//    @PatchMapping("/{id}/{error}")
//    @ResponseStatus(HttpStatus.OK)
//    public void updateSubscriptionError(@PathVariable("id") UUID subscriptionId, @PathVariable("error") String error){
//        service.updateSubscriptionFailure(subscriptionId, error);
//    }
}
