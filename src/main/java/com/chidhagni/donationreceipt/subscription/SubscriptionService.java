package com.chidhagni.donationreceipt.subscription;

import org.springframework.stereotype.Service;

@Service
public class SubscriptionService {
//    private static final Logger log = LoggerFactory.getLogger(SubscriptionService.class);
//    @Autowired
//    private RazorpayClient razorpayClient;
//    @Autowired
//    private RazorPayConfig razorpayConfig;
//    @Autowired
//    private SubscriptionMapper mapper;
//    @Autowired
//    private SubscriptionsDao subscriptionsDao;
//    @Autowired
//    private DSLContext dslContext;
//
//    public SubscriptionResponseDTO createSubscription(SubscriptionPayLoadDTO subscriptionPayLoadDTO, UserPrincipal userPrincipal) {
//        UUID userId=userPrincipal.getId();
//        Order order=getOrder(subscriptionPayLoadDTO);
//        UUID upgradedFrom=getUpgradedFrom(subscriptionPayLoadDTO, userId);
//        Subscriptions subscriptions=mapper.payloadDTOToSubscription(subscriptionPayLoadDTO,order,upgradedFrom, userId);
//        subscriptionsDao.insert(subscriptions);
//        return mapper.subscriptionToResponseDTO(subscriptions);
//    }
//
//    public SubscriptionDTO updateStatusAndGateway(UUID subscriptionId, String failureDescription, Boolean isPaid, String paymentGateWay){
//        String status=setStatus(isPaid);
//        Subscriptions subscriptions=subscriptionsDao.fetchOneById(subscriptionId);
//        subscriptions.setStatus(status);
//        subscriptions.setIsActive(isPaid);
//        subscriptions.setPaymentGateway(paymentGateWay);
//        subscriptions.setFailureDescription(failureDescription);
//        subscriptionsDao.update(subscriptions);
//        return mapper.subscriptionToDTO(subscriptions);
//    }
//
//    public void updateSubscriptionFailure(UUID subscriptionId, String error){
//        Subscriptions subscriptions=subscriptionsDao.fetchOneById(subscriptionId);
//        if(subscriptions==null){
//            throw new IllegalArgumentException("Subscription is not available with given Id::"+subscriptionId);
//        }
//        subscriptions.setFailureDescription(error);
//        subscriptionsDao.update(subscriptions);
//    }
//
//    public SubscriptionDTO getSubscriptionDetails(UUID subscriptionId){
//        Optional<Subscriptions> optionalSubscription = Optional.ofNullable(subscriptionsDao.fetchOneById(subscriptionId));
//        Subscriptions subscriptions = optionalSubscription.orElseThrow(() -> {
//                    log.warn("Subscription with the given id is not found::{}", subscriptionId);
//                    return new IllegalArgumentException("Subscription with the given id is not found");
//                }
//        );
//        return mapper.subscriptionToDTO(subscriptions);
//    }
//
//    public UUID getSubscriptionDetailsByUserId(UUID userId){
//        return dslContext
//                .select(SUBSCRIPTIONS.PACKAGE_ID)
//                .from(SUBSCRIPTIONS)
//                .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(SUBSCRIPTIONS.SP_ORGANISATION_ID))
//                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userId))
//                .and(SUBSCRIPTIONS.IS_ACTIVE.eq(true))
//                .and(SUBSCRIPTIONS.START_DATE.isNotNull())
//                .and(SUBSCRIPTIONS.END_DATE.isNotNull())
//                .orderBy(SUBSCRIPTIONS.CREATED_ON.desc())
//                .limit(1)
//                .fetchOneInto(UUID.class);
//    }
//
//    public void deleteSubscription(UUID subscriptionId){
//        Optional<Subscriptions> optionalSubscription = Optional.ofNullable(subscriptionsDao.fetchOneById(subscriptionId));
//        Subscriptions subscriptions = optionalSubscription.orElseThrow(() -> {
//                    log.warn("Subscription with the given id not found::{}", subscriptionId);
//                    return new IllegalArgumentException("Subscription with the given id is not found");
//                }
//        );
//        subscriptions.setIsActive(false);
//        subscriptionsDao.update(subscriptions);
//    }
//    public Order getOrder(SubscriptionPayLoadDTO subscriptionPayLoadDTO) {
//        JSONObject orderRequest = new JSONObject();
//
//        String indianCurrencyCode= String.valueOf(CurrencyCode.INR);
//
//        orderRequest.put("amount", subscriptionPayLoadDTO.getPrice());
//        orderRequest.put("currency", indianCurrencyCode);
//
//        Order order=null;
//        try {
//            order= razorpayClient.Orders.create(orderRequest);
//        } catch (RazorpayException e) {
//            log.warn("Razorpay Order id not generated::{}", e);
//            throw new RuntimeException("Razorpay Order id not generated " + e);
//        }
//        return order;
//    }
//
//    public UUID getUpgradedFrom(SubscriptionPayLoadDTO subscriptionPayLoadDTO, UUID userId){
//        List<Subscriptions> subscriptionsList
//                =dslContext
//                .select()
//                .from(SUBSCRIPTIONS)
//                .join(INDIVIDUAL_ROLE).on(SUBSCRIPTIONS.SP_ORGANISATION_ID.eq(INDIVIDUAL_ROLE.ORG_ID))
//                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userId))
//                .orderBy(SUBSCRIPTIONS.CREATED_ON.desc())
//                .fetchInto(Subscriptions.class);
//            if(!subscriptionsList.isEmpty()){
//                return subscriptionsList.get(0).getPackageId();
//            }
//            return null;
//    }
//
//    public String setStatus(Boolean isPaid){
//        return isPaid?SubscriptionEnums.PAID.name():SubscriptionEnums.PENDING.name();
//    }
}
