package com.chidhagni.donationreceipt.subscription;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubscriptionDTO {
    private UUID id;
    private UUID spOrganisationId;
    private UUID packageId;
    private BigDecimal price;
    private String status;
    private UUID upgradedFrom;
    private String paymentGateway;
    private String orderId;
    private LocalDate startDate;
    private LocalDate endDate;
    private boolean isActive;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
