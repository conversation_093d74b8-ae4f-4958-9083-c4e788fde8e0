package com.chidhagni.donationreceipt.subscription;


import com.chidhagni.donationreceipt.individual.IndividualRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SubscriptionMapper {
    private final IndividualRepository individualRepository;

//    public Subscriptions payloadDTOToSubscription(SubscriptionPayLoadDTO subscriptionPayLoadDTO, Order order, UUID upgradedFrom, UUID userId) {
//        Subscriptions subscriptions = new Subscriptions();
//        List<IndividualRole> individualRoleByIndividualId = individualRepository.getIndividualRoleByIndividualId(userId);
//        if (CollectionUtils.isEmpty(individualRoleByIndividualId)) {
//            throw new IllegalArgumentException(String.format("An individual role is not present with the given individual [id=%s]", userId));
//        }
//        UUID orgId = individualRoleByIndividualId.get(0).getOrgId();
//        if(orgId == null){
//            throw new IllegalArgumentException(String.format("Organisation id is null in the individual role with " +
//                            "primary id :: [id=%s]", individualRoleByIndividualId.get(0).getId()));
//        }
//        String orderId = order.get("id").toString();
//        BigDecimal amountInPaisa = subscriptionPayLoadDTO.getPrice();
//        BigDecimal amountInRupees = amountInPaisa.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
//
//        String status = SubscriptionEnums.PENDING.name();
//        LocalDate endDate = getDateAfter365Days();
//        UUID subscriptionId = UUID.randomUUID();
//        subscriptions.setId(subscriptionId);
//        subscriptions.setSpOrganisationId(orgId);
//        subscriptions.setUpgradedFrom(upgradedFrom);
//        subscriptions.setPackageId(subscriptionPayLoadDTO.getPackageId());
//        subscriptions.setPrice(amountInRupees);
//        subscriptions.setStatus(status);
//        subscriptions.setOrderId(orderId);
//        subscriptions.setStartDate(DateUtils.currentDate());
//        subscriptions.setEndDate(endDate);
//        subscriptions.setIsActive(false);
//        subscriptions.setCreatedBy(userId);
//        subscriptions.setUpdatedBy(userId);
//        subscriptions.setCreatedOn(DateUtils.currentTimeIST());
//        subscriptions.setUpdatedOn(DateUtils.currentTimeIST());
//        return subscriptions;
//    }
//
//    public SubscriptionDTO subscriptionToDTO(Subscriptions subscriptions) {
//
//        return SubscriptionDTO.builder()
//                .id(subscriptions.getId())
//                .spOrganisationId(subscriptions.getSpOrganisationId())
//                .packageId(subscriptions.getPackageId())
//                .price(subscriptions.getPrice())
//                .status(subscriptions.getStatus())
//                .upgradedFrom(subscriptions.getUpgradedFrom())
//                .paymentGateway(subscriptions.getPaymentGateway())
//                .orderId(subscriptions.getOrderId())
//                .startDate(subscriptions.getStartDate())
//                .endDate(subscriptions.getEndDate())
//                .isActive(subscriptions.getIsActive())
//                .updatedBy(subscriptions.getUpdatedBy())
//                .createdBy(subscriptions.getCreatedBy())
//                .updatedOn(subscriptions.getUpdatedOn())
//                .createdOn(subscriptions.getCreatedOn())
//                .build();
//    }
//
//    public Subscriptions subscriptionDTOTOSubscription(SubscriptionDTO subscriptionsDTO) {
//        Subscriptions subscriptions = new Subscriptions();
//        subscriptions.setId(subscriptionsDTO.getId());
//        subscriptions.setSpOrganisationId(subscriptionsDTO.getSpOrganisationId());
//        subscriptions.setPackageId(subscriptionsDTO.getPackageId());
//        subscriptions.setPrice(subscriptionsDTO.getPrice());
//        subscriptions.setStatus(subscriptionsDTO.getStatus());
//        subscriptions.setUpgradedFrom(subscriptionsDTO.getUpgradedFrom());
//        subscriptions.setPaymentGateway(subscriptionsDTO.getPaymentGateway());
//        subscriptions.setOrderId(subscriptionsDTO.getOrderId());
//        subscriptions.setStartDate(subscriptionsDTO.getStartDate());
//        subscriptions.setEndDate(subscriptionsDTO.getEndDate());
//        subscriptions.setIsActive(subscriptionsDTO.isActive());
//        subscriptions.setUpdatedBy(subscriptionsDTO.getUpdatedBy());
//        subscriptions.setCreatedBy(subscriptionsDTO.getCreatedBy());
//        subscriptions.setUpdatedOn(subscriptionsDTO.getUpdatedOn());
//        subscriptions.setCreatedOn(subscriptionsDTO.getCreatedOn());
//        return subscriptions;
//    }
//
//    public SubscriptionResponseDTO subscriptionToResponseDTO(Subscriptions subscriptions) {
//        return SubscriptionResponseDTO.builder()
//                .subscriptionId(subscriptions.getId())
//                .orderId(subscriptions.getOrderId())
//                .price(subscriptions.getPrice())
//                .build();
//    }
//
//    public LocalDate getDateAfter365Days() {
//        LocalDate currentDate = LocalDate.now();
//        LocalDate dateAfter365Days = currentDate.plusDays(365);
//        return dateAfter365Days;
//    }
}
