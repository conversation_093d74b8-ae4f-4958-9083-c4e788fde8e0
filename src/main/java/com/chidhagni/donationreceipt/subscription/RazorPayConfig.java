package com.chidhagni.donationreceipt.subscription;

import com.razorpay.RazorpayClient;
import com.razorpay.RazorpayException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

@Configuration
public class RazorPayConfig {

    @Value("${rzp-client-Id}")
    private String razorpay_clientId;

    @Value("${rzp-client-Secret}")
    private String razorpay_clientSecret;

    public String getRazorpay_clientId() {
        return razorpay_clientId;
    }

    public String getRazorpay_clientSecret() {
        return razorpay_clientSecret;
    }

    @Bean
    public RazorpayClient razorPayClient() {
        try {
            return new RazorpayClient(razorpay_clientId, razorpay_clientSecret);
        } catch (RazorpayException e) {
            throw new RuntimeException("Razorpay client initialization Failed" + e);
        }
    }

    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public JSONObject jsonObject() {
        return new JSONObject();
    }
}
