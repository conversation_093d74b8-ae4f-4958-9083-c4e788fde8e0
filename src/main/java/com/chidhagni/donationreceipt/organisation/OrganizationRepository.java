package com.chidhagni.donationreceipt.organisation;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualVerificationAuditDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.OrganisationDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;

import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.organisation.dto.response.GetOrganisationDropDown;
import com.chidhagni.donationreceipt.organisation.dto.response.OrganisationNameDTO;

import com.chidhagni.donationreceipt.organisation.dto.request.OrganisationPaginationRequest;
import com.chidhagni.donationreceipt.organisation.dto.request.TenantsDTO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;


import java.time.LocalDateTime;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.when;



@Repository
@RequiredArgsConstructor
@Slf4j
public class OrganizationRepository {

    private final OrganisationDao organisationDao;
    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;

    private final IndividualVerificationAuditDao individualVerificationAuditDao;
    private final IndividualRoleRepository individualRoleRepository;

    public Optional<String> getNameOrCompanyNameById(UUID id) {
        try {
            return organisationDao.ctx().select(ORGANISATION.NAME
                    )
                    .from(ORGANISATION)
                    .where(ORGANISATION.ID.eq(id))
                    .fetchOptional("name", String.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching organization name by organisation id ", ex);
        }
    }


    public Organisation getById(UUID id) {
        Organisation organisation = null;
        try {
            organisation = organisationDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching Organisation by it's id", ex);
        }
        return organisation;
    }

    public Organisation fetchOrganizationByIndividualId(UUID individualId) {
        // Fetch individual roles associated with the given individualId
        List<IndividualRole> individualRole = individualRoleRepository.getByIndivdiualId(individualId);

        if (individualRole != null && !individualRole.isEmpty()) {
            // Extract the first orgId from the roles
            UUID orgId = individualRole.get(0).getOrgId();

            if (orgId != null) {
                // Fetch and return the Organization entity using the orgId
                return organisationDao.fetchOneById(orgId);
            }
        }
        return null; // Return null if no organization is found
    }

    /**
     * Queries the database to fetch organization names by category.
     *
     * <p>This method retrieves organizations filtered by the given category type,
     * excluding specific employee organizations.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Executes a database query to retrieve organization names and IDs.</li>
     *   <li>Filters records where `CATEGORY` matches the provided category.</li>
     *   <li>Excludes organizations where:
     *     <ul>
     *       <li>`CATEGORY = EMPLOYEE`</li>
     *       <li>`NAME = devOrganisationName`</li>
     *     </ul>
     *   </li>
     *   <li>Maps the result into a list of `OrganisationNameDTO` objects.</li>
     * </ul>
     *
     * <p><b>Notes:</b></p>
     * <ul>
     *   <li>Throws an `InternalServerError` if an exception occurs.</li>
     *   <li>Returns an empty list if no records match the filtering conditions.</li>
     * </ul>
     *
     * @param category The category type of organizations to fetch.
     * @return A list of `OrganisationNameDTO` objects.
     * @throws InternalServerError If an error occurs during database access.
     */
    public List<OrganisationNameDTO> fetchOrganizationNameByCategoryType(OrganisationEnums category) {
        try {
            return organisationDao.ctx()
                    .select(ORGANISATION.ID.as("id"), ORGANISATION.NAME.as("name"))
                    .from(ORGANISATION)
                    .where(ORGANISATION.CATEGORY.eq(category.name()))
                    .and(ORGANISATION.CATEGORY.ne(OrganisationEnums.SUPER_ADMIN.name()))
                    .fetchInto(OrganisationNameDTO.class);
        } catch (Exception e) {
            throw new InternalServerError("Exception occurred while fetching organisation names by category");
        }
    }

    public boolean isOrganisationExist(UUID id) {
        try {
            return organisationDao.ctx().fetchExists(organisationDao.ctx().select(ORGANISATION.ID).from(ORGANISATION)
                    .where(ORGANISATION.ID.eq(id)));
        } catch (Exception ex) {
            throw new IllegalArgumentException("Exception occurred while checking the organisation exist by id");
        }
    }

    public String getOrganisationName(UUID id)
    {
        return organisationDao.ctx().select(ORGANISATION.NAME).from(ORGANISATION)
                .where(ORGANISATION.ID.eq(id))
                .fetchOneInto(String.class);
    }

    public void insertOrganisation(Organisation organisation){
        try{
            organisationDao.insert(organisation);
        }
        catch (Exception ex){
            throw new InternalServerError("Exception while inserting the organisation",ex);
        }
    }

    public void updateOrganisation(Organisation organisation) {
        try {
            organisationDao.update(organisation);
            log.info("Successfully updated the organisation");
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while updating organisation meta data", ex);
        }
    }

    public List<TenantsDTO> getAllTenants(Condition finalCondition, List<SortField<?>> sortFields,
                                          OrganisationPaginationRequest organisationPaginationRequest) {
        try {
            Individual createdBy = INDIVIDUAL.as("createdBy");
            Individual updatedBy = INDIVIDUAL.as("updatedBy");

            // Define fields using DSL.name to refer to aliased fields
            Field<UUID> createdById = createdBy.field(DSL.name("id"), UUID.class);
            Field<UUID> updatedById = updatedBy.field(DSL.name("id"), UUID.class);

            Field<String> createdByEmail = createdBy.field(DSL.name("email"), String.class);
            Field<String> updatedByEmail = updatedBy.field(DSL.name("email"), String.class);

            Integer offset = (organisationPaginationRequest.getPage() - 1) * organisationPaginationRequest.getPageSize();
            Integer pageSize = organisationPaginationRequest.getPageSize();

            // Main query for tenant data
            List<TenantsDTO> tenantsDTOList = organisationDao.ctx()
                    .select(
                            ORGANISATION.ID.as("orgId"),
                            ORGANISATION.NAME.as("trustName"),
                            field("ORGANISATION.meta_data ->> 'orgEmail'", String.class).as("orgEmail"),
                            INDIVIDUAL.NAME.as("tenantAdminName"),
                            INDIVIDUAL.EMAIL.as("tenantAdminEmail"),
                            INDIVIDUAL.MOBILE_NUMBER.as("tenantAdminMobileNumber"),
                            ORGANISATION.IS_ACTIVE.as("isActiveOrg"),
                            ORGANISATION.CREATED_ON.as("createdOn"),
                            ORGANISATION.UPDATED_ON.as("updatedOn"),
                            createdByEmail.as("createdBy"),
                            updatedByEmail.as("updatedBy"),
                            DSL.val("VERIFIED").as("verificationStatus")
                    )
                    .from(ORGANISATION)
                    .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(createdBy).on(ORGANISATION.CREATED_BY.eq(createdById))
                    .leftJoin(updatedBy).on(ORGANISATION.UPDATED_BY.eq(updatedById))
                    .where(INDIVIDUAL_ROLE.ROLE_ID.eq(tenantAdminRoleId))
                    .and(finalCondition)
                    .orderBy(ORGANISATION.CREATED_ON)
                    .fetchInto(TenantsDTO.class);

            List<TenantsDTO> unverifiedFromIndividualVerification = individualVerificationAuditDao.ctx()
                    .select(
                            INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.as("orgId"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'trustName'", String.class).as("trustName"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'orgEmail'", String.class).as("orgEmail"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'individualName'", String.class).as("tenantAdminName"),
                            INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_VALUE.as("tenantAdminEmail"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'contactNumber'", String.class).as("tenantAdminMobileNumber"),
                            DSL.val(false, Boolean.class).as("isActiveOrg"),
                            INDIVIDUAL_VERIFICATION_AUDIT.CREATED_ON.as("createdOn"),
                            INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.as("verificationStatus"),
                            createdByEmail.as("createdBy")
                    )
                    .from(INDIVIDUAL_VERIFICATION_AUDIT)
                    .leftJoin(createdBy).on(INDIVIDUAL_VERIFICATION_AUDIT.CREATED_BY.eq(createdById))
                    .where(INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.isNull())
                    .and(INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.eq(VerificationStatusEnum.PENDING.name()))
                    .fetchInto(TenantsDTO.class);

            List<TenantsDTO> combinedList = new ArrayList<>(tenantsDTOList);
            combinedList.addAll(unverifiedFromIndividualVerification);

            int start = Math.min(offset, combinedList.size());
            int end = Math.min(offset + pageSize, combinedList.size());
            return combinedList.subList(start, end);

        } catch (Exception ex) {
            throw new IllegalArgumentException("Exception while fetching all tenants by admin", ex);
        }
    }

    public Integer getAllTenantsCount(Condition finalCondition) {
        try {
            // Count of verified tenants
            int verifiedCount = organisationDao.ctx()
                    .selectCount()
                    .from(ORGANISATION)
                    .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .where(INDIVIDUAL_ROLE.ROLE_ID.eq(tenantAdminRoleId))
                    .and(finalCondition != null ? finalCondition : DSL.noCondition())
                    .fetchOne(0, int.class);

            // Count of unverified tenants
            int unverifiedCount = individualVerificationAuditDao.ctx()
                    .selectCount()
                    .from(INDIVIDUAL_VERIFICATION_AUDIT)
                    .where(INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.isNull())
                    .and(INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.eq(VerificationStatusEnum.PENDING.name()))
                    .fetchOne(0, int.class);

            return verifiedCount + unverifiedCount;
        } catch (Exception ex) {
            throw new IllegalArgumentException("Exception while fetching tenant count", ex);
        }
    }


    public List<Organisation> findAllByCreatedOnBetween(LocalDateTime startOfDay, LocalDateTime endOfDay) {

        return organisationDao.ctx().select()
                .from(ORGANISATION)
                .where(ORGANISATION.CREATED_ON.between(startOfDay, endOfDay))
                .fetchInto(Organisation.class);
    }

    public List<GetOrganisationDropDown> getOrganisationDropDowns() {

        return organisationDao.ctx().select(ORGANISATION.ID.as("id"),ORGANISATION.NAME.as("orgName"))
                .from(ORGANISATION)
                .where(ORGANISATION.CATEGORY.eq(OrganisationEnums.DONOR.name()))
                .fetchInto(GetOrganisationDropDown.class);
    }

}


