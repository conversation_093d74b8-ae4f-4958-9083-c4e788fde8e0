package com.chidhagni.donationreceipt.organisation;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.*;
import com.chidhagni.donationreceipt.documentrepo.DocumentRepoService;
import com.chidhagni.donationreceipt.documentrepo.dto.request.DocumentRepoDTO;
import com.chidhagni.donationreceipt.documentrepo.dto.request.participantDetailsDTO;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individual.IndividualService;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorBasicProfileDto;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorDTO;
import com.chidhagni.donationreceipt.individual.util.IndividualMapper;
import com.chidhagni.donationreceipt.individual.util.IndividualMapperI;
import com.chidhagni.donationreceipt.individualpermission.IndividualPermissionRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.IndividualVerificationAuditRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.organisation.dto.OrganisationTenantMetaData;
import com.chidhagni.donationreceipt.organisation.dto.request.*;
import com.chidhagni.donationreceipt.organisation.dto.response.DonorEmailAndPanNoResponse;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllDonorEmailPanNoResponses;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllTenantsDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.GetOrganisationDropDown;
import com.chidhagni.donationreceipt.organisation.dto.response.OrganisationNameDTO;
import com.chidhagni.donationreceipt.roles.RolesRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.Aes256EncryptionUtils;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.mail.MessagingException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;


@Service
@Slf4j
@RequiredArgsConstructor
public class OrganisationService {

    private final OrganizationRepository organizationRepository;
    private final IndividualRoleRepository individualRoleRepository;
    private final IndividualRepository individualRepository;
    private final IndividualService individualService;
    private final IndividualMapperI individualMapperI;

    private final DonorsRepository donorsRepository;
    private final Aes256EncryptionUtils encryptionUtils;

    private final DocumentRepoService documentRepoService;
    private final IndividualVerificationAuditRepository individualVerificationAuditRepository;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;
    private final RolesRepository rolesRepository;
    private final IndividualPermissionRepository individualPermissionRepository;

    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;

    @Value("${donation.receipt.category}")
    private UUID categoryUuid;

    @Value("${donation.receipt.subcategory.8g}")
    private UUID eightGSubCategoryUuid;

    @Value("${donation.receipt.subcategory.logo}")
    private UUID logoSubCategoryUuid;

    @Value("${donation.receipt.recipient.org}")
    private UUID recipientOrgId;

    @Value("${donation.receipt.recipient.individual}")
    private UUID recipientIndividualId;


    public GetResponseBasicProfileDTO getResponseBasicProfileDTO(UUID orgId, UserPrincipal userPrincipal) {
        List<OrgIndividuals> orgIndividualsList = new ArrayList<>();
        Organisation organisation = organizationRepository.getById(orgId);
        List<IndividualRole> individualRoleList = individualRoleRepository.getIndividualIdByOrganisationId(orgId);

        IndividualRole tenantAdminRole = individualRoleList.stream()
                .filter(role -> role.getRoleId().equals(tenantAdminRoleId))
                .findFirst()
                .orElse(null);

        Individual loggedIndividual = null;
        if (tenantAdminRole != null) {
            loggedIndividual = individualRepository.getById(tenantAdminRole.getIndividualId());
        }

        for (IndividualRole individualRole : individualRoleList) {
            OrgIndividuals orgIndividuals = new OrgIndividuals();
            Individual individual = individualRepository.getById(individualRole.getIndividualId());
//            if (individualRole.getRoleId().equals(tenantAdminRoleId)) {
//                orgIndividuals.setId(UUID.randomUUID());
//            }
            orgIndividuals.setId(UUID.randomUUID());
            orgIndividuals.setIndividualId(individual.getId());
            orgIndividuals.setEmail(individual.getEmail());
            orgIndividuals.setName(individual.getName());
            orgIndividuals.setRoleId(individualRole.getRoleId());
            orgIndividuals.setMobileNumber(individual.getMobileNumber());
            orgIndividuals.setIsActive(individual.getIsActive());
            orgIndividuals.setCreatedBy(individual.getCreatedBy());
            orgIndividuals.setCreatedOn(individual.getCreatedOn());
            orgIndividuals.setUpdatedBy(individual.getUpdatedBy());
            orgIndividuals.setUpdatedOn(individual.getUpdatedOn());
            orgIndividuals.setIsVerified(VerificationStatusEnum.VERIFIED.name());
            orgIndividualsList.add(orgIndividuals);

        }

        List<IndividualVerificationAudit> unverifiedIndividuals = individualVerificationAuditRepository.getByOrgIdAndPending(orgId);
        for (IndividualVerificationAudit individualVerificationAudit : unverifiedIndividuals) {
            OrgIndividuals orgIndividuals = new OrgIndividuals();
            orgIndividuals.setId(individualVerificationAudit.getId());
            orgIndividuals.setName(individualVerificationAudit.getMetaData().getIndividualName());
            orgIndividuals.setEmail(individualVerificationAudit.getContactValue());
            orgIndividuals.setIsVerified(individualVerificationAudit.getVerificationStatus());
            orgIndividuals.setRoleId(individualVerificationAudit.getRoleId());
            orgIndividuals.setMobileNumber(individualVerificationAudit.getMetaData().getContactNumber());
            orgIndividuals.setIsActive(Boolean.FALSE);
            orgIndividualsList.add(orgIndividuals);
        }

        MetaDataDTO metaData = organisation.getMetaData();
        OrganisationTenantMetaData tenantMetaData = null;
        if (metaData instanceof OrganisationTenantMetaData) {
            tenantMetaData = (OrganisationTenantMetaData) metaData;
        }
        return GetResponseBasicProfileDTO.builder()
                .name(loggedIndividual != null ? loggedIndividual.getName() : null)
                .mobileNumber(loggedIndividual != null ? loggedIndividual.getMobileNumber() : null)
                .email(loggedIndividual != null ? loggedIndividual.getEmail() : null)
                .trustName(organisation.getName())
                .orgId(organisation.getId())
                .orgEmail(tenantMetaData != null ? tenantMetaData.getOrgEmail() : null)
                .address(tenantMetaData != null ? tenantMetaData.getAddress() : null)
                .registrationNo(tenantMetaData != null ? tenantMetaData.getRegistrationNo() : null)
                .panNo(tenantMetaData != null ? tenantMetaData.getPanNo() : null)
                .g80RegistrationNo(tenantMetaData != null ? tenantMetaData.getG80RegistrationNo() : null)
                .state(tenantMetaData != null ? tenantMetaData.getState() : null)
                .pinCode(tenantMetaData != null ? tenantMetaData.getPinCode() : null)
                .orgIndividualsList(orgIndividualsList)
                .createdOn(organisation.getCreatedOn())
                .updatedOn(organisation.getUpdatedOn())
                .createdBy(organisation.getCreatedBy())
                .updatedBy(organisation.getUpdatedBy())
                .isActiveOrg(organisation.getIsActive())
                .logoFileLocation(tenantMetaData.getLogoFileLocation())
                .g80CertificationFileLocationPageOne(tenantMetaData.getG80CertificationFileLocationPageOne())
                .g80CertificationFileLocationPageTwo(tenantMetaData.getG80CertificationFileLocationPageTwo())
                .build();
    }


    public UUID patchBasicProfile(UUID orgId, PatchBasicProfileDTO patchBasicProfileDTO, UserPrincipal userPrincipal,
                                  MultipartFile logoFile, MultipartFile eightyGFileOne, MultipartFile eightyGFileTwo) throws MessagingException, IOException {

        Organisation organisation = organizationRepository.getById(orgId);
        if (organisation == null) {
            throw new IllegalArgumentException("Organisation Not found");
        }
        log.info("Trust Information Mapping");
        Organisation updatedOrganisation = mapPatchRequestToOrganisation(patchBasicProfileDTO, organisation);


        log.info("Basic Profile Updating");
        log.info("Retrieving Tenant Admin Individual Role");
        List<IndividualRole> loggedInIndividual = individualRoleRepository.getIndividualIdByOrganisationId(orgId);

        log.info("Retrieving Tenant Admin Individual");
        Individual individual = individualRepository.getById(loggedInIndividual.get(0).getIndividualId());
        if (patchBasicProfileDTO.getName() != null) {
            individual.setName(patchBasicProfileDTO.getName());
        }

        if (patchBasicProfileDTO.getMobileNumber() != null) {
            individual.setMobileNumber(patchBasicProfileDTO.getMobileNumber());
        }

        if (patchBasicProfileDTO.getEmail() != null) {
            individual.setEmail(patchBasicProfileDTO.getEmail());
        }
        log.info("Updating the tenant admin details");
        individualRepository.update(individual);
        log.info("Basic Profile Updation done");


        log.info("Adding the members");
        if (patchBasicProfileDTO.getAddUnVerifiedIndividualsList() != null && !patchBasicProfileDTO.getAddUnVerifiedIndividualsList().isEmpty()) {
            List<OrgIndividuals> orgIndividualsList = patchBasicProfileDTO.getAddUnVerifiedIndividualsList();

            for (OrgIndividuals orgIndividuals : orgIndividualsList) {
                log.info("Sending activation link to added individuals");
                individualService.sendActivationLinkToIndividuals(orgIndividuals, orgId);
            }
        }
        log.info("Edit the individuals who are already present in the org");
        if (patchBasicProfileDTO.getEditOrgIndividuals() != null && !patchBasicProfileDTO.getEditOrgIndividuals().isEmpty()) {

            List<OrgIndividuals> editIndividuals = patchBasicProfileDTO.getEditOrgIndividuals();
            for (OrgIndividuals orgIndividuals : editIndividuals) {
                individualService.editIndividual(orgIndividuals, userPrincipal);
            }
        }
        log.info("Uploading the logo file");

        MetaDataDTO metaData = updatedOrganisation.getMetaData();
        OrganisationTenantMetaData tenantMetaData = null;
        if (metaData instanceof OrganisationTenantMetaData) {
            tenantMetaData = (OrganisationTenantMetaData) metaData;
        }
        if (logoFile != null) {
            String logoPath = uploadDocumentAndGetPath(logoFile, categoryUuid, logoSubCategoryUuid,
                    individual.getId(), orgId, recipientIndividualId, recipientOrgId, userPrincipal);
            tenantMetaData.setLogoFileLocation(logoPath);

        }
        log.info("Uploading the 80GCertificationPageOne file");
        if (eightyGFileOne != null) {
            String eightyGOnePath = uploadDocumentAndGetPath(eightyGFileOne, categoryUuid, eightGSubCategoryUuid,
                    individual.getId(), orgId, recipientIndividualId, recipientOrgId, userPrincipal);
            tenantMetaData.setG80CertificationFileLocationPageOne(eightyGOnePath);
        }
        log.info("Uploading the 80GCertificationPageTwo file");
        if (eightyGFileTwo != null) {
            String eightyGTwoPath = uploadDocumentAndGetPath(eightyGFileTwo, categoryUuid, eightGSubCategoryUuid,
                    individual.getId(), orgId, recipientIndividualId, recipientOrgId, userPrincipal);
            tenantMetaData.setG80CertificationFileLocationPageTwo(eightyGTwoPath);
        }

        log.info("Updating the organisation with trust information and document");
        organizationRepository.updateOrganisation(updatedOrganisation);
        return organisation.getId();

    }

    public Organisation mapPatchRequestToOrganisation(PatchBasicProfileDTO getResponseBasicProfileDTO, Organisation organisation) {

        MetaDataDTO metaData = organisation.getMetaData();
        OrganisationTenantMetaData tenantMetaData = null;
        if (metaData instanceof OrganisationTenantMetaData) {
            tenantMetaData = (OrganisationTenantMetaData) metaData;
        }
        if (getResponseBasicProfileDTO.getTrustName() != null) {
            organisation.setName(getResponseBasicProfileDTO.getTrustName());
        }
        if (getResponseBasicProfileDTO.getOrgEmail() != null) {
            tenantMetaData.setOrgEmail(getResponseBasicProfileDTO.getOrgEmail());
        }
        if (getResponseBasicProfileDTO.getAddress() != null) {
            tenantMetaData.setAddress(getResponseBasicProfileDTO.getAddress());
        }
        if (getResponseBasicProfileDTO.getRegistrationNo() != null) {
            tenantMetaData.setRegistrationNo(getResponseBasicProfileDTO.getRegistrationNo());
        }
        if (getResponseBasicProfileDTO.getPanNo() != null) {
            tenantMetaData.setPanNo(getResponseBasicProfileDTO.getPanNo());
        }
        if (getResponseBasicProfileDTO.getG80RegistrationNo() != null) {
            tenantMetaData.setG80RegistrationNo(getResponseBasicProfileDTO.getG80RegistrationNo());
        }
        if (getResponseBasicProfileDTO.getState() != null) {
            tenantMetaData.setState(getResponseBasicProfileDTO.getState());
        }
        if (getResponseBasicProfileDTO.getPinCode() != null) {
            tenantMetaData.setPinCode(getResponseBasicProfileDTO.getPinCode());
        }
        return organisation;
    }


    private String uploadDocumentAndGetPath(MultipartFile file, UUID categoryId, UUID subCategoryId,
                                            UUID senderIndividualId, UUID senderOrgId, UUID recipientIndividualId,
                                            UUID recipientOrgId, UserPrincipal userPrincipal) throws IOException {

        if (file == null || file.isEmpty()) {
            return null;
        }

        DocumentRepoDTO documentRepoDTO = new DocumentRepoDTO();
        documentRepoDTO.setCategoryId(categoryId);
        documentRepoDTO.setSubCategoryId(subCategoryId);

        participantDetailsDTO sender = new participantDetailsDTO();
        sender.setIndividualId(senderIndividualId);
        sender.setOrganisationId(senderOrgId);
        documentRepoDTO.setSenderDetails(sender);

        participantDetailsDTO recipient = new participantDetailsDTO();
        recipient.setIndividualId(recipientIndividualId);
        recipient.setOrganisationId(recipientOrgId);
        documentRepoDTO.setRecipientDetails(List.of(recipient));

        UUID documentId = documentRepoService.upload(file, documentRepoDTO, userPrincipal);
        List<String> paths = documentRepoService.getByDocumentRepoIdList(Collections.singletonList(documentId));

        return paths.isEmpty() ? null : paths.get(0);
    }


    public GetAllTenantsDTO getAllTenants(OrganisationPaginationRequest organisationPaginationRequest) {
        applyDefaults(organisationPaginationRequest);
        Condition finalCondition = getSearchingCondition(organisationPaginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();
        List<TenantsDTO> allTenants = organizationRepository.getAllTenants(finalCondition, sortFields, organisationPaginationRequest);

        Integer count = organizationRepository.getAllTenantsCount(finalCondition);
        return GetAllTenantsDTO.builder()
                .tenantsDTOList(allTenants)
                .count(count)
                .build();

    }

    public void applyDefaults(OrganisationPaginationRequest organisationPaginationRequest) {
        if (organisationPaginationRequest.getPageSize() == null || organisationPaginationRequest.getPageSize() < 1) {
            organisationPaginationRequest.setPageSize(pageSize);
        }
        if (organisationPaginationRequest.getPage() == null || organisationPaginationRequest.getPage() < 1) {
            organisationPaginationRequest.setPage(pageNo);
        }
    }

    public Condition getSearchingCondition(OrganisationPaginationRequest organisationPaginationRequest) {

        String searchByTrustName = organisationPaginationRequest.getTrustNameFilter();
        String searchByEmail = organisationPaginationRequest.getContactPersonEmailFilter();
        String searchByMobileNumber = organisationPaginationRequest.getContactPersonMobileFilter();
        String searchByContactPersonName = organisationPaginationRequest.getContactPersonNameFilter();

        String searchKeyword = organisationPaginationRequest.getSearchKeyword();

        if (searchKeyword == null) {
            searchKeyword = "";
        }
        List<Condition> conditions = new ArrayList<>();


        if (searchByTrustName != null && !searchByTrustName.isEmpty()) {
            conditions.add(ORGANISATION.NAME.eq(searchByTrustName));
        }

        if (searchByEmail != null && !searchByEmail.isEmpty()) {
            conditions.add(DSL.exists(
                    DSL.selectOne()
                            .from(INDIVIDUAL_ROLE)
                            .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                            .where(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                            .and(INDIVIDUAL.EMAIL.eq(searchByEmail))
            ));
        }
        if (searchByMobileNumber != null && !searchByMobileNumber.isEmpty()) {
            conditions.add(DSL.exists(
                    DSL.selectOne()
                            .from(INDIVIDUAL_ROLE)
                            .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                            .where(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                            .and(INDIVIDUAL.MOBILE_NUMBER.eq(searchByMobileNumber))
            ));
        }

        if (searchByContactPersonName != null && !searchByContactPersonName.isEmpty()) {
            conditions.add(DSL.exists(
                    DSL.selectOne()
                            .from(INDIVIDUAL_ROLE)
                            .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                            .where(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                            .and(INDIVIDUAL.NAME.eq(searchByContactPersonName))
            ));
        }
        if (!searchKeyword.isEmpty()) {
            conditions.add(
                    DSL.or(
                            ORGANISATION.NAME.containsIgnoreCase(searchKeyword),
                            DSL.exists(
                                    DSL.selectOne()
                                            .from(INDIVIDUAL_ROLE)
                                            .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                                            .where(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                                            .and(INDIVIDUAL.NAME.containsIgnoreCase(searchKeyword))
                            )
                    )
            );
        }

        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }


    public IndividualDonorBasicProfileDto updateBasicProfileDonor(UUID orgId,
                                                                  IndividualDonorBasicProfileDto individualDonorBasicProfileDto,
                                                                  UserPrincipal userPrincipal) {
        Organisation organisation = organizationRepository.getById(orgId);
        if (organisation == null) {
            throw new IllegalArgumentException("Organisation Not found");
        }
        List<IndividualRole> individualRole = individualRoleRepository.getIndividualIdByOrganisationId(orgId);
        if (individualRole == null || individualRole.isEmpty()) {
            throw new IllegalStateException("No individual roles found for organisation id: " + orgId);
        }
        for (IndividualRole role : individualRole) {
            Individual response = individualMapperI.updateIndividualFromDto(
                    role.getIndividualId(),
                    individualDonorBasicProfileDto,
                    userPrincipal
            );
            individualRepository.update(response);
        }

        updateOrganisation(organisation, individualDonorBasicProfileDto, userPrincipal);
        organizationRepository.updateOrganisation(organisation);

        IndividualRole role = individualRole.get(0);
        if (role.getIndividualId() == null) {
            throw new IllegalStateException("Individual ID is null in role");
        }
        UUID newRoleId = individualDonorBasicProfileDto.getRoleId();
        if (newRoleId != null) {
            role.setRoleId(newRoleId);
        }
        role.setUpdatedBy(userPrincipal.getId());
        role.setUpdatedOn(DateUtils.currentTimeIST());
        individualRoleRepository.upsertIndividualRoles(individualRole);

        Roles roles = rolesRepository.getRoleById(role.getRoleId());
        if (roles == null || roles.getPermissions() == null) {
            throw new IllegalStateException("No permissions found for role ID: " + role.getRoleId());
        }
        List<IndividualPermission> individualPermission = individualPermissionRepository.getIndividualPermissionByIndividualId(individualRole.get(0).getIndividualId());
        if (individualPermission == null || individualPermission.isEmpty()) {
            throw new IllegalStateException("No individual permissions found for individual ID: " + individualRole.get(0).getIndividualId());
        }
        IndividualPermission permission = individualPermission.get(0);
        permission.setPermissions(roles.getPermissions());
        permission.setUpdatedOn(DateUtils.currentTimeIST());
        permission.setUpdatedBy(userPrincipal.getId());
        individualPermissionRepository.upsertIndividualPermissions(individualPermission);
        Individual updatedIndividual = individualRepository.getById(individualRole.get(0).getIndividualId());
        return IndividualMapper.mapToIndividualBasicProfileDto(updatedIndividual, organisation, role.getRoleId());
    }


    public IndividualDonorDTO getByDonorOrgIdId(UUID orgId) {
        Organisation organisation = organizationRepository.getById(orgId);
        List<IndividualRole> individualRoleList = individualRoleRepository.getIndividualIdByOrganisationId(orgId);
        Individual individual = individualRepository.getById(individualRoleList.get(0).getIndividualId());
        return IndividualMapper.mapDonor(individual, individualRoleList.get(0).getRoleId(), organisation);
    }


    private void updateOrganisation(Organisation organisation,
                                    IndividualDonorBasicProfileDto dto,
                                    UserPrincipal userPrincipal) {
        organisation.setName(dto.getDonorOrgName());
        organisation.setUpdatedBy(userPrincipal.getId());
        organisation.setUpdatedOn(DateUtils.currentTimeIST());
        MetaDataDTO metaData = organisation.getMetaData();
        if (metaData instanceof OrganisationDonorMetaDataDto) {
            OrganisationDonorMetaDataDto donorMetaData = (OrganisationDonorMetaDataDto) metaData;
            donorMetaData.setDonorType(dto.getDonorType());
            donorMetaData.setDonorReferralSource(dto.getDonorReferralSource());
            donorMetaData.setDonorReferralSourceAnyOther(dto.getDonorReferralSourceAnyOther());
        }
    }


    public List<OrganisationNameDTO> fetchOrganizationNameByCategoryType(OrganisationEnums category) {

        return organizationRepository.fetchOrganizationNameByCategoryType(category);
    }

    public GetAllDonorEmailPanNoResponses getAllDonorEmailPanNoResponsesByOrgId(UUID orgId) {
        List<DonorEmailAndPanNoResponse> donorEmailAndPanNoResponses =
                donorsRepository.getAllDonorEmailPanNoResponsesByOrgId(orgId);

        // Decrypt PAN numbers before returning
        for (DonorEmailAndPanNoResponse response : donorEmailAndPanNoResponses) {
            String encryptedPan = response.getEncryptedPanNo();
            String panNonce = response.getPanNoNonce();
            if (encryptedPan != null && !encryptedPan.isEmpty() && panNonce != null && !panNonce.isEmpty()) {
                String decryptedPan = encryptionUtils.decrypt(encryptedPan, panNonce);
                response.setPanNo(decryptedPan);
            } else {
                response.setPanNo(null);
            }
            // Clear encrypted fields from response for security
            response.setEncryptedPanNo(null);
            response.setPanNoNonce(null);
        }

        return GetAllDonorEmailPanNoResponses.builder()
                .donorEmailAndPanNoResponsesList(donorEmailAndPanNoResponses)
                .build();
    }

    public List<GetOrganisationDropDown> getOrganisationDropDowns()
    {
        return organizationRepository.getOrganisationDropDowns();
    }
}
