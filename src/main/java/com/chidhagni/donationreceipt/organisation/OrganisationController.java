package com.chidhagni.donationreceipt.organisation;


import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorBasicProfileDto;
import com.chidhagni.donationreceipt.individual.dto.request.IndividualDonorDTO;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.organisation.dto.request.GetResponseBasicProfileDTO;
import com.chidhagni.donationreceipt.organisation.dto.request.OrganisationPaginationRequest;
import com.chidhagni.donationreceipt.organisation.dto.request.PatchBasicProfileDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllDonorEmailPanNoResponses;
import com.chidhagni.donationreceipt.organisation.dto.response.OrganisationNameDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllTenantsDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.GetOrganisationDropDown;
import com.chidhagni.donationreceipt.organisation.dto.response.OrganisationNameDTO;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.mail.MessagingException;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.individual.constants.IndividualMetaData.INDIVIDUAL_GET_RES_V1;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;


@RestController
@RequestMapping("/api/v1/organisation")
@RequiredArgsConstructor
@Slf4j
public class OrganisationController {

    private final OrganisationService organisationService;
   private final ObjectMapper objectMapper;

    @GetMapping(path = "/{categoryType}", produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<List<OrganisationNameDTO>> fetchOrganizationNameByCategoryType(
            @PathVariable("categoryType") OrganisationEnums categoryType) {
        return new ResponseEntity<>(organisationService.fetchOrganizationNameByCategoryType(categoryType), HttpStatus.OK);
    }


    @GetMapping(path = {"/org-basic-profile/{orgId}"})
    public ResponseEntity<GetResponseBasicProfileDTO> getResponseBasicProfileDTO(@PathVariable UUID orgId,@CurrentUser UserPrincipal userPrincipal) {
        return new ResponseEntity<>(organisationService.getResponseBasicProfileDTO(orgId,userPrincipal), HttpStatus.OK);
    }


    @PatchMapping(path = {"/{orgId}/organisation-meta-data"})
    public ResponseEntity<UUID> updateOrganisationMetaData(@PathVariable("orgId") UUID orgId
            , @RequestParam String patchBasicProfileDTO
            , @CurrentUser UserPrincipal userPrincipal, @RequestParam(value = "file", required = false)MultipartFile file,
                                                           @RequestParam(value = "file80GOne", required = false)MultipartFile file80GOne,
                                                           @RequestParam(value = "file80GTwo", required = false)MultipartFile file80GTwo) throws IOException, MessagingException {

      PatchBasicProfileDTO patchBasicProfileDTO1 = null;
        if (patchBasicProfileDTO != null) {
            patchBasicProfileDTO1 = objectMapper.readValue(patchBasicProfileDTO, new TypeReference<PatchBasicProfileDTO>() {});
        }
        return new ResponseEntity<>(
                organisationService.patchBasicProfile(orgId, patchBasicProfileDTO1, userPrincipal,file,file80GOne,file80GTwo), HttpStatus.OK);
    }


    @PostMapping(path = {"/admin/all"})
    public ResponseEntity<GetAllTenantsDTO> getAllWorkOrders(@RequestBody(required = false)
                                                             OrganisationPaginationRequest organisationPaginationRequest){
        log.info("Fetching all work orders by admin");
        if(organisationPaginationRequest == null){
            log.info("Pagination request is null so assigning default one");
            organisationPaginationRequest = OrganisationPaginationRequest.builder().build();
        }
        return new ResponseEntity<>(organisationService.getAllTenants(organisationPaginationRequest), HttpStatus.ACCEPTED);
    }



    @PatchMapping(path = {"/update-individual/{orgId}"})
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<IndividualDonorBasicProfileDto> updateDonorBasicProfile(@PathVariable("orgId")UUID orgId,
                                                                                  @RequestBody IndividualDonorBasicProfileDto individualDonorBasicProfileDto,
                                                                                  @CurrentUser UserPrincipal userPrincipal)
    {
        IndividualDonorBasicProfileDto updatedDto = organisationService.updateBasicProfileDonor(
                orgId, individualDonorBasicProfileDto, userPrincipal);
        return ResponseEntity.ok(updatedDto);
    }


    @GetMapping(value= "/donor/{orgId}",produces = INDIVIDUAL_GET_RES_V1)
    public ResponseEntity<IndividualDonorDTO> getDonorByOrgId(@PathVariable UUID orgId) {
        IndividualDonorDTO individualDTO = organisationService.getByDonorOrgIdId(orgId);
        if (individualDTO != null) {
            return ResponseEntity.ok(individualDTO);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/get-all-donor-email-panNo/{orgId}")
    public ResponseEntity<GetAllDonorEmailPanNoResponses>getAllDonorEmailPanNoResponsesResponseEntity(@PathVariable UUID orgId)
    {
        return new ResponseEntity<>(organisationService.getAllDonorEmailPanNoResponsesByOrgId(orgId), HttpStatus.OK);
}
    @GetMapping("/organisation-dropdown")
    public List<GetOrganisationDropDown> getOrganisationDropDownResponse()
    {
        return  organisationService.getOrganisationDropDowns();
    }
}
