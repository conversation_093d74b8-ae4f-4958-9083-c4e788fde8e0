package com.chidhagni.donationreceipt.organisation.jooq;

import com.chidhagni.donationreceipt.organisation.MetaDataDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import lombok.extern.slf4j.Slf4j;
import org.jooq.JSONB;

@Slf4j
public class OrganisationMetaDataJsonConverter extends BaseJsonBJooqConverter<MetaDataDTO> {

    @Override
    public Class<MetaDataDTO> toType() {
        return MetaDataDTO.class;
    }

    @Override
    public MetaDataDTO from(JSONB jsonb) {
        if (jsonb == null || jsonb.data().isEmpty() || jsonb.data().equals("{}")) {
            log.debug("Empty or null JSONB data for meta_data, returning null");
            return null;
        }
        try {
            return super.from(jsonb);
        } catch (Exception e) {
            log.error("Failed to deserialize JSONB: {}", jsonb, e);
            throw new RuntimeException("Invalid metadata format", e);
        }
    }

    @Override
    public JSONB to(MetaDataDTO metaData) {
        if (metaData == null) {
            return null;
        }
        return super.to(metaData);
    }
}
