package com.chidhagni.donationreceipt.organisation;

import com.chidhagni.donationreceipt.organisation.dto.OrganisationTenantMetaData;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = OrganisationTenantMetaData.class, name = "TENANT"),
    @JsonSubTypes.Type(value = OrganisationDonorMetaDataDto.class, name = "DONOR")
})
public interface MetaDataDTO {
    String getType();
    void setType(String type);
}
