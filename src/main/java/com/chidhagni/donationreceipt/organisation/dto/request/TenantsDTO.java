package com.chidhagni.donationreceipt.organisation.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantsDTO {

    private UUID orgId;
    private String tenantAdminName;
    private String tenantAdminMobileNumber;
    private String tenantAdminEmail;

    private String trustName;
    private String orgEmail;

    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String createdBy;
    private String updatedBy;
    private Boolean isActiveOrg;

    private String verificationStatus;

}
