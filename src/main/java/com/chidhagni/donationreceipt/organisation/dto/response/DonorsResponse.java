package com.chidhagni.donationreceipt.organisation.dto.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorsResponse {

    private UUID id;
    private String name;
    private String email;
    private String contactNumber;
    private String address;
    private String panNo;
    private UUID roleId;
    private UUID donorType;
    private UUID donorReferralSource;
    private String donorReferralSourceAnyOther;
    private String donorOrgName;
    private UUID donorOrgId;

}
