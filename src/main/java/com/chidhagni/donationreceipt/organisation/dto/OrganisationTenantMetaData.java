package com.chidhagni.donationreceipt.organisation.dto;


import com.chidhagni.donationreceipt.organisation.MetaDataDTO;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganisationTenantMetaData implements MetaDataDTO {

    private String type= OrganisationEnums.TENANT.name();

    private String id;
    private String website;
    private String orgEmail;
    private String address;
    private String registrationNo;
    private String panNo;
    private String g80RegistrationNo;
    private String state;
    private String pinCode;
    private String logoFileLocation;
    private String g80CertificationFileLocationPageOne;
    private String g80CertificationFileLocationPageTwo;
}


