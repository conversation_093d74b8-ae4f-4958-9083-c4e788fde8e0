package com.chidhagni.donationreceipt.organisation.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetResponseBasicProfileDTO {


    //basic profile
    private String name;
    private String mobileNumber;
    private String email;


    //trust information
    private String trustName;
    private UUID orgId;
    private String orgEmail;
    private String address;
    private String registrationNo;
    private String panNo;
    private String g80RegistrationNo;
    private String state;
    private String pinCode;

    //Member - individuals- verified and individual_verification - pending
    private List<OrgIndividuals> orgIndividualsList;
    //org-audit columns
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
    private Boolean isActiveOrg;


    //documents
    private String logoFileLocation;
    private String g80CertificationFileLocationPageOne;
    private String g80CertificationFileLocationPageTwo;



}
