package com.chidhagni.donationreceipt.organisation.dto.response;


import com.chidhagni.donationreceipt.organisation.dto.request.TenantsDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAllDonors {


    private List<DonorsResponse> donorsResponses;
    private Integer count;
}
