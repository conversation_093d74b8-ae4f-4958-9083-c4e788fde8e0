package com.chidhagni.donationreceipt.organisation.dto;

import com.chidhagni.donationreceipt.organisation.constants.DefaultEmployeeCompanyTypeEnums;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmployeeProfileDTO {

    private DefaultEmployeeCompanyTypeEnums companyType;
    private String clientAddress;
    private String accountNumber;
    private String accountHolderName;
    private String bankName;
    private String ifscCode;
    private String branch;
    private String panNo;
    private String cin;
    private String gstNo;
    private String stateName;
}
