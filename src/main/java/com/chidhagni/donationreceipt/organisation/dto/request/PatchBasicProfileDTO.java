package com.chidhagni.donationreceipt.organisation.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatchBasicProfileDTO {

    private String name;
    private String mobileNumber;
    private String email;

    private String trustName;
    private String orgEmail;
    private String address;
    private String registrationNo;
    private String panNo;
    private String g80RegistrationNo;
    private String state;
    private String pinCode;

    private List<OrgIndividuals> addUnVerifiedIndividualsList;
    private List<OrgIndividuals> editOrgIndividuals;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
    private Boolean isActiveOrg;
}
