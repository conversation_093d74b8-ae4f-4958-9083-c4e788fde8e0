package com.chidhagni.donationreceipt.organisation.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
public class OrganisationPaginationRequest {
    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer pageSize = 10;

    @Builder.Default
    private String contactPersonNameFilter = "";

    @Builder.Default
    private String contactPersonEmailFilter = "";

    @Builder.Default
    private String contactPersonMobileFilter = "";


    @Builder.Default
    private String trustNameFilter = "";

    @Builder.Default
    private UUID roleFilter = null;

    private String searchKeyword;
}
