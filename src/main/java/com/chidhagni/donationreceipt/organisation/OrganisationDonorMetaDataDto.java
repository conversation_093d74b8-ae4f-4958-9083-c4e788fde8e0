package com.chidhagni.donationreceipt.organisation;


import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganisationDonorMetaDataDto implements  MetaDataDTO{

    private String type = OrganisationEnums.DONOR.name();
    private UUID donorType;
    private UUID donorReferralSource;
    private String donorReferralSourceAnyOther;
}
