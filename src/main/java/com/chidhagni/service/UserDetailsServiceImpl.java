package com.chidhagni.service;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.PermissionsData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final PermissionsData permissionsData;
    private final IndividualDao individualDao;



    @Override
    @Transactional
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {

        List<Individual> individualList = individualDao.fetchByEmail(email);

        if (individualList.isEmpty()) {
            throw new UsernameNotFoundException("Member not found");
        }
        Individual individual = individualList.get(0);

        return UserPrincipal.create(individual, permissionsData.getIndividualAuthorities(individual));
    }
}

