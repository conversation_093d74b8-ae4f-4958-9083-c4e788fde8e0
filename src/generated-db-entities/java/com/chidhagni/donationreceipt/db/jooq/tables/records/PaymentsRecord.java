/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.Payments;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaymentsRecord extends UpdatableRecordImpl<PaymentsRecord> implements Record15<UUID, BigDecimal, String, String, String, LocalDateTime, UUID, Boolean, UUID, UUID, UUID, LocalDateTime, LocalDateTime, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>payments.id</code>.
     */
    public PaymentsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>payments.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>payments.amount</code>.
     */
    public PaymentsRecord setAmount(BigDecimal value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>payments.amount</code>.
     */
    public BigDecimal getAmount() {
        return (BigDecimal) get(1);
    }

    /**
     * Setter for <code>payments.payment_status</code>.
     */
    public PaymentsRecord setPaymentStatus(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>payments.payment_status</code>.
     */
    public String getPaymentStatus() {
        return (String) get(2);
    }

    /**
     * Setter for <code>payments.razorpay_payment_method</code>.
     */
    public PaymentsRecord setRazorpayPaymentMethod(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>payments.razorpay_payment_method</code>.
     */
    public String getRazorpayPaymentMethod() {
        return (String) get(3);
    }

    /**
     * Setter for <code>payments.order_id</code>.
     */
    public PaymentsRecord setOrderId(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>payments.order_id</code>.
     */
    public String getOrderId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>payments.payment_date</code>.
     */
    public PaymentsRecord setPaymentDate(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>payments.payment_date</code>.
     */
    public LocalDateTime getPaymentDate() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>payments.donation_receipt_id</code>.
     */
    public PaymentsRecord setDonationReceiptId(UUID value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>payments.donation_receipt_id</code>.
     */
    public UUID getDonationReceiptId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>payments.is_active</code>.
     */
    public PaymentsRecord setIsActive(Boolean value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>payments.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(7);
    }

    /**
     * Setter for <code>payments.payment_type</code>.
     */
    public PaymentsRecord setPaymentType(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>payments.payment_type</code>.
     */
    public UUID getPaymentType() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>payments.created_by</code>.
     */
    public PaymentsRecord setCreatedBy(UUID value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>payments.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>payments.updated_by</code>.
     */
    public PaymentsRecord setUpdatedBy(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>payments.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(10);
    }

    /**
     * Setter for <code>payments.created_on</code>.
     */
    public PaymentsRecord setCreatedOn(LocalDateTime value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>payments.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>payments.updated_on</code>.
     */
    public PaymentsRecord setUpdatedOn(LocalDateTime value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>payments.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>payments.razorpay_payment_id</code>.
     */
    public PaymentsRecord setRazorpayPaymentId(String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>payments.razorpay_payment_id</code>.
     */
    public String getRazorpayPaymentId() {
        return (String) get(13);
    }

    /**
     * Setter for <code>payments.failure_description</code>.
     */
    public PaymentsRecord setFailureDescription(String value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>payments.failure_description</code>.
     */
    public String getFailureDescription() {
        return (String) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, BigDecimal, String, String, String, LocalDateTime, UUID, Boolean, UUID, UUID, UUID, LocalDateTime, LocalDateTime, String, String> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    @Override
    public Row15<UUID, BigDecimal, String, String, String, LocalDateTime, UUID, Boolean, UUID, UUID, UUID, LocalDateTime, LocalDateTime, String, String> valuesRow() {
        return (Row15) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Payments.PAYMENTS.ID;
    }

    @Override
    public Field<BigDecimal> field2() {
        return Payments.PAYMENTS.AMOUNT;
    }

    @Override
    public Field<String> field3() {
        return Payments.PAYMENTS.PAYMENT_STATUS;
    }

    @Override
    public Field<String> field4() {
        return Payments.PAYMENTS.RAZORPAY_PAYMENT_METHOD;
    }

    @Override
    public Field<String> field5() {
        return Payments.PAYMENTS.ORDER_ID;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return Payments.PAYMENTS.PAYMENT_DATE;
    }

    @Override
    public Field<UUID> field7() {
        return Payments.PAYMENTS.DONATION_RECEIPT_ID;
    }

    @Override
    public Field<Boolean> field8() {
        return Payments.PAYMENTS.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field9() {
        return Payments.PAYMENTS.PAYMENT_TYPE;
    }

    @Override
    public Field<UUID> field10() {
        return Payments.PAYMENTS.CREATED_BY;
    }

    @Override
    public Field<UUID> field11() {
        return Payments.PAYMENTS.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return Payments.PAYMENTS.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return Payments.PAYMENTS.UPDATED_ON;
    }

    @Override
    public Field<String> field14() {
        return Payments.PAYMENTS.RAZORPAY_PAYMENT_ID;
    }

    @Override
    public Field<String> field15() {
        return Payments.PAYMENTS.FAILURE_DESCRIPTION;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public BigDecimal component2() {
        return getAmount();
    }

    @Override
    public String component3() {
        return getPaymentStatus();
    }

    @Override
    public String component4() {
        return getRazorpayPaymentMethod();
    }

    @Override
    public String component5() {
        return getOrderId();
    }

    @Override
    public LocalDateTime component6() {
        return getPaymentDate();
    }

    @Override
    public UUID component7() {
        return getDonationReceiptId();
    }

    @Override
    public Boolean component8() {
        return getIsActive();
    }

    @Override
    public UUID component9() {
        return getPaymentType();
    }

    @Override
    public UUID component10() {
        return getCreatedBy();
    }

    @Override
    public UUID component11() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component12() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component13() {
        return getUpdatedOn();
    }

    @Override
    public String component14() {
        return getRazorpayPaymentId();
    }

    @Override
    public String component15() {
        return getFailureDescription();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public BigDecimal value2() {
        return getAmount();
    }

    @Override
    public String value3() {
        return getPaymentStatus();
    }

    @Override
    public String value4() {
        return getRazorpayPaymentMethod();
    }

    @Override
    public String value5() {
        return getOrderId();
    }

    @Override
    public LocalDateTime value6() {
        return getPaymentDate();
    }

    @Override
    public UUID value7() {
        return getDonationReceiptId();
    }

    @Override
    public Boolean value8() {
        return getIsActive();
    }

    @Override
    public UUID value9() {
        return getPaymentType();
    }

    @Override
    public UUID value10() {
        return getCreatedBy();
    }

    @Override
    public UUID value11() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value12() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value13() {
        return getUpdatedOn();
    }

    @Override
    public String value14() {
        return getRazorpayPaymentId();
    }

    @Override
    public String value15() {
        return getFailureDescription();
    }

    @Override
    public PaymentsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public PaymentsRecord value2(BigDecimal value) {
        setAmount(value);
        return this;
    }

    @Override
    public PaymentsRecord value3(String value) {
        setPaymentStatus(value);
        return this;
    }

    @Override
    public PaymentsRecord value4(String value) {
        setRazorpayPaymentMethod(value);
        return this;
    }

    @Override
    public PaymentsRecord value5(String value) {
        setOrderId(value);
        return this;
    }

    @Override
    public PaymentsRecord value6(LocalDateTime value) {
        setPaymentDate(value);
        return this;
    }

    @Override
    public PaymentsRecord value7(UUID value) {
        setDonationReceiptId(value);
        return this;
    }

    @Override
    public PaymentsRecord value8(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public PaymentsRecord value9(UUID value) {
        setPaymentType(value);
        return this;
    }

    @Override
    public PaymentsRecord value10(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public PaymentsRecord value11(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public PaymentsRecord value12(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public PaymentsRecord value13(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public PaymentsRecord value14(String value) {
        setRazorpayPaymentId(value);
        return this;
    }

    @Override
    public PaymentsRecord value15(String value) {
        setFailureDescription(value);
        return this;
    }

    @Override
    public PaymentsRecord values(UUID value1, BigDecimal value2, String value3, String value4, String value5, LocalDateTime value6, UUID value7, Boolean value8, UUID value9, UUID value10, UUID value11, LocalDateTime value12, LocalDateTime value13, String value14, String value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PaymentsRecord
     */
    public PaymentsRecord() {
        super(Payments.PAYMENTS);
    }

    /**
     * Create a detached, initialised PaymentsRecord
     */
    public PaymentsRecord(UUID id, BigDecimal amount, String paymentStatus, String razorpayPaymentMethod, String orderId, LocalDateTime paymentDate, UUID donationReceiptId, Boolean isActive, UUID paymentType, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, String razorpayPaymentId, String failureDescription) {
        super(Payments.PAYMENTS);

        setId(id);
        setAmount(amount);
        setPaymentStatus(paymentStatus);
        setRazorpayPaymentMethod(razorpayPaymentMethod);
        setOrderId(orderId);
        setPaymentDate(paymentDate);
        setDonationReceiptId(donationReceiptId);
        setIsActive(isActive);
        setPaymentType(paymentType);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setRazorpayPaymentId(razorpayPaymentId);
        setFailureDescription(failureDescription);
    }
}
