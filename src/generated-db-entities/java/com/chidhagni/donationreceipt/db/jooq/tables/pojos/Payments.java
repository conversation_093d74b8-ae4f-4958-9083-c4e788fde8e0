/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Payments implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private BigDecimal    amount;
    private String        paymentStatus;
    private String        razorpayPaymentMethod;
    private String        orderId;
    private LocalDateTime paymentDate;
    private UUID          donationReceiptId;
    private Boolean       isActive;
    private UUID          paymentType;
    private UUID          createdBy;
    private UUID          updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String        razorpayPaymentId;
    private String        failureDescription;

    public Payments() {}

    public Payments(Payments value) {
        this.id = value.id;
        this.amount = value.amount;
        this.paymentStatus = value.paymentStatus;
        this.razorpayPaymentMethod = value.razorpayPaymentMethod;
        this.orderId = value.orderId;
        this.paymentDate = value.paymentDate;
        this.donationReceiptId = value.donationReceiptId;
        this.isActive = value.isActive;
        this.paymentType = value.paymentType;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.razorpayPaymentId = value.razorpayPaymentId;
        this.failureDescription = value.failureDescription;
    }

    public Payments(
        UUID          id,
        BigDecimal    amount,
        String        paymentStatus,
        String        razorpayPaymentMethod,
        String        orderId,
        LocalDateTime paymentDate,
        UUID          donationReceiptId,
        Boolean       isActive,
        UUID          paymentType,
        UUID          createdBy,
        UUID          updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        String        razorpayPaymentId,
        String        failureDescription
    ) {
        this.id = id;
        this.amount = amount;
        this.paymentStatus = paymentStatus;
        this.razorpayPaymentMethod = razorpayPaymentMethod;
        this.orderId = orderId;
        this.paymentDate = paymentDate;
        this.donationReceiptId = donationReceiptId;
        this.isActive = isActive;
        this.paymentType = paymentType;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.razorpayPaymentId = razorpayPaymentId;
        this.failureDescription = failureDescription;
    }

    /**
     * Getter for <code>payments.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>payments.id</code>.
     */
    public Payments setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>payments.amount</code>.
     */
    public BigDecimal getAmount() {
        return this.amount;
    }

    /**
     * Setter for <code>payments.amount</code>.
     */
    public Payments setAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }

    /**
     * Getter for <code>payments.payment_status</code>.
     */
    public String getPaymentStatus() {
        return this.paymentStatus;
    }

    /**
     * Setter for <code>payments.payment_status</code>.
     */
    public Payments setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
        return this;
    }

    /**
     * Getter for <code>payments.razorpay_payment_method</code>.
     */
    public String getRazorpayPaymentMethod() {
        return this.razorpayPaymentMethod;
    }

    /**
     * Setter for <code>payments.razorpay_payment_method</code>.
     */
    public Payments setRazorpayPaymentMethod(String razorpayPaymentMethod) {
        this.razorpayPaymentMethod = razorpayPaymentMethod;
        return this;
    }

    /**
     * Getter for <code>payments.order_id</code>.
     */
    public String getOrderId() {
        return this.orderId;
    }

    /**
     * Setter for <code>payments.order_id</code>.
     */
    public Payments setOrderId(String orderId) {
        this.orderId = orderId;
        return this;
    }

    /**
     * Getter for <code>payments.payment_date</code>.
     */
    public LocalDateTime getPaymentDate() {
        return this.paymentDate;
    }

    /**
     * Setter for <code>payments.payment_date</code>.
     */
    public Payments setPaymentDate(LocalDateTime paymentDate) {
        this.paymentDate = paymentDate;
        return this;
    }

    /**
     * Getter for <code>payments.donation_receipt_id</code>.
     */
    public UUID getDonationReceiptId() {
        return this.donationReceiptId;
    }

    /**
     * Setter for <code>payments.donation_receipt_id</code>.
     */
    public Payments setDonationReceiptId(UUID donationReceiptId) {
        this.donationReceiptId = donationReceiptId;
        return this;
    }

    /**
     * Getter for <code>payments.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>payments.is_active</code>.
     */
    public Payments setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>payments.payment_type</code>.
     */
    public UUID getPaymentType() {
        return this.paymentType;
    }

    /**
     * Setter for <code>payments.payment_type</code>.
     */
    public Payments setPaymentType(UUID paymentType) {
        this.paymentType = paymentType;
        return this;
    }

    /**
     * Getter for <code>payments.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>payments.created_by</code>.
     */
    public Payments setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>payments.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>payments.updated_by</code>.
     */
    public Payments setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>payments.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>payments.created_on</code>.
     */
    public Payments setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>payments.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>payments.updated_on</code>.
     */
    public Payments setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>payments.razorpay_payment_id</code>.
     */
    public String getRazorpayPaymentId() {
        return this.razorpayPaymentId;
    }

    /**
     * Setter for <code>payments.razorpay_payment_id</code>.
     */
    public Payments setRazorpayPaymentId(String razorpayPaymentId) {
        this.razorpayPaymentId = razorpayPaymentId;
        return this;
    }

    /**
     * Getter for <code>payments.failure_description</code>.
     */
    public String getFailureDescription() {
        return this.failureDescription;
    }

    /**
     * Setter for <code>payments.failure_description</code>.
     */
    public Payments setFailureDescription(String failureDescription) {
        this.failureDescription = failureDescription;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Payments other = (Payments) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (amount == null) {
            if (other.amount != null)
                return false;
        }
        else if (!amount.equals(other.amount))
            return false;
        if (paymentStatus == null) {
            if (other.paymentStatus != null)
                return false;
        }
        else if (!paymentStatus.equals(other.paymentStatus))
            return false;
        if (razorpayPaymentMethod == null) {
            if (other.razorpayPaymentMethod != null)
                return false;
        }
        else if (!razorpayPaymentMethod.equals(other.razorpayPaymentMethod))
            return false;
        if (orderId == null) {
            if (other.orderId != null)
                return false;
        }
        else if (!orderId.equals(other.orderId))
            return false;
        if (paymentDate == null) {
            if (other.paymentDate != null)
                return false;
        }
        else if (!paymentDate.equals(other.paymentDate))
            return false;
        if (donationReceiptId == null) {
            if (other.donationReceiptId != null)
                return false;
        }
        else if (!donationReceiptId.equals(other.donationReceiptId))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (paymentType == null) {
            if (other.paymentType != null)
                return false;
        }
        else if (!paymentType.equals(other.paymentType))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (razorpayPaymentId == null) {
            if (other.razorpayPaymentId != null)
                return false;
        }
        else if (!razorpayPaymentId.equals(other.razorpayPaymentId))
            return false;
        if (failureDescription == null) {
            if (other.failureDescription != null)
                return false;
        }
        else if (!failureDescription.equals(other.failureDescription))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.amount == null) ? 0 : this.amount.hashCode());
        result = prime * result + ((this.paymentStatus == null) ? 0 : this.paymentStatus.hashCode());
        result = prime * result + ((this.razorpayPaymentMethod == null) ? 0 : this.razorpayPaymentMethod.hashCode());
        result = prime * result + ((this.orderId == null) ? 0 : this.orderId.hashCode());
        result = prime * result + ((this.paymentDate == null) ? 0 : this.paymentDate.hashCode());
        result = prime * result + ((this.donationReceiptId == null) ? 0 : this.donationReceiptId.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.paymentType == null) ? 0 : this.paymentType.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.razorpayPaymentId == null) ? 0 : this.razorpayPaymentId.hashCode());
        result = prime * result + ((this.failureDescription == null) ? 0 : this.failureDescription.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Payments (");

        sb.append(id);
        sb.append(", ").append(amount);
        sb.append(", ").append(paymentStatus);
        sb.append(", ").append(razorpayPaymentMethod);
        sb.append(", ").append(orderId);
        sb.append(", ").append(paymentDate);
        sb.append(", ").append(donationReceiptId);
        sb.append(", ").append(isActive);
        sb.append(", ").append(paymentType);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(razorpayPaymentId);
        sb.append(", ").append(failureDescription);

        sb.append(")");
        return sb.toString();
    }
}
