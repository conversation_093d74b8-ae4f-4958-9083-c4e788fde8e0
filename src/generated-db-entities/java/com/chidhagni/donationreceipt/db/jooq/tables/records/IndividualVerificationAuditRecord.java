/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record21;
import org.jooq.Row21;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualVerificationAuditRecord extends UpdatableRecordImpl<IndividualVerificationAuditRecord> implements Record21<UUID, String, String, Boolean, LocalDateTime, LocalDateTime, LocalDateTime, String, UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, IndividualVerificationMetaData, UUID, String, LocalDateTime, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>individual_verification_audit.id</code>.
     */
    public IndividualVerificationAuditRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>individual_verification_audit.contact_type</code>.
     */
    public IndividualVerificationAuditRecord setContactType(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.contact_type</code>.
     */
    public String getContactType() {
        return (String) get(1);
    }

    /**
     * Setter for <code>individual_verification_audit.contact_value</code>.
     */
    public IndividualVerificationAuditRecord setContactValue(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.contact_value</code>.
     */
    public String getContactValue() {
        return (String) get(2);
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link</code>.
     */
    public IndividualVerificationAuditRecord setActivationLink(Boolean value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link</code>.
     */
    public Boolean getActivationLink() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link_created_at</code>.
     */
    public IndividualVerificationAuditRecord setActivationLinkCreatedAt(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link_created_at</code>.
     */
    public LocalDateTime getActivationLinkCreatedAt() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link_expires_at</code>.
     */
    public IndividualVerificationAuditRecord setActivationLinkExpiresAt(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link_expires_at</code>.
     */
    public LocalDateTime getActivationLinkExpiresAt() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link_verified_at</code>.
     */
    public IndividualVerificationAuditRecord setActivationLinkVerifiedAt(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link_verified_at</code>.
     */
    public LocalDateTime getActivationLinkVerifiedAt() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>individual_verification_audit.ip_address</code>.
     */
    public IndividualVerificationAuditRecord setIpAddress(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.ip_address</code>.
     */
    public String getIpAddress() {
        return (String) get(7);
    }

    /**
     * Setter for <code>individual_verification_audit.role_id</code>.
     */
    public IndividualVerificationAuditRecord setRoleId(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.role_id</code>.
     */
    public UUID getRoleId() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>individual_verification_audit.verification_status</code>.
     */
    public IndividualVerificationAuditRecord setVerificationStatus(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.verification_status</code>.
     */
    public String getVerificationStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>individual_verification_audit.is_active</code>.
     */
    public IndividualVerificationAuditRecord setIsActive(Boolean value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(10);
    }

    /**
     * Setter for <code>individual_verification_audit.created_by</code>.
     */
    public IndividualVerificationAuditRecord setCreatedBy(UUID value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(11);
    }

    /**
     * Setter for <code>individual_verification_audit.updated_by</code>.
     */
    public IndividualVerificationAuditRecord setUpdatedBy(UUID value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(12);
    }

    /**
     * Setter for <code>individual_verification_audit.created_on</code>.
     */
    public IndividualVerificationAuditRecord setCreatedOn(LocalDateTime value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(13);
    }

    /**
     * Setter for <code>individual_verification_audit.updated_on</code>.
     */
    public IndividualVerificationAuditRecord setUpdatedOn(LocalDateTime value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(14);
    }

    /**
     * Setter for <code>individual_verification_audit.meta_data</code>.
     */
    public IndividualVerificationAuditRecord setMetaData(IndividualVerificationMetaData value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.meta_data</code>.
     */
    public IndividualVerificationMetaData getMetaData() {
        return (IndividualVerificationMetaData) get(15);
    }

    /**
     * Setter for <code>individual_verification_audit.org_id</code>.
     */
    public IndividualVerificationAuditRecord setOrgId(UUID value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(16);
    }

    /**
     * Setter for <code>individual_verification_audit.otp_code</code>.
     */
    public IndividualVerificationAuditRecord setOtpCode(String value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_code</code>.
     */
    public String getOtpCode() {
        return (String) get(17);
    }

    /**
     * Setter for <code>individual_verification_audit.otp_created_at</code>.
     */
    public IndividualVerificationAuditRecord setOtpCreatedAt(LocalDateTime value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_created_at</code>.
     */
    public LocalDateTime getOtpCreatedAt() {
        return (LocalDateTime) get(18);
    }

    /**
     * Setter for <code>individual_verification_audit.otp_expires_at</code>.
     */
    public IndividualVerificationAuditRecord setOtpExpiresAt(LocalDateTime value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_expires_at</code>.
     */
    public LocalDateTime getOtpExpiresAt() {
        return (LocalDateTime) get(19);
    }

    /**
     * Setter for <code>individual_verification_audit.otp_verified_at</code>.
     */
    public IndividualVerificationAuditRecord setOtpVerifiedAt(LocalDateTime value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_verified_at</code>.
     */
    public LocalDateTime getOtpVerifiedAt() {
        return (LocalDateTime) get(20);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record21 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row21<UUID, String, String, Boolean, LocalDateTime, LocalDateTime, LocalDateTime, String, UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, IndividualVerificationMetaData, UUID, String, LocalDateTime, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row21) super.fieldsRow();
    }

    @Override
    public Row21<UUID, String, String, Boolean, LocalDateTime, LocalDateTime, LocalDateTime, String, UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, IndividualVerificationMetaData, UUID, String, LocalDateTime, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row21) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ID;
    }

    @Override
    public Field<String> field2() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_TYPE;
    }

    @Override
    public Field<String> field3() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_VALUE;
    }

    @Override
    public Field<Boolean> field4() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_CREATED_AT;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_EXPIRES_AT;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_VERIFIED_AT;
    }

    @Override
    public Field<String> field8() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.IP_ADDRESS;
    }

    @Override
    public Field<UUID> field9() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ROLE_ID;
    }

    @Override
    public Field<String> field10() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS;
    }

    @Override
    public Field<Boolean> field11() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field12() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CREATED_BY;
    }

    @Override
    public Field<UUID> field13() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field14() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field15() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.UPDATED_ON;
    }

    @Override
    public Field<IndividualVerificationMetaData> field16() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.META_DATA;
    }

    @Override
    public Field<UUID> field17() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID;
    }

    @Override
    public Field<String> field18() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_CODE;
    }

    @Override
    public Field<LocalDateTime> field19() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_CREATED_AT;
    }

    @Override
    public Field<LocalDateTime> field20() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_EXPIRES_AT;
    }

    @Override
    public Field<LocalDateTime> field21() {
        return IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_VERIFIED_AT;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getContactType();
    }

    @Override
    public String component3() {
        return getContactValue();
    }

    @Override
    public Boolean component4() {
        return getActivationLink();
    }

    @Override
    public LocalDateTime component5() {
        return getActivationLinkCreatedAt();
    }

    @Override
    public LocalDateTime component6() {
        return getActivationLinkExpiresAt();
    }

    @Override
    public LocalDateTime component7() {
        return getActivationLinkVerifiedAt();
    }

    @Override
    public String component8() {
        return getIpAddress();
    }

    @Override
    public UUID component9() {
        return getRoleId();
    }

    @Override
    public String component10() {
        return getVerificationStatus();
    }

    @Override
    public Boolean component11() {
        return getIsActive();
    }

    @Override
    public UUID component12() {
        return getCreatedBy();
    }

    @Override
    public UUID component13() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component14() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component15() {
        return getUpdatedOn();
    }

    @Override
    public IndividualVerificationMetaData component16() {
        return getMetaData();
    }

    @Override
    public UUID component17() {
        return getOrgId();
    }

    @Override
    public String component18() {
        return getOtpCode();
    }

    @Override
    public LocalDateTime component19() {
        return getOtpCreatedAt();
    }

    @Override
    public LocalDateTime component20() {
        return getOtpExpiresAt();
    }

    @Override
    public LocalDateTime component21() {
        return getOtpVerifiedAt();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getContactType();
    }

    @Override
    public String value3() {
        return getContactValue();
    }

    @Override
    public Boolean value4() {
        return getActivationLink();
    }

    @Override
    public LocalDateTime value5() {
        return getActivationLinkCreatedAt();
    }

    @Override
    public LocalDateTime value6() {
        return getActivationLinkExpiresAt();
    }

    @Override
    public LocalDateTime value7() {
        return getActivationLinkVerifiedAt();
    }

    @Override
    public String value8() {
        return getIpAddress();
    }

    @Override
    public UUID value9() {
        return getRoleId();
    }

    @Override
    public String value10() {
        return getVerificationStatus();
    }

    @Override
    public Boolean value11() {
        return getIsActive();
    }

    @Override
    public UUID value12() {
        return getCreatedBy();
    }

    @Override
    public UUID value13() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value14() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value15() {
        return getUpdatedOn();
    }

    @Override
    public IndividualVerificationMetaData value16() {
        return getMetaData();
    }

    @Override
    public UUID value17() {
        return getOrgId();
    }

    @Override
    public String value18() {
        return getOtpCode();
    }

    @Override
    public LocalDateTime value19() {
        return getOtpCreatedAt();
    }

    @Override
    public LocalDateTime value20() {
        return getOtpExpiresAt();
    }

    @Override
    public LocalDateTime value21() {
        return getOtpVerifiedAt();
    }

    @Override
    public IndividualVerificationAuditRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value2(String value) {
        setContactType(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value3(String value) {
        setContactValue(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value4(Boolean value) {
        setActivationLink(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value5(LocalDateTime value) {
        setActivationLinkCreatedAt(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value6(LocalDateTime value) {
        setActivationLinkExpiresAt(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value7(LocalDateTime value) {
        setActivationLinkVerifiedAt(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value8(String value) {
        setIpAddress(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value9(UUID value) {
        setRoleId(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value10(String value) {
        setVerificationStatus(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value11(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value12(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value13(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value14(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value15(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value16(IndividualVerificationMetaData value) {
        setMetaData(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value17(UUID value) {
        setOrgId(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value18(String value) {
        setOtpCode(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value19(LocalDateTime value) {
        setOtpCreatedAt(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value20(LocalDateTime value) {
        setOtpExpiresAt(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord value21(LocalDateTime value) {
        setOtpVerifiedAt(value);
        return this;
    }

    @Override
    public IndividualVerificationAuditRecord values(UUID value1, String value2, String value3, Boolean value4, LocalDateTime value5, LocalDateTime value6, LocalDateTime value7, String value8, UUID value9, String value10, Boolean value11, UUID value12, UUID value13, LocalDateTime value14, LocalDateTime value15, IndividualVerificationMetaData value16, UUID value17, String value18, LocalDateTime value19, LocalDateTime value20, LocalDateTime value21) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        value21(value21);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IndividualVerificationAuditRecord
     */
    public IndividualVerificationAuditRecord() {
        super(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT);
    }

    /**
     * Create a detached, initialised IndividualVerificationAuditRecord
     */
    public IndividualVerificationAuditRecord(UUID id, String contactType, String contactValue, Boolean activationLink, LocalDateTime activationLinkCreatedAt, LocalDateTime activationLinkExpiresAt, LocalDateTime activationLinkVerifiedAt, String ipAddress, UUID roleId, String verificationStatus, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, IndividualVerificationMetaData metaData, UUID orgId, String otpCode, LocalDateTime otpCreatedAt, LocalDateTime otpExpiresAt, LocalDateTime otpVerifiedAt) {
        super(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT);

        setId(id);
        setContactType(contactType);
        setContactValue(contactValue);
        setActivationLink(activationLink);
        setActivationLinkCreatedAt(activationLinkCreatedAt);
        setActivationLinkExpiresAt(activationLinkExpiresAt);
        setActivationLinkVerifiedAt(activationLinkVerifiedAt);
        setIpAddress(ipAddress);
        setRoleId(roleId);
        setVerificationStatus(verificationStatus);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setMetaData(metaData);
        setOrgId(orgId);
        setOtpCode(otpCode);
        setOtpCreatedAt(otpCreatedAt);
        setOtpExpiresAt(otpExpiresAt);
        setOtpVerifiedAt(otpVerifiedAt);
    }
}
