/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualRecord;
import com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class IndividualDao extends DAOImpl<IndividualRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual, UUID> {

    /**
     * Create a new IndividualDao without any configuration
     */
    public IndividualDao() {
        super(Individual.INDIVIDUAL, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual.class);
    }

    /**
     * Create a new IndividualDao with an attached configuration
     */
    @Autowired
    public IndividualDao(Configuration configuration) {
        super(Individual.INDIVIDUAL, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchById(UUID... values) {
        return fetch(Individual.INDIVIDUAL.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual fetchOneById(UUID value) {
        return fetchOne(Individual.INDIVIDUAL.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByName(String... values) {
        return fetch(Individual.INDIVIDUAL.NAME, values);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByEmail(String... values) {
        return fetch(Individual.INDIVIDUAL.EMAIL, values);
    }

    /**
     * Fetch a unique record that has <code>email = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual fetchOneByEmail(String value) {
        return fetchOne(Individual.INDIVIDUAL.EMAIL, value);
    }

    /**
     * Fetch records that have <code>mobile_number BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfMobileNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.MOBILE_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mobile_number IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByMobileNumber(String... values) {
        return fetch(Individual.INDIVIDUAL.MOBILE_NUMBER, values);
    }

    /**
     * Fetch records that have <code>password BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfPassword(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.PASSWORD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>password IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByPassword(String... values) {
        return fetch(Individual.INDIVIDUAL.PASSWORD, values);
    }

    /**
     * Fetch records that have <code>social_login_provider BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfSocialLoginProvider(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_login_provider IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchBySocialLoginProvider(String... values) {
        return fetch(Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER, values);
    }

    /**
     * Fetch records that have <code>social_login_provider_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfSocialLoginProviderId(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_login_provider_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchBySocialLoginProviderId(String... values) {
        return fetch(Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER_ID, values);
    }

    /**
     * Fetch records that have <code>social_login_provider_image_url BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfSocialLoginProviderImageUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER_IMAGE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_login_provider_image_url IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchBySocialLoginProviderImageUrl(String... values) {
        return fetch(Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER_IMAGE_URL, values);
    }

    /**
     * Fetch records that have <code>meta_data BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfMetaData(IndividualMetaDataDTO lowerInclusive, IndividualMetaDataDTO upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.META_DATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>meta_data IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByMetaData(IndividualMetaDataDTO... values) {
        return fetch(Individual.INDIVIDUAL.META_DATA, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByIsActive(Boolean... values) {
        return fetch(Individual.INDIVIDUAL.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByCreatedBy(UUID... values) {
        return fetch(Individual.INDIVIDUAL.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByUpdatedBy(UUID... values) {
        return fetch(Individual.INDIVIDUAL.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Individual.INDIVIDUAL.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Individual.INDIVIDUAL.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Individual.INDIVIDUAL.UPDATED_ON, values);
    }
}
