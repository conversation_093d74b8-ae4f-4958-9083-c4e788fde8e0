/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonationHeadsRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DonationHeadsDao extends DAOImpl<DonationHeadsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads, UUID> {

    /**
     * Create a new DonationHeadsDao without any configuration
     */
    public DonationHeadsDao() {
        super(DonationHeads.DONATION_HEADS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads.class);
    }

    /**
     * Create a new DonationHeadsDao with an attached configuration
     */
    @Autowired
    public DonationHeadsDao(Configuration configuration) {
        super(DonationHeads.DONATION_HEADS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchById(UUID... values) {
        return fetch(DonationHeads.DONATION_HEADS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads fetchOneById(UUID value) {
        return fetchOne(DonationHeads.DONATION_HEADS.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByName(String... values) {
        return fetch(DonationHeads.DONATION_HEADS.NAME, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByDescription(String... values) {
        return fetch(DonationHeads.DONATION_HEADS.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByOrgId(UUID... values) {
        return fetch(DonationHeads.DONATION_HEADS.ORG_ID, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(DonationHeads.DONATION_HEADS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByCreatedBy(UUID... values) {
        return fetch(DonationHeads.DONATION_HEADS.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(DonationHeads.DONATION_HEADS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByUpdatedBy(UUID... values) {
        return fetch(DonationHeads.DONATION_HEADS.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(DonationHeads.DONATION_HEADS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads> fetchByIsActive(Boolean... values) {
        return fetch(DonationHeads.DONATION_HEADS.IS_ACTIVE, values);
    }
}
