/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq;


import com.chidhagni.donationreceipt.db.jooq.tables.ContactUs;
import com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging;
import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualSessions;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.ListNames;
import com.chidhagni.donationreceipt.db.jooq.tables.ListValues;
import com.chidhagni.donationreceipt.db.jooq.tables.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.Payments;
import com.chidhagni.donationreceipt.db.jooq.tables.Resource;
import com.chidhagni.donationreceipt.db.jooq.tables.Roles;


/**
 * Convenience access to all tables in the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>contact_us</code>.
     */
    public static final ContactUs CONTACT_US = ContactUs.CONTACT_US;

    /**
     * The table <code>document_repo</code>.
     */
    public static final DocumentRepo DOCUMENT_REPO = DocumentRepo.DOCUMENT_REPO;

    /**
     * The table <code>donation_heads</code>.
     */
    public static final DonationHeads DONATION_HEADS = DonationHeads.DONATION_HEADS;

    /**
     * The table <code>donation_receipts</code>.
     */
    public static final DonationReceipts DONATION_RECEIPTS = DonationReceipts.DONATION_RECEIPTS;

    /**
     * The table <code>donor_group_mapping</code>.
     */
    public static final DonorGroupMapping DONOR_GROUP_MAPPING = DonorGroupMapping.DONOR_GROUP_MAPPING;

    /**
     * The table <code>donor_groups</code>.
     */
    public static final DonorGroups DONOR_GROUPS = DonorGroups.DONOR_GROUPS;

    /**
     * The table <code>donors</code>.
     */
    public static final Donors DONORS = Donors.DONORS;

    /**
     * The table <code>import_batch</code>.
     */
    public static final ImportBatch IMPORT_BATCH = ImportBatch.IMPORT_BATCH;

    /**
     * The table <code>import_staging</code>.
     */
    public static final ImportStaging IMPORT_STAGING = ImportStaging.IMPORT_STAGING;

    /**
     * The table <code>individual</code>.
     */
    public static final Individual INDIVIDUAL = Individual.INDIVIDUAL;

    /**
     * The table <code>individual_password_reset_audit</code>.
     */
    public static final IndividualPasswordResetAudit INDIVIDUAL_PASSWORD_RESET_AUDIT = IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT;

    /**
     * The table <code>individual_permission</code>.
     */
    public static final IndividualPermission INDIVIDUAL_PERMISSION = IndividualPermission.INDIVIDUAL_PERMISSION;

    /**
     * The table <code>individual_role</code>.
     */
    public static final IndividualRole INDIVIDUAL_ROLE = IndividualRole.INDIVIDUAL_ROLE;

    /**
     * The table <code>individual_sessions</code>.
     */
    public static final IndividualSessions INDIVIDUAL_SESSIONS = IndividualSessions.INDIVIDUAL_SESSIONS;

    /**
     * The table <code>individual_verification_audit</code>.
     */
    public static final IndividualVerificationAudit INDIVIDUAL_VERIFICATION_AUDIT = IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT;

    /**
     * The table <code>list_names</code>.
     */
    public static final ListNames LIST_NAMES = ListNames.LIST_NAMES;

    /**
     * The table <code>list_values</code>.
     */
    public static final ListValues LIST_VALUES = ListValues.LIST_VALUES;

    /**
     * The table <code>organisation</code>.
     */
    public static final Organisation ORGANISATION = Organisation.ORGANISATION;

    /**
     * The table <code>payments</code>.
     */
    public static final Payments PAYMENTS = Payments.PAYMENTS;

    /**
     * The table <code>resource</code>.
     */
    public static final Resource RESOURCE = Resource.RESOURCE;

    /**
     * The table <code>roles</code>.
     */
    public static final Roles ROLES = Roles.ROLES;
}
