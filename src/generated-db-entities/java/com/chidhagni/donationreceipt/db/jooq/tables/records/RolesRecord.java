/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.Roles;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RolesRecord extends UpdatableRecordImpl<RolesRecord> implements Record11<UUID, String, String, UUID, Boolean, LocalDateTime, LocalDateTime, UUID, UUID, List<RoleNode>, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>roles.id</code>.
     */
    public RolesRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>roles.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>roles.name</code>.
     */
    public RolesRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>roles.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>roles.description</code>.
     */
    public RolesRecord setDescription(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>roles.description</code>.
     */
    public String getDescription() {
        return (String) get(2);
    }

    /**
     * Setter for <code>roles.parent_role_id</code>.
     */
    public RolesRecord setParentRoleId(UUID value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>roles.parent_role_id</code>.
     */
    public UUID getParentRoleId() {
        return (UUID) get(3);
    }

    /**
     * Setter for <code>roles.is_active</code>.
     */
    public RolesRecord setIsActive(Boolean value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>roles.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(4);
    }

    /**
     * Setter for <code>roles.created_on</code>.
     */
    public RolesRecord setCreatedOn(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>roles.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>roles.updated_on</code>.
     */
    public RolesRecord setUpdatedOn(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>roles.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>roles.created_by</code>.
     */
    public RolesRecord setCreatedBy(UUID value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>roles.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>roles.updated_by</code>.
     */
    public RolesRecord setUpdatedBy(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>roles.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>roles.permissions</code>.
     */
    public RolesRecord setPermissions(List<RoleNode> value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>roles.permissions</code>.
     */
    public List<RoleNode> getPermissions() {
        return (List<RoleNode>) get(9);
    }

    /**
     * Setter for <code>roles.org_id</code>.
     */
    public RolesRecord setOrgId(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>roles.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, String, String, UUID, Boolean, LocalDateTime, LocalDateTime, UUID, UUID, List<RoleNode>, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<UUID, String, String, UUID, Boolean, LocalDateTime, LocalDateTime, UUID, UUID, List<RoleNode>, UUID> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Roles.ROLES.ID;
    }

    @Override
    public Field<String> field2() {
        return Roles.ROLES.NAME;
    }

    @Override
    public Field<String> field3() {
        return Roles.ROLES.DESCRIPTION;
    }

    @Override
    public Field<UUID> field4() {
        return Roles.ROLES.PARENT_ROLE_ID;
    }

    @Override
    public Field<Boolean> field5() {
        return Roles.ROLES.IS_ACTIVE;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return Roles.ROLES.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return Roles.ROLES.UPDATED_ON;
    }

    @Override
    public Field<UUID> field8() {
        return Roles.ROLES.CREATED_BY;
    }

    @Override
    public Field<UUID> field9() {
        return Roles.ROLES.UPDATED_BY;
    }

    @Override
    public Field<List<RoleNode>> field10() {
        return Roles.ROLES.PERMISSIONS;
    }

    @Override
    public Field<UUID> field11() {
        return Roles.ROLES.ORG_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getDescription();
    }

    @Override
    public UUID component4() {
        return getParentRoleId();
    }

    @Override
    public Boolean component5() {
        return getIsActive();
    }

    @Override
    public LocalDateTime component6() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component7() {
        return getUpdatedOn();
    }

    @Override
    public UUID component8() {
        return getCreatedBy();
    }

    @Override
    public UUID component9() {
        return getUpdatedBy();
    }

    @Override
    public List<RoleNode> component10() {
        return getPermissions();
    }

    @Override
    public UUID component11() {
        return getOrgId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getDescription();
    }

    @Override
    public UUID value4() {
        return getParentRoleId();
    }

    @Override
    public Boolean value5() {
        return getIsActive();
    }

    @Override
    public LocalDateTime value6() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value7() {
        return getUpdatedOn();
    }

    @Override
    public UUID value8() {
        return getCreatedBy();
    }

    @Override
    public UUID value9() {
        return getUpdatedBy();
    }

    @Override
    public List<RoleNode> value10() {
        return getPermissions();
    }

    @Override
    public UUID value11() {
        return getOrgId();
    }

    @Override
    public RolesRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public RolesRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public RolesRecord value3(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public RolesRecord value4(UUID value) {
        setParentRoleId(value);
        return this;
    }

    @Override
    public RolesRecord value5(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public RolesRecord value6(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public RolesRecord value7(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public RolesRecord value8(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public RolesRecord value9(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public RolesRecord value10(List<RoleNode> value) {
        setPermissions(value);
        return this;
    }

    @Override
    public RolesRecord value11(UUID value) {
        setOrgId(value);
        return this;
    }

    @Override
    public RolesRecord values(UUID value1, String value2, String value3, UUID value4, Boolean value5, LocalDateTime value6, LocalDateTime value7, UUID value8, UUID value9, List<RoleNode> value10, UUID value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached RolesRecord
     */
    public RolesRecord() {
        super(Roles.ROLES);
    }

    /**
     * Create a detached, initialised RolesRecord
     */
    public RolesRecord(UUID id, String name, String description, UUID parentRoleId, Boolean isActive, LocalDateTime createdOn, LocalDateTime updatedOn, UUID createdBy, UUID updatedBy, List<RoleNode> permissions, UUID orgId) {
        super(Roles.ROLES);

        setId(id);
        setName(name);
        setDescription(description);
        setParentRoleId(parentRoleId);
        setIsActive(isActive);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setPermissions(permissions);
        setOrgId(orgId);
    }
}
