/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportBatchRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ImportBatchDao extends DAOImpl<ImportBatchRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch, UUID> {

    /**
     * Create a new ImportBatchDao without any configuration
     */
    public ImportBatchDao() {
        super(ImportBatch.IMPORT_BATCH, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch.class);
    }

    /**
     * Create a new ImportBatchDao with an attached configuration
     */
    @Autowired
    public ImportBatchDao(Configuration configuration) {
        super(ImportBatch.IMPORT_BATCH, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchById(UUID... values) {
        return fetch(ImportBatch.IMPORT_BATCH.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch fetchOneById(UUID value) {
        return fetchOne(ImportBatch.IMPORT_BATCH.ID, value);
    }

    /**
     * Fetch records that have <code>tenant_org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfTenantOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.TENANT_ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tenant_org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByTenantOrgId(UUID... values) {
        return fetch(ImportBatch.IMPORT_BATCH.TENANT_ORG_ID, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByCategory(String... values) {
        return fetch(ImportBatch.IMPORT_BATCH.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>file_name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfFileName(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.FILE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>file_name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByFileName(String... values) {
        return fetch(ImportBatch.IMPORT_BATCH.FILE_NAME, values);
    }

    /**
     * Fetch records that have <code>total_records BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfTotalRecords(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.TOTAL_RECORDS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>total_records IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByTotalRecords(Integer... values) {
        return fetch(ImportBatch.IMPORT_BATCH.TOTAL_RECORDS, values);
    }

    /**
     * Fetch records that have <code>processed_records BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfProcessedRecords(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.PROCESSED_RECORDS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>processed_records IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByProcessedRecords(Integer... values) {
        return fetch(ImportBatch.IMPORT_BATCH.PROCESSED_RECORDS, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByStatus(String... values) {
        return fetch(ImportBatch.IMPORT_BATCH.STATUS, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(ImportBatch.IMPORT_BATCH.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(ImportBatch.IMPORT_BATCH.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByCreatedBy(UUID... values) {
        return fetch(ImportBatch.IMPORT_BATCH.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByUpdatedBy(UUID... values) {
        return fetch(ImportBatch.IMPORT_BATCH.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(ImportBatch.IMPORT_BATCH.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportBatch> fetchByIsActive(Boolean... values) {
        return fetch(ImportBatch.IMPORT_BATCH.IS_ACTIVE, values);
    }
}
