/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorGroupMappingRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DonorGroupMappingDao extends DAOImpl<DonorGroupMappingRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping, UUID> {

    /**
     * Create a new DonorGroupMappingDao without any configuration
     */
    public DonorGroupMappingDao() {
        super(DonorGroupMapping.DONOR_GROUP_MAPPING, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping.class);
    }

    /**
     * Create a new DonorGroupMappingDao with an attached configuration
     */
    @Autowired
    public DonorGroupMappingDao(Configuration configuration) {
        super(DonorGroupMapping.DONOR_GROUP_MAPPING, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchById(UUID... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping fetchOneById(UUID value) {
        return fetchOne(DonorGroupMapping.DONOR_GROUP_MAPPING.ID, value);
    }

    /**
     * Fetch records that have <code>donor_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfDonorId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.DONOR_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>donor_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByDonorId(UUID... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.DONOR_ID, values);
    }

    /**
     * Fetch records that have <code>group_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfGroupId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.GROUP_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>group_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByGroupId(UUID... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.GROUP_ID, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByCreatedBy(UUID... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByUpdatedBy(UUID... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(DonorGroupMapping.DONOR_GROUP_MAPPING.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping> fetchByIsActive(Boolean... values) {
        return fetch(DonorGroupMapping.DONOR_GROUP_MAPPING.IS_ACTIVE, values);
    }
}
