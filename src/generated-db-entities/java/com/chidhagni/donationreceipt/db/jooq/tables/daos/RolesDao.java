/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.Roles;
import com.chidhagni.donationreceipt.db.jooq.tables.records.RolesRecord;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class RolesDao extends DAOImpl<RolesRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles, UUID> {

    /**
     * Create a new RolesDao without any configuration
     */
    public RolesDao() {
        super(Roles.ROLES, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles.class);
    }

    /**
     * Create a new RolesDao with an attached configuration
     */
    @Autowired
    public RolesDao(Configuration configuration) {
        super(Roles.ROLES, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Roles.ROLES.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchById(UUID... values) {
        return fetch(Roles.ROLES.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles fetchOneById(UUID value) {
        return fetchOne(Roles.ROLES.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Roles.ROLES.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByName(String... values) {
        return fetch(Roles.ROLES.NAME, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(Roles.ROLES.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByDescription(String... values) {
        return fetch(Roles.ROLES.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>parent_role_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfParentRoleId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Roles.ROLES.PARENT_ROLE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>parent_role_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByParentRoleId(UUID... values) {
        return fetch(Roles.ROLES.PARENT_ROLE_ID, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Roles.ROLES.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByIsActive(Boolean... values) {
        return fetch(Roles.ROLES.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Roles.ROLES.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Roles.ROLES.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Roles.ROLES.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Roles.ROLES.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Roles.ROLES.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByCreatedBy(UUID... values) {
        return fetch(Roles.ROLES.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Roles.ROLES.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByUpdatedBy(UUID... values) {
        return fetch(Roles.ROLES.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>permissions BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfPermissions(List<RoleNode> lowerInclusive, List<RoleNode> upperInclusive) {
        return fetchRange(Roles.ROLES.PERMISSIONS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>permissions IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByPermissions(List<RoleNode>... values) {
        return fetch(Roles.ROLES.PERMISSIONS, values);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Roles.ROLES.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles> fetchByOrgId(UUID... values) {
        return fetch(Roles.ROLES.ORG_ID, values);
    }
}
