/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DocumentRepoRecord;
import com.chidhagni.donationreceipt.documentrepo.dto.request.TagsDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DocumentRepoDao extends DAOImpl<DocumentRepoRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo, UUID> {

    /**
     * Create a new DocumentRepoDao without any configuration
     */
    public DocumentRepoDao() {
        super(DocumentRepo.DOCUMENT_REPO, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo.class);
    }

    /**
     * Create a new DocumentRepoDao with an attached configuration
     */
    @Autowired
    public DocumentRepoDao(Configuration configuration) {
        super(DocumentRepo.DOCUMENT_REPO, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchById(UUID... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo fetchOneById(UUID value) {
        return fetchOne(DocumentRepo.DOCUMENT_REPO.ID, value);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfCategory(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByCategory(UUID... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>sub_category BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfSubCategory(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.SUB_CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sub_category IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchBySubCategory(UUID... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.SUB_CATEGORY, values);
    }

    /**
     * Fetch records that have <code>sender BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfSender(com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO lowerInclusive, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.SENDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>sender IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchBySender(com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.SENDER, values);
    }

    /**
     * Fetch records that have <code>recipients BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfRecipients(List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> lowerInclusive, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.RECIPIENTS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>recipients IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByRecipients(List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.RECIPIENTS, values);
    }

    /**
     * Fetch records that have <code>path BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfPath(String lowerInclusive, String upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.PATH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>path IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByPath(String... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.PATH, values);
    }

    /**
     * Fetch records that have <code>file_date BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfFileDate(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.FILE_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>file_date IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByFileDate(LocalDateTime... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.FILE_DATE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByCreatedBy(UUID... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByUpdatedBy(UUID... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>remarks BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfRemarks(String lowerInclusive, String upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.REMARKS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>remarks IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByRemarks(String... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.REMARKS, values);
    }

    /**
     * Fetch records that have <code>tags BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfTags(TagsDTO lowerInclusive, TagsDTO upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.TAGS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tags IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByTags(TagsDTO... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.TAGS, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(DocumentRepo.DOCUMENT_REPO.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DocumentRepo> fetchByIsActive(Boolean... values) {
        return fetch(DocumentRepo.DOCUMENT_REPO.IS_ACTIVE, values);
    }
}
