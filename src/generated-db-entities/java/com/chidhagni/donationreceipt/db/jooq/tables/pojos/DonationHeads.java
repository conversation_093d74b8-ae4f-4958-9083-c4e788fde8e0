/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonationHeads implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        name;
    private String        description;
    private UUID          orgId;
    private LocalDateTime createdOn;
    private UUID          createdBy;
    private LocalDateTime updatedOn;
    private UUID          updatedBy;
    private Boolean       isActive;

    public DonationHeads() {}

    public DonationHeads(DonationHeads value) {
        this.id = value.id;
        this.name = value.name;
        this.description = value.description;
        this.orgId = value.orgId;
        this.createdOn = value.createdOn;
        this.createdBy = value.createdBy;
        this.updatedOn = value.updatedOn;
        this.updatedBy = value.updatedBy;
        this.isActive = value.isActive;
    }

    public DonationHeads(
        UUID          id,
        String        name,
        String        description,
        UUID          orgId,
        LocalDateTime createdOn,
        UUID          createdBy,
        LocalDateTime updatedOn,
        UUID          updatedBy,
        Boolean       isActive
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.orgId = orgId;
        this.createdOn = createdOn;
        this.createdBy = createdBy;
        this.updatedOn = updatedOn;
        this.updatedBy = updatedBy;
        this.isActive = isActive;
    }

    /**
     * Getter for <code>donation_heads.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>donation_heads.id</code>.
     */
    public DonationHeads setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>donation_heads.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>donation_heads.name</code>.
     */
    public DonationHeads setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>donation_heads.description</code>.
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>donation_heads.description</code>.
     */
    public DonationHeads setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>donation_heads.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>donation_heads.org_id</code>.
     */
    public DonationHeads setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    /**
     * Getter for <code>donation_heads.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>donation_heads.created_on</code>.
     */
    public DonationHeads setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>donation_heads.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>donation_heads.created_by</code>.
     */
    public DonationHeads setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>donation_heads.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>donation_heads.updated_on</code>.
     */
    public DonationHeads setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>donation_heads.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>donation_heads.updated_by</code>.
     */
    public DonationHeads setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>donation_heads.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>donation_heads.is_active</code>.
     */
    public DonationHeads setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final DonationHeads other = (DonationHeads) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        }
        else if (!description.equals(other.description))
            return false;
        if (orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!orgId.equals(other.orgId))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DonationHeads (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(orgId);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(isActive);

        sb.append(")");
        return sb.toString();
    }
}
