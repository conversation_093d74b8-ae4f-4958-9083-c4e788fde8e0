/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.organisation.MetaDataDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Organisation implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        name;
    private String        category;
    private MetaDataDTO   metaData;
    private Boolean       isActive;
    private UUID          createdBy;
    private UUID          updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;

    public Organisation() {}

    public Organisation(Organisation value) {
        this.id = value.id;
        this.name = value.name;
        this.category = value.category;
        this.metaData = value.metaData;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
    }

    public Organisation(
        UUID          id,
        String        name,
        String        category,
        MetaDataDTO   metaData,
        Boolean       isActive,
        UUID          createdBy,
        UUID          updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn
    ) {
        this.id = id;
        this.name = name;
        this.category = category;
        this.metaData = metaData;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
    }

    /**
     * Getter for <code>organisation.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>organisation.id</code>.
     */
    public Organisation setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>organisation.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>organisation.name</code>.
     */
    public Organisation setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>organisation.category</code>.
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>organisation.category</code>.
     */
    public Organisation setCategory(String category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>organisation.meta_data</code>.
     */
    public MetaDataDTO getMetaData() {
        return this.metaData;
    }

    /**
     * Setter for <code>organisation.meta_data</code>.
     */
    public Organisation setMetaData(MetaDataDTO metaData) {
        this.metaData = metaData;
        return this;
    }

    /**
     * Getter for <code>organisation.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>organisation.is_active</code>.
     */
    public Organisation setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>organisation.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>organisation.created_by</code>.
     */
    public Organisation setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>organisation.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>organisation.updated_by</code>.
     */
    public Organisation setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>organisation.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>organisation.created_on</code>.
     */
    public Organisation setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>organisation.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>organisation.updated_on</code>.
     */
    public Organisation setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Organisation other = (Organisation) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (category == null) {
            if (other.category != null)
                return false;
        }
        else if (!category.equals(other.category))
            return false;
        if (metaData == null) {
            if (other.metaData != null)
                return false;
        }
        else if (!metaData.equals(other.metaData))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.metaData == null) ? 0 : this.metaData.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Organisation (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(category);
        sb.append(", ").append(metaData);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);

        sb.append(")");
        return sb.toString();
    }
}
