/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualSessions implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          individualId;
    private String        accesstoken;
    private LocalDateTime accesstokenExpirytime;
    private LocalDateTime accesstokenGeneratedOn;
    private String        refreshtoken;
    private LocalDateTime refreshtokenExpirytime;
    private LocalDateTime refreshtokenGeneratedOn;
    private String        ipaddress;

    public IndividualSessions() {}

    public IndividualSessions(IndividualSessions value) {
        this.id = value.id;
        this.individualId = value.individualId;
        this.accesstoken = value.accesstoken;
        this.accesstokenExpirytime = value.accesstokenExpirytime;
        this.accesstokenGeneratedOn = value.accesstokenGeneratedOn;
        this.refreshtoken = value.refreshtoken;
        this.refreshtokenExpirytime = value.refreshtokenExpirytime;
        this.refreshtokenGeneratedOn = value.refreshtokenGeneratedOn;
        this.ipaddress = value.ipaddress;
    }

    public IndividualSessions(
        UUID          id,
        UUID          individualId,
        String        accesstoken,
        LocalDateTime accesstokenExpirytime,
        LocalDateTime accesstokenGeneratedOn,
        String        refreshtoken,
        LocalDateTime refreshtokenExpirytime,
        LocalDateTime refreshtokenGeneratedOn,
        String        ipaddress
    ) {
        this.id = id;
        this.individualId = individualId;
        this.accesstoken = accesstoken;
        this.accesstokenExpirytime = accesstokenExpirytime;
        this.accesstokenGeneratedOn = accesstokenGeneratedOn;
        this.refreshtoken = refreshtoken;
        this.refreshtokenExpirytime = refreshtokenExpirytime;
        this.refreshtokenGeneratedOn = refreshtokenGeneratedOn;
        this.ipaddress = ipaddress;
    }

    /**
     * Getter for <code>individual_sessions.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>individual_sessions.id</code>.
     */
    public IndividualSessions setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.individual_id</code>.
     */
    public UUID getIndividualId() {
        return this.individualId;
    }

    /**
     * Setter for <code>individual_sessions.individual_id</code>.
     */
    public IndividualSessions setIndividualId(UUID individualId) {
        this.individualId = individualId;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.accesstoken</code>.
     */
    public String getAccesstoken() {
        return this.accesstoken;
    }

    /**
     * Setter for <code>individual_sessions.accesstoken</code>.
     */
    public IndividualSessions setAccesstoken(String accesstoken) {
        this.accesstoken = accesstoken;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.accesstoken_expirytime</code>.
     */
    public LocalDateTime getAccesstokenExpirytime() {
        return this.accesstokenExpirytime;
    }

    /**
     * Setter for <code>individual_sessions.accesstoken_expirytime</code>.
     */
    public IndividualSessions setAccesstokenExpirytime(LocalDateTime accesstokenExpirytime) {
        this.accesstokenExpirytime = accesstokenExpirytime;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.accesstoken_generated_on</code>.
     */
    public LocalDateTime getAccesstokenGeneratedOn() {
        return this.accesstokenGeneratedOn;
    }

    /**
     * Setter for <code>individual_sessions.accesstoken_generated_on</code>.
     */
    public IndividualSessions setAccesstokenGeneratedOn(LocalDateTime accesstokenGeneratedOn) {
        this.accesstokenGeneratedOn = accesstokenGeneratedOn;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.refreshtoken</code>.
     */
    public String getRefreshtoken() {
        return this.refreshtoken;
    }

    /**
     * Setter for <code>individual_sessions.refreshtoken</code>.
     */
    public IndividualSessions setRefreshtoken(String refreshtoken) {
        this.refreshtoken = refreshtoken;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.refreshtoken_expirytime</code>.
     */
    public LocalDateTime getRefreshtokenExpirytime() {
        return this.refreshtokenExpirytime;
    }

    /**
     * Setter for <code>individual_sessions.refreshtoken_expirytime</code>.
     */
    public IndividualSessions setRefreshtokenExpirytime(LocalDateTime refreshtokenExpirytime) {
        this.refreshtokenExpirytime = refreshtokenExpirytime;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.refreshtoken_generated_on</code>.
     */
    public LocalDateTime getRefreshtokenGeneratedOn() {
        return this.refreshtokenGeneratedOn;
    }

    /**
     * Setter for <code>individual_sessions.refreshtoken_generated_on</code>.
     */
    public IndividualSessions setRefreshtokenGeneratedOn(LocalDateTime refreshtokenGeneratedOn) {
        this.refreshtokenGeneratedOn = refreshtokenGeneratedOn;
        return this;
    }

    /**
     * Getter for <code>individual_sessions.ipaddress</code>.
     */
    public String getIpaddress() {
        return this.ipaddress;
    }

    /**
     * Setter for <code>individual_sessions.ipaddress</code>.
     */
    public IndividualSessions setIpaddress(String ipaddress) {
        this.ipaddress = ipaddress;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final IndividualSessions other = (IndividualSessions) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (individualId == null) {
            if (other.individualId != null)
                return false;
        }
        else if (!individualId.equals(other.individualId))
            return false;
        if (accesstoken == null) {
            if (other.accesstoken != null)
                return false;
        }
        else if (!accesstoken.equals(other.accesstoken))
            return false;
        if (accesstokenExpirytime == null) {
            if (other.accesstokenExpirytime != null)
                return false;
        }
        else if (!accesstokenExpirytime.equals(other.accesstokenExpirytime))
            return false;
        if (accesstokenGeneratedOn == null) {
            if (other.accesstokenGeneratedOn != null)
                return false;
        }
        else if (!accesstokenGeneratedOn.equals(other.accesstokenGeneratedOn))
            return false;
        if (refreshtoken == null) {
            if (other.refreshtoken != null)
                return false;
        }
        else if (!refreshtoken.equals(other.refreshtoken))
            return false;
        if (refreshtokenExpirytime == null) {
            if (other.refreshtokenExpirytime != null)
                return false;
        }
        else if (!refreshtokenExpirytime.equals(other.refreshtokenExpirytime))
            return false;
        if (refreshtokenGeneratedOn == null) {
            if (other.refreshtokenGeneratedOn != null)
                return false;
        }
        else if (!refreshtokenGeneratedOn.equals(other.refreshtokenGeneratedOn))
            return false;
        if (ipaddress == null) {
            if (other.ipaddress != null)
                return false;
        }
        else if (!ipaddress.equals(other.ipaddress))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.individualId == null) ? 0 : this.individualId.hashCode());
        result = prime * result + ((this.accesstoken == null) ? 0 : this.accesstoken.hashCode());
        result = prime * result + ((this.accesstokenExpirytime == null) ? 0 : this.accesstokenExpirytime.hashCode());
        result = prime * result + ((this.accesstokenGeneratedOn == null) ? 0 : this.accesstokenGeneratedOn.hashCode());
        result = prime * result + ((this.refreshtoken == null) ? 0 : this.refreshtoken.hashCode());
        result = prime * result + ((this.refreshtokenExpirytime == null) ? 0 : this.refreshtokenExpirytime.hashCode());
        result = prime * result + ((this.refreshtokenGeneratedOn == null) ? 0 : this.refreshtokenGeneratedOn.hashCode());
        result = prime * result + ((this.ipaddress == null) ? 0 : this.ipaddress.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IndividualSessions (");

        sb.append(id);
        sb.append(", ").append(individualId);
        sb.append(", ").append(accesstoken);
        sb.append(", ").append(accesstokenExpirytime);
        sb.append(", ").append(accesstokenGeneratedOn);
        sb.append(", ").append(refreshtoken);
        sb.append(", ").append(refreshtokenExpirytime);
        sb.append(", ").append(refreshtokenGeneratedOn);
        sb.append(", ").append(ipaddress);

        sb.append(")");
        return sb.toString();
    }
}
