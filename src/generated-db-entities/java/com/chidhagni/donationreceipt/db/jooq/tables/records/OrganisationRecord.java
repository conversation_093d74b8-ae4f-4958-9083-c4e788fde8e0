/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.Organisation;
import com.chidhagni.donationreceipt.organisation.MetaDataDTO;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganisationRecord extends UpdatableRecordImpl<OrganisationRecord> implements Record9<UUID, String, String, MetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>organisation.id</code>.
     */
    public OrganisationRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>organisation.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>organisation.name</code>.
     */
    public OrganisationRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>organisation.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>organisation.category</code>.
     */
    public OrganisationRecord setCategory(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>organisation.category</code>.
     */
    public String getCategory() {
        return (String) get(2);
    }

    /**
     * Setter for <code>organisation.meta_data</code>.
     */
    public OrganisationRecord setMetaData(MetaDataDTO value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>organisation.meta_data</code>.
     */
    public MetaDataDTO getMetaData() {
        return (MetaDataDTO) get(3);
    }

    /**
     * Setter for <code>organisation.is_active</code>.
     */
    public OrganisationRecord setIsActive(Boolean value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>organisation.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(4);
    }

    /**
     * Setter for <code>organisation.created_by</code>.
     */
    public OrganisationRecord setCreatedBy(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>organisation.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>organisation.updated_by</code>.
     */
    public OrganisationRecord setUpdatedBy(UUID value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>organisation.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>organisation.created_on</code>.
     */
    public OrganisationRecord setCreatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>organisation.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>organisation.updated_on</code>.
     */
    public OrganisationRecord setUpdatedOn(LocalDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>organisation.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, String, String, MetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<UUID, String, String, MetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Organisation.ORGANISATION.ID;
    }

    @Override
    public Field<String> field2() {
        return Organisation.ORGANISATION.NAME;
    }

    @Override
    public Field<String> field3() {
        return Organisation.ORGANISATION.CATEGORY;
    }

    @Override
    public Field<MetaDataDTO> field4() {
        return Organisation.ORGANISATION.META_DATA;
    }

    @Override
    public Field<Boolean> field5() {
        return Organisation.ORGANISATION.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field6() {
        return Organisation.ORGANISATION.CREATED_BY;
    }

    @Override
    public Field<UUID> field7() {
        return Organisation.ORGANISATION.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return Organisation.ORGANISATION.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return Organisation.ORGANISATION.UPDATED_ON;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getCategory();
    }

    @Override
    public MetaDataDTO component4() {
        return getMetaData();
    }

    @Override
    public Boolean component5() {
        return getIsActive();
    }

    @Override
    public UUID component6() {
        return getCreatedBy();
    }

    @Override
    public UUID component7() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component8() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component9() {
        return getUpdatedOn();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getCategory();
    }

    @Override
    public MetaDataDTO value4() {
        return getMetaData();
    }

    @Override
    public Boolean value5() {
        return getIsActive();
    }

    @Override
    public UUID value6() {
        return getCreatedBy();
    }

    @Override
    public UUID value7() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value8() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value9() {
        return getUpdatedOn();
    }

    @Override
    public OrganisationRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public OrganisationRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public OrganisationRecord value3(String value) {
        setCategory(value);
        return this;
    }

    @Override
    public OrganisationRecord value4(MetaDataDTO value) {
        setMetaData(value);
        return this;
    }

    @Override
    public OrganisationRecord value5(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public OrganisationRecord value6(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public OrganisationRecord value7(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public OrganisationRecord value8(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public OrganisationRecord value9(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public OrganisationRecord values(UUID value1, String value2, String value3, MetaDataDTO value4, Boolean value5, UUID value6, UUID value7, LocalDateTime value8, LocalDateTime value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OrganisationRecord
     */
    public OrganisationRecord() {
        super(Organisation.ORGANISATION);
    }

    /**
     * Create a detached, initialised OrganisationRecord
     */
    public OrganisationRecord(UUID id, String name, String category, MetaDataDTO metaData, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn) {
        super(Organisation.ORGANISATION);

        setId(id);
        setName(name);
        setCategory(category);
        setMetaData(metaData);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
    }
}
