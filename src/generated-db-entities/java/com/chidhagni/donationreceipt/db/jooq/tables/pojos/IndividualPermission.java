/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID           id;
    private UUID           individualId;
    private List<RoleNode> permissions;
    private LocalDateTime  createdOn;
    private UUID           createdBy;
    private LocalDateTime  updatedOn;
    private UUID           updatedBy;
    private String         remarks;
    private UUID           orgId;

    public IndividualPermission() {}

    public IndividualPermission(IndividualPermission value) {
        this.id = value.id;
        this.individualId = value.individualId;
        this.permissions = value.permissions;
        this.createdOn = value.createdOn;
        this.createdBy = value.createdBy;
        this.updatedOn = value.updatedOn;
        this.updatedBy = value.updatedBy;
        this.remarks = value.remarks;
        this.orgId = value.orgId;
    }

    public IndividualPermission(
        UUID           id,
        UUID           individualId,
        List<RoleNode> permissions,
        LocalDateTime  createdOn,
        UUID           createdBy,
        LocalDateTime  updatedOn,
        UUID           updatedBy,
        String         remarks,
        UUID           orgId
    ) {
        this.id = id;
        this.individualId = individualId;
        this.permissions = permissions;
        this.createdOn = createdOn;
        this.createdBy = createdBy;
        this.updatedOn = updatedOn;
        this.updatedBy = updatedBy;
        this.remarks = remarks;
        this.orgId = orgId;
    }

    /**
     * Getter for <code>individual_permission.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>individual_permission.id</code>.
     */
    public IndividualPermission setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>individual_permission.individual_id</code>.
     */
    public UUID getIndividualId() {
        return this.individualId;
    }

    /**
     * Setter for <code>individual_permission.individual_id</code>.
     */
    public IndividualPermission setIndividualId(UUID individualId) {
        this.individualId = individualId;
        return this;
    }

    /**
     * Getter for <code>individual_permission.permissions</code>.
     */
    public List<RoleNode> getPermissions() {
        return this.permissions;
    }

    /**
     * Setter for <code>individual_permission.permissions</code>.
     */
    public IndividualPermission setPermissions(List<RoleNode> permissions) {
        this.permissions = permissions;
        return this;
    }

    /**
     * Getter for <code>individual_permission.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>individual_permission.created_on</code>.
     */
    public IndividualPermission setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>individual_permission.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>individual_permission.created_by</code>.
     */
    public IndividualPermission setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>individual_permission.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>individual_permission.updated_on</code>.
     */
    public IndividualPermission setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>individual_permission.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>individual_permission.updated_by</code>.
     */
    public IndividualPermission setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>individual_permission.remarks</code>.
     */
    public String getRemarks() {
        return this.remarks;
    }

    /**
     * Setter for <code>individual_permission.remarks</code>.
     */
    public IndividualPermission setRemarks(String remarks) {
        this.remarks = remarks;
        return this;
    }

    /**
     * Getter for <code>individual_permission.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>individual_permission.org_id</code>.
     */
    public IndividualPermission setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final IndividualPermission other = (IndividualPermission) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (individualId == null) {
            if (other.individualId != null)
                return false;
        }
        else if (!individualId.equals(other.individualId))
            return false;
        if (permissions == null) {
            if (other.permissions != null)
                return false;
        }
        else if (!permissions.equals(other.permissions))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (remarks == null) {
            if (other.remarks != null)
                return false;
        }
        else if (!remarks.equals(other.remarks))
            return false;
        if (orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!orgId.equals(other.orgId))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.individualId == null) ? 0 : this.individualId.hashCode());
        result = prime * result + ((this.permissions == null) ? 0 : this.permissions.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.remarks == null) ? 0 : this.remarks.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IndividualPermission (");

        sb.append(id);
        sb.append(", ").append(individualId);
        sb.append(", ").append(permissions);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(remarks);
        sb.append(", ").append(orgId);

        sb.append(")");
        return sb.toString();
    }
}
