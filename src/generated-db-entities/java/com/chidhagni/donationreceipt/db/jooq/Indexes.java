/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq;


import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index IDX_DONOR_GROUP_MAPPING_CREATED_ON = Internal.createIndex(DSL.name("idx_donor_group_mapping_created_on"), DonorGroupMapping.DONOR_GROUP_MAPPING, new OrderField[] { DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_ON }, false);
    public static final Index IDX_DONOR_GROUP_MAPPING_DONOR_ID = Internal.createIndex(DSL.name("idx_donor_group_mapping_donor_id"), DonorGroupMapping.DONOR_GROUP_MAPPING, new OrderField[] { DonorGroupMapping.DONOR_GROUP_MAPPING.DONOR_ID }, false);
    public static final Index IDX_DONOR_GROUP_MAPPING_GROUP_ID = Internal.createIndex(DSL.name("idx_donor_group_mapping_group_id"), DonorGroupMapping.DONOR_GROUP_MAPPING, new OrderField[] { DonorGroupMapping.DONOR_GROUP_MAPPING.GROUP_ID }, false);
    public static final Index IDX_DONOR_GROUPS_CREATED_ON = Internal.createIndex(DSL.name("idx_donor_groups_created_on"), DonorGroups.DONOR_GROUPS, new OrderField[] { DonorGroups.DONOR_GROUPS.CREATED_ON }, false);
    public static final Index IDX_DONOR_GROUPS_FILTERS = Internal.createIndex(DSL.name("idx_donor_groups_filters"), DonorGroups.DONOR_GROUPS, new OrderField[] { DonorGroups.DONOR_GROUPS.FILTERS }, false);
    public static final Index IDX_DONOR_GROUPS_IS_ACTIVE = Internal.createIndex(DSL.name("idx_donor_groups_is_active"), DonorGroups.DONOR_GROUPS, new OrderField[] { DonorGroups.DONOR_GROUPS.IS_ACTIVE }, false);
    public static final Index IDX_IMPORT_BATCH_CATEGORY = Internal.createIndex(DSL.name("idx_import_batch_category"), ImportBatch.IMPORT_BATCH, new OrderField[] { ImportBatch.IMPORT_BATCH.CATEGORY }, false);
    public static final Index IDX_IMPORT_BATCH_CREATED_ON = Internal.createIndex(DSL.name("idx_import_batch_created_on"), ImportBatch.IMPORT_BATCH, new OrderField[] { ImportBatch.IMPORT_BATCH.CREATED_ON }, false);
    public static final Index IDX_IMPORT_BATCH_STATUS = Internal.createIndex(DSL.name("idx_import_batch_status"), ImportBatch.IMPORT_BATCH, new OrderField[] { ImportBatch.IMPORT_BATCH.STATUS }, false);
    public static final Index IDX_IMPORT_BATCH_TENANT_ORG_ID = Internal.createIndex(DSL.name("idx_import_batch_tenant_org_id"), ImportBatch.IMPORT_BATCH, new OrderField[] { ImportBatch.IMPORT_BATCH.TENANT_ORG_ID }, false);
    public static final Index IDX_IMPORT_STAGING_BATCH_ID = Internal.createIndex(DSL.name("idx_import_staging_batch_id"), ImportStaging.IMPORT_STAGING, new OrderField[] { ImportStaging.IMPORT_STAGING.IMPORT_BATCH_ID }, false);
    public static final Index IDX_IMPORT_STAGING_CATEGORY = Internal.createIndex(DSL.name("idx_import_staging_category"), ImportStaging.IMPORT_STAGING, new OrderField[] { ImportStaging.IMPORT_STAGING.CATEGORY }, false);
    public static final Index IDX_IMPORT_STAGING_EMAIL = Internal.createIndex(DSL.name("idx_import_staging_email"), ImportStaging.IMPORT_STAGING, new OrderField[] { ImportStaging.IMPORT_STAGING.EMAIL }, false);
    public static final Index IDX_IMPORT_STAGING_METADATA = Internal.createIndex(DSL.name("idx_import_staging_metadata"), ImportStaging.IMPORT_STAGING, new OrderField[] { ImportStaging.IMPORT_STAGING.META_DATA }, false);
    public static final Index IDX_IMPORT_STAGING_PAN_NO = Internal.createIndex(DSL.name("idx_import_staging_pan_no"), ImportStaging.IMPORT_STAGING, new OrderField[] { ImportStaging.IMPORT_STAGING.PAN_NO }, false);
    public static final Index IDX_IMPORT_STAGING_STATUS = Internal.createIndex(DSL.name("idx_import_staging_status"), ImportStaging.IMPORT_STAGING, new OrderField[] { ImportStaging.IMPORT_STAGING.STATUS }, false);
}
