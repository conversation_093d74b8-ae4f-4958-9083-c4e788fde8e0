/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Donors implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          tenantOrgId;
    private String        name;
    private String        email;
    private String        mobileNumber;
    private String        panNo;
    private DonorMetaData metaData;
    private Boolean       isActive;
    private UUID          createdBy;
    private UUID          updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String        encryptedPanNo;
    private String        panNoNonce;

    public Donors() {}

    public Donors(Donors value) {
        this.id = value.id;
        this.tenantOrgId = value.tenantOrgId;
        this.name = value.name;
        this.email = value.email;
        this.mobileNumber = value.mobileNumber;
        this.panNo = value.panNo;
        this.metaData = value.metaData;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.encryptedPanNo = value.encryptedPanNo;
        this.panNoNonce = value.panNoNonce;
    }

    public Donors(
        UUID          id,
        UUID          tenantOrgId,
        String        name,
        String        email,
        String        mobileNumber,
        String        panNo,
        DonorMetaData metaData,
        Boolean       isActive,
        UUID          createdBy,
        UUID          updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        String        encryptedPanNo,
        String        panNoNonce
    ) {
        this.id = id;
        this.tenantOrgId = tenantOrgId;
        this.name = name;
        this.email = email;
        this.mobileNumber = mobileNumber;
        this.panNo = panNo;
        this.metaData = metaData;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.encryptedPanNo = encryptedPanNo;
        this.panNoNonce = panNoNonce;
    }

    /**
     * Getter for <code>donors.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>donors.id</code>.
     */
    public Donors setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>donors.tenant_org_id</code>.
     */
    public UUID getTenantOrgId() {
        return this.tenantOrgId;
    }

    /**
     * Setter for <code>donors.tenant_org_id</code>.
     */
    public Donors setTenantOrgId(UUID tenantOrgId) {
        this.tenantOrgId = tenantOrgId;
        return this;
    }

    /**
     * Getter for <code>donors.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>donors.name</code>.
     */
    public Donors setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>donors.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>donors.email</code>.
     */
    public Donors setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>donors.mobile_number</code>.
     */
    public String getMobileNumber() {
        return this.mobileNumber;
    }

    /**
     * Setter for <code>donors.mobile_number</code>.
     */
    public Donors setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
        return this;
    }

    /**
     * Getter for <code>donors.pan_no</code>.
     */
    public String getPanNo() {
        return this.panNo;
    }

    /**
     * Setter for <code>donors.pan_no</code>.
     */
    public Donors setPanNo(String panNo) {
        this.panNo = panNo;
        return this;
    }

    /**
     * Getter for <code>donors.meta_data</code>.
     */
    public DonorMetaData getMetaData() {
        return this.metaData;
    }

    /**
     * Setter for <code>donors.meta_data</code>.
     */
    public Donors setMetaData(DonorMetaData metaData) {
        this.metaData = metaData;
        return this;
    }

    /**
     * Getter for <code>donors.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>donors.is_active</code>.
     */
    public Donors setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>donors.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>donors.created_by</code>.
     */
    public Donors setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>donors.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>donors.updated_by</code>.
     */
    public Donors setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>donors.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>donors.created_on</code>.
     */
    public Donors setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>donors.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>donors.updated_on</code>.
     */
    public Donors setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>donors.encrypted_pan_no</code>.
     */
    public String getEncryptedPanNo() {
        return this.encryptedPanNo;
    }

    /**
     * Setter for <code>donors.encrypted_pan_no</code>.
     */
    public Donors setEncryptedPanNo(String encryptedPanNo) {
        this.encryptedPanNo = encryptedPanNo;
        return this;
    }

    /**
     * Getter for <code>donors.pan_no_nonce</code>.
     */
    public String getPanNoNonce() {
        return this.panNoNonce;
    }

    /**
     * Setter for <code>donors.pan_no_nonce</code>.
     */
    public Donors setPanNoNonce(String panNoNonce) {
        this.panNoNonce = panNoNonce;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Donors other = (Donors) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (tenantOrgId == null) {
            if (other.tenantOrgId != null)
                return false;
        }
        else if (!tenantOrgId.equals(other.tenantOrgId))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (email == null) {
            if (other.email != null)
                return false;
        }
        else if (!email.equals(other.email))
            return false;
        if (mobileNumber == null) {
            if (other.mobileNumber != null)
                return false;
        }
        else if (!mobileNumber.equals(other.mobileNumber))
            return false;
        if (panNo == null) {
            if (other.panNo != null)
                return false;
        }
        else if (!panNo.equals(other.panNo))
            return false;
        if (metaData == null) {
            if (other.metaData != null)
                return false;
        }
        else if (!metaData.equals(other.metaData))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (encryptedPanNo == null) {
            if (other.encryptedPanNo != null)
                return false;
        }
        else if (!encryptedPanNo.equals(other.encryptedPanNo))
            return false;
        if (panNoNonce == null) {
            if (other.panNoNonce != null)
                return false;
        }
        else if (!panNoNonce.equals(other.panNoNonce))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.tenantOrgId == null) ? 0 : this.tenantOrgId.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.mobileNumber == null) ? 0 : this.mobileNumber.hashCode());
        result = prime * result + ((this.panNo == null) ? 0 : this.panNo.hashCode());
        result = prime * result + ((this.metaData == null) ? 0 : this.metaData.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.encryptedPanNo == null) ? 0 : this.encryptedPanNo.hashCode());
        result = prime * result + ((this.panNoNonce == null) ? 0 : this.panNoNonce.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Donors (");

        sb.append(id);
        sb.append(", ").append(tenantOrgId);
        sb.append(", ").append(name);
        sb.append(", ").append(email);
        sb.append(", ").append(mobileNumber);
        sb.append(", ").append(panNo);
        sb.append(", ").append(metaData);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(encryptedPanNo);
        sb.append(", ").append(panNoNonce);

        sb.append(")");
        return sb.toString();
    }
}
