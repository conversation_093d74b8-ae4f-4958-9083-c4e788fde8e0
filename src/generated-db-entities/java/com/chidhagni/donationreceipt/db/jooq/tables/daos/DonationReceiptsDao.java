/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonationReceiptsRecord;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DonationReceiptsDao extends DAOImpl<DonationReceiptsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts, UUID> {

    /**
     * Create a new DonationReceiptsDao without any configuration
     */
    public DonationReceiptsDao() {
        super(DonationReceipts.DONATION_RECEIPTS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts.class);
    }

    /**
     * Create a new DonationReceiptsDao with an attached configuration
     */
    @Autowired
    public DonationReceiptsDao(Configuration configuration) {
        super(DonationReceipts.DONATION_RECEIPTS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchById(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts fetchOneById(UUID value) {
        return fetchOne(DonationReceipts.DONATION_RECEIPTS.ID, value);
    }

    /**
     * Fetch records that have <code>receipt_no BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfReceiptNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.RECEIPT_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>receipt_no IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByReceiptNo(String... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.RECEIPT_NO, values);
    }

    /**
     * Fetch records that have <code>tenant_org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfTenantOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.TENANT_ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tenant_org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByTenantOrgId(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.TENANT_ORG_ID, values);
    }

    /**
     * Fetch records that have <code>meta_data BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfMetaData(DonationReceiptMetaDataDTO lowerInclusive, DonationReceiptMetaDataDTO upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.META_DATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>meta_data IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByMetaData(DonationReceiptMetaDataDTO... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.META_DATA, values);
    }

    /**
     * Fetch records that have <code>donation_type_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfDonationTypeId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.DONATION_TYPE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>donation_type_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByDonationTypeId(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.DONATION_TYPE_ID, values);
    }

    /**
     * Fetch records that have <code>donation_head_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfDonationHeadId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.DONATION_HEAD_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>donation_head_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByDonationHeadId(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.DONATION_HEAD_ID, values);
    }

    /**
     * Fetch records that have <code>receipt_date BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfReceiptDate(LocalDate lowerInclusive, LocalDate upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.RECEIPT_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>receipt_date IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByReceiptDate(LocalDate... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.RECEIPT_DATE, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByCreatedBy(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByUpdatedBy(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByIsActive(Boolean... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>donor_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfDonorId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.DONOR_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>donor_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByDonorId(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.DONOR_ID, values);
    }

    /**
     * Fetch records that have <code>donor_org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchRangeOfDonorOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonationReceipts.DONATION_RECEIPTS.DONOR_ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>donor_org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts> fetchByDonorOrgId(UUID... values) {
        return fetch(DonationReceipts.DONATION_RECEIPTS.DONOR_ORG_ID, values);
    }
}
