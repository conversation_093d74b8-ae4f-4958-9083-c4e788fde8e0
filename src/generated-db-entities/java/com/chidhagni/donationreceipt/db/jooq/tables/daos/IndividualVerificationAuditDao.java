/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualVerificationAuditRecord;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class IndividualVerificationAuditDao extends DAOImpl<IndividualVerificationAuditRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit, UUID> {

    /**
     * Create a new IndividualVerificationAuditDao without any configuration
     */
    public IndividualVerificationAuditDao() {
        super(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit.class);
    }

    /**
     * Create a new IndividualVerificationAuditDao with an attached configuration
     */
    @Autowired
    public IndividualVerificationAuditDao(Configuration configuration) {
        super(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchById(UUID... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit fetchOneById(UUID value) {
        return fetchOne(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ID, value);
    }

    /**
     * Fetch records that have <code>contact_type BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfContactType(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>contact_type IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByContactType(String... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_TYPE, values);
    }

    /**
     * Fetch records that have <code>contact_value BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfContactValue(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_VALUE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>contact_value IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByContactValue(String... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_VALUE, values);
    }

    /**
     * Fetch records that have <code>activation_link BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfActivationLink(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>activation_link IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByActivationLink(Boolean... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK, values);
    }

    /**
     * Fetch records that have <code>activation_link_created_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfActivationLinkCreatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>activation_link_created_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByActivationLinkCreatedAt(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>activation_link_expires_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfActivationLinkExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>activation_link_expires_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByActivationLinkExpiresAt(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>activation_link_verified_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfActivationLinkVerifiedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_VERIFIED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>activation_link_verified_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByActivationLinkVerifiedAt(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ACTIVATION_LINK_VERIFIED_AT, values);
    }

    /**
     * Fetch records that have <code>ip_address BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfIpAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.IP_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ip_address IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByIpAddress(String... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.IP_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>role_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfRoleId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ROLE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>role_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByRoleId(UUID... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ROLE_ID, values);
    }

    /**
     * Fetch records that have <code>verification_status BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfVerificationStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>verification_status IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByVerificationStatus(String... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByIsActive(Boolean... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByCreatedBy(UUID... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByUpdatedBy(UUID... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>meta_data BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfMetaData(IndividualVerificationMetaData lowerInclusive, IndividualVerificationMetaData upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.META_DATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>meta_data IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByMetaData(IndividualVerificationMetaData... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.META_DATA, values);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByOrgId(UUID... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID, values);
    }

    /**
     * Fetch records that have <code>otp_code BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfOtpCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>otp_code IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByOtpCode(String... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_CODE, values);
    }

    /**
     * Fetch records that have <code>otp_created_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfOtpCreatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>otp_created_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByOtpCreatedAt(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>otp_expires_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfOtpExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>otp_expires_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByOtpExpiresAt(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>otp_verified_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchRangeOfOtpVerifiedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_VERIFIED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>otp_verified_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit> fetchByOtpVerifiedAt(LocalDateTime... values) {
        return fetch(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.OTP_VERIFIED_AT, values);
    }
}
