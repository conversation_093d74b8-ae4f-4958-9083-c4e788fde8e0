/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Indexes;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorGroupsRecord;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupFilters;
import com.chidhagni.donationreceipt.donorgroups.jooq.DonorGroupFiltersJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row10;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorGroups extends TableImpl<DonorGroupsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>donor_groups</code>
     */
    public static final DonorGroups DONOR_GROUPS = new DonorGroups();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DonorGroupsRecord> getRecordType() {
        return DonorGroupsRecord.class;
    }

    /**
     * The column <code>donor_groups.id</code>.
     */
    public final TableField<DonorGroupsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donor_groups.name</code>.
     */
    public final TableField<DonorGroupsRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>donor_groups.description</code>.
     */
    public final TableField<DonorGroupsRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>donor_groups.filters</code>.
     */
    public final TableField<DonorGroupsRecord, DonorGroupFilters> FILTERS = createField(DSL.name("filters"), SQLDataType.JSONB, this, "", new DonorGroupFiltersJsonConverter());

    /**
     * The column <code>donor_groups.created_by</code>.
     */
    public final TableField<DonorGroupsRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donor_groups.updated_by</code>.
     */
    public final TableField<DonorGroupsRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donor_groups.created_on</code>.
     */
    public final TableField<DonorGroupsRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>donor_groups.updated_on</code>.
     */
    public final TableField<DonorGroupsRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>donor_groups.is_active</code>.
     */
    public final TableField<DonorGroupsRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>donor_groups.org_id</code>.
     */
    public final TableField<DonorGroupsRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID, this, "");

    private DonorGroups(Name alias, Table<DonorGroupsRecord> aliased) {
        this(alias, aliased, null);
    }

    private DonorGroups(Name alias, Table<DonorGroupsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>donor_groups</code> table reference
     */
    public DonorGroups(String alias) {
        this(DSL.name(alias), DONOR_GROUPS);
    }

    /**
     * Create an aliased <code>donor_groups</code> table reference
     */
    public DonorGroups(Name alias) {
        this(alias, DONOR_GROUPS);
    }

    /**
     * Create a <code>donor_groups</code> table reference
     */
    public DonorGroups() {
        this(DSL.name("donor_groups"), null);
    }

    public <O extends Record> DonorGroups(Table<O> child, ForeignKey<O, DonorGroupsRecord> key) {
        super(child, key, DONOR_GROUPS);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.IDX_DONOR_GROUPS_CREATED_ON, Indexes.IDX_DONOR_GROUPS_FILTERS, Indexes.IDX_DONOR_GROUPS_IS_ACTIVE);
    }

    @Override
    public UniqueKey<DonorGroupsRecord> getPrimaryKey() {
        return Keys.DONOR_GROUPS_PKEY;
    }

    @Override
    public List<UniqueKey<DonorGroupsRecord>> getKeys() {
        return Arrays.<UniqueKey<DonorGroupsRecord>>asList(Keys.DONOR_GROUPS_PKEY);
    }

    @Override
    public List<ForeignKey<DonorGroupsRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<DonorGroupsRecord, ?>>asList(Keys.DONOR_GROUPS__FK_DONOR_GROUPS_ORG);
    }

    private transient Organisation _organisation;

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.DONOR_GROUPS__FK_DONOR_GROUPS_ORG);

        return _organisation;
    }

    @Override
    public DonorGroups as(String alias) {
        return new DonorGroups(DSL.name(alias), this);
    }

    @Override
    public DonorGroups as(Name alias) {
        return new DonorGroups(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DonorGroups rename(String name) {
        return new DonorGroups(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DonorGroups rename(Name name) {
        return new DonorGroups(name, null);
    }

    // -------------------------------------------------------------------------
    // Row10 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row10<UUID, String, String, DonorGroupFilters, UUID, UUID, LocalDateTime, LocalDateTime, Boolean, UUID> fieldsRow() {
        return (Row10) super.fieldsRow();
    }
}
