/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Individual implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID                  id;
    private String                name;
    private String                email;
    private String                mobileNumber;
    private String                password;
    private String                socialLoginProvider;
    private String                socialLoginProviderId;
    private String                socialLoginProviderImageUrl;
    private IndividualMetaDataDTO metaData;
    private Boolean               isActive;
    private UUID                  createdBy;
    private UUID                  updatedBy;
    private LocalDateTime         createdOn;
    private LocalDateTime         updatedOn;

    public Individual() {}

    public Individual(Individual value) {
        this.id = value.id;
        this.name = value.name;
        this.email = value.email;
        this.mobileNumber = value.mobileNumber;
        this.password = value.password;
        this.socialLoginProvider = value.socialLoginProvider;
        this.socialLoginProviderId = value.socialLoginProviderId;
        this.socialLoginProviderImageUrl = value.socialLoginProviderImageUrl;
        this.metaData = value.metaData;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
    }

    public Individual(
        UUID                  id,
        String                name,
        String                email,
        String                mobileNumber,
        String                password,
        String                socialLoginProvider,
        String                socialLoginProviderId,
        String                socialLoginProviderImageUrl,
        IndividualMetaDataDTO metaData,
        Boolean               isActive,
        UUID                  createdBy,
        UUID                  updatedBy,
        LocalDateTime         createdOn,
        LocalDateTime         updatedOn
    ) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.mobileNumber = mobileNumber;
        this.password = password;
        this.socialLoginProvider = socialLoginProvider;
        this.socialLoginProviderId = socialLoginProviderId;
        this.socialLoginProviderImageUrl = socialLoginProviderImageUrl;
        this.metaData = metaData;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
    }

    /**
     * Getter for <code>individual.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>individual.id</code>.
     */
    public Individual setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>individual.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>individual.name</code>.
     */
    public Individual setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>individual.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>individual.email</code>.
     */
    public Individual setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>individual.mobile_number</code>.
     */
    public String getMobileNumber() {
        return this.mobileNumber;
    }

    /**
     * Setter for <code>individual.mobile_number</code>.
     */
    public Individual setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
        return this;
    }

    /**
     * Getter for <code>individual.password</code>.
     */
    public String getPassword() {
        return this.password;
    }

    /**
     * Setter for <code>individual.password</code>.
     */
    public Individual setPassword(String password) {
        this.password = password;
        return this;
    }

    /**
     * Getter for <code>individual.social_login_provider</code>.
     */
    public String getSocialLoginProvider() {
        return this.socialLoginProvider;
    }

    /**
     * Setter for <code>individual.social_login_provider</code>.
     */
    public Individual setSocialLoginProvider(String socialLoginProvider) {
        this.socialLoginProvider = socialLoginProvider;
        return this;
    }

    /**
     * Getter for <code>individual.social_login_provider_id</code>.
     */
    public String getSocialLoginProviderId() {
        return this.socialLoginProviderId;
    }

    /**
     * Setter for <code>individual.social_login_provider_id</code>.
     */
    public Individual setSocialLoginProviderId(String socialLoginProviderId) {
        this.socialLoginProviderId = socialLoginProviderId;
        return this;
    }

    /**
     * Getter for <code>individual.social_login_provider_image_url</code>.
     */
    public String getSocialLoginProviderImageUrl() {
        return this.socialLoginProviderImageUrl;
    }

    /**
     * Setter for <code>individual.social_login_provider_image_url</code>.
     */
    public Individual setSocialLoginProviderImageUrl(String socialLoginProviderImageUrl) {
        this.socialLoginProviderImageUrl = socialLoginProviderImageUrl;
        return this;
    }

    /**
     * Getter for <code>individual.meta_data</code>.
     */
    public IndividualMetaDataDTO getMetaData() {
        return this.metaData;
    }

    /**
     * Setter for <code>individual.meta_data</code>.
     */
    public Individual setMetaData(IndividualMetaDataDTO metaData) {
        this.metaData = metaData;
        return this;
    }

    /**
     * Getter for <code>individual.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>individual.is_active</code>.
     */
    public Individual setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>individual.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>individual.created_by</code>.
     */
    public Individual setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>individual.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>individual.updated_by</code>.
     */
    public Individual setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>individual.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>individual.created_on</code>.
     */
    public Individual setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>individual.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>individual.updated_on</code>.
     */
    public Individual setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Individual other = (Individual) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (email == null) {
            if (other.email != null)
                return false;
        }
        else if (!email.equals(other.email))
            return false;
        if (mobileNumber == null) {
            if (other.mobileNumber != null)
                return false;
        }
        else if (!mobileNumber.equals(other.mobileNumber))
            return false;
        if (password == null) {
            if (other.password != null)
                return false;
        }
        else if (!password.equals(other.password))
            return false;
        if (socialLoginProvider == null) {
            if (other.socialLoginProvider != null)
                return false;
        }
        else if (!socialLoginProvider.equals(other.socialLoginProvider))
            return false;
        if (socialLoginProviderId == null) {
            if (other.socialLoginProviderId != null)
                return false;
        }
        else if (!socialLoginProviderId.equals(other.socialLoginProviderId))
            return false;
        if (socialLoginProviderImageUrl == null) {
            if (other.socialLoginProviderImageUrl != null)
                return false;
        }
        else if (!socialLoginProviderImageUrl.equals(other.socialLoginProviderImageUrl))
            return false;
        if (metaData == null) {
            if (other.metaData != null)
                return false;
        }
        else if (!metaData.equals(other.metaData))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.mobileNumber == null) ? 0 : this.mobileNumber.hashCode());
        result = prime * result + ((this.password == null) ? 0 : this.password.hashCode());
        result = prime * result + ((this.socialLoginProvider == null) ? 0 : this.socialLoginProvider.hashCode());
        result = prime * result + ((this.socialLoginProviderId == null) ? 0 : this.socialLoginProviderId.hashCode());
        result = prime * result + ((this.socialLoginProviderImageUrl == null) ? 0 : this.socialLoginProviderImageUrl.hashCode());
        result = prime * result + ((this.metaData == null) ? 0 : this.metaData.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Individual (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(email);
        sb.append(", ").append(mobileNumber);
        sb.append(", ").append(password);
        sb.append(", ").append(socialLoginProvider);
        sb.append(", ").append(socialLoginProviderId);
        sb.append(", ").append(socialLoginProviderImageUrl);
        sb.append(", ").append(metaData);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);

        sb.append(")");
        return sb.toString();
    }
}
