/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonationReceiptsRecord;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;
import com.chidhagni.donationreceipt.donationreceipts.jooq.DonationReceiptsMetaDataJsonConverter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonationReceipts extends TableImpl<DonationReceiptsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>donation_receipts</code>
     */
    public static final DonationReceipts DONATION_RECEIPTS = new DonationReceipts();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DonationReceiptsRecord> getRecordType() {
        return DonationReceiptsRecord.class;
    }

    /**
     * The column <code>donation_receipts.id</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donation_receipts.receipt_no</code>.
     */
    public final TableField<DonationReceiptsRecord, String> RECEIPT_NO = createField(DSL.name("receipt_no"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>donation_receipts.tenant_org_id</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> TENANT_ORG_ID = createField(DSL.name("tenant_org_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donation_receipts.meta_data</code>.
     */
    public final TableField<DonationReceiptsRecord, DonationReceiptMetaDataDTO> META_DATA = createField(DSL.name("meta_data"), SQLDataType.JSONB, this, "", new DonationReceiptsMetaDataJsonConverter());

    /**
     * The column <code>donation_receipts.donation_type_id</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> DONATION_TYPE_ID = createField(DSL.name("donation_type_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donation_receipts.donation_head_id</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> DONATION_HEAD_ID = createField(DSL.name("donation_head_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donation_receipts.receipt_date</code>.
     */
    public final TableField<DonationReceiptsRecord, LocalDate> RECEIPT_DATE = createField(DSL.name("receipt_date"), SQLDataType.LOCALDATE.nullable(false), this, "");

    /**
     * The column <code>donation_receipts.created_on</code>.
     */
    public final TableField<DonationReceiptsRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>donation_receipts.created_by</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donation_receipts.updated_on</code>.
     */
    public final TableField<DonationReceiptsRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>donation_receipts.updated_by</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donation_receipts.is_active</code>.
     */
    public final TableField<DonationReceiptsRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>donation_receipts.donor_id</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> DONOR_ID = createField(DSL.name("donor_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donation_receipts.donor_org_id</code>.
     */
    public final TableField<DonationReceiptsRecord, UUID> DONOR_ORG_ID = createField(DSL.name("donor_org_id"), SQLDataType.UUID, this, "");

    private DonationReceipts(Name alias, Table<DonationReceiptsRecord> aliased) {
        this(alias, aliased, null);
    }

    private DonationReceipts(Name alias, Table<DonationReceiptsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>donation_receipts</code> table reference
     */
    public DonationReceipts(String alias) {
        this(DSL.name(alias), DONATION_RECEIPTS);
    }

    /**
     * Create an aliased <code>donation_receipts</code> table reference
     */
    public DonationReceipts(Name alias) {
        this(alias, DONATION_RECEIPTS);
    }

    /**
     * Create a <code>donation_receipts</code> table reference
     */
    public DonationReceipts() {
        this(DSL.name("donation_receipts"), null);
    }

    public <O extends Record> DonationReceipts(Table<O> child, ForeignKey<O, DonationReceiptsRecord> key) {
        super(child, key, DONATION_RECEIPTS);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<DonationReceiptsRecord> getPrimaryKey() {
        return Keys.DONATION_RECEIPTS_PKEY;
    }

    @Override
    public List<UniqueKey<DonationReceiptsRecord>> getKeys() {
        return Arrays.<UniqueKey<DonationReceiptsRecord>>asList(Keys.DONATION_RECEIPTS_PKEY);
    }

    @Override
    public List<ForeignKey<DonationReceiptsRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<DonationReceiptsRecord, ?>>asList(Keys.DONATION_RECEIPTS__DONATION_RECEIPT_ORG_FK, Keys.DONATION_RECEIPTS__FK_DONOR_ID, Keys.DONATION_RECEIPTS__FK_DONOR_ORGANISATION_ID);
    }

    private transient Organisation _donationReceiptOrgFk;
    private transient Donors _donors;
    private transient Organisation _fkDonorOrganisationId;

    public Organisation donationReceiptOrgFk() {
        if (_donationReceiptOrgFk == null)
            _donationReceiptOrgFk = new Organisation(this, Keys.DONATION_RECEIPTS__DONATION_RECEIPT_ORG_FK);

        return _donationReceiptOrgFk;
    }

    public Donors donors() {
        if (_donors == null)
            _donors = new Donors(this, Keys.DONATION_RECEIPTS__FK_DONOR_ID);

        return _donors;
    }

    public Organisation fkDonorOrganisationId() {
        if (_fkDonorOrganisationId == null)
            _fkDonorOrganisationId = new Organisation(this, Keys.DONATION_RECEIPTS__FK_DONOR_ORGANISATION_ID);

        return _fkDonorOrganisationId;
    }

    @Override
    public DonationReceipts as(String alias) {
        return new DonationReceipts(DSL.name(alias), this);
    }

    @Override
    public DonationReceipts as(Name alias) {
        return new DonationReceipts(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DonationReceipts rename(String name) {
        return new DonationReceipts(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DonationReceipts rename(Name name) {
        return new DonationReceipts(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, String, UUID, DonationReceiptMetaDataDTO, UUID, UUID, LocalDate, LocalDateTime, UUID, LocalDateTime, UUID, Boolean, UUID, UUID> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
