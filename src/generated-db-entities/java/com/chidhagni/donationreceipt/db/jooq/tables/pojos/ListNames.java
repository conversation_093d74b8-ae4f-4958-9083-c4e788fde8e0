/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ListNames implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        name;
    private Boolean       isActive;
    private UUID          createdBy;
    private UUID          updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private Boolean       isStatistics;

    public ListNames() {}

    public ListNames(ListNames value) {
        this.id = value.id;
        this.name = value.name;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.isStatistics = value.isStatistics;
    }

    public ListNames(
        UUID          id,
        String        name,
        Boolean       isActive,
        UUID          createdBy,
        UUID          updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        Boolean       isStatistics
    ) {
        this.id = id;
        this.name = name;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.isStatistics = isStatistics;
    }

    /**
     * Getter for <code>list_names.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>list_names.id</code>.
     */
    public ListNames setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>list_names.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>list_names.name</code>.
     */
    public ListNames setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>list_names.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>list_names.is_active</code>.
     */
    public ListNames setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>list_names.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>list_names.created_by</code>.
     */
    public ListNames setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>list_names.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>list_names.updated_by</code>.
     */
    public ListNames setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>list_names.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>list_names.created_on</code>.
     */
    public ListNames setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>list_names.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>list_names.updated_on</code>.
     */
    public ListNames setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>list_names.is_statistics</code>.
     */
    public Boolean getIsStatistics() {
        return this.isStatistics;
    }

    /**
     * Setter for <code>list_names.is_statistics</code>.
     */
    public ListNames setIsStatistics(Boolean isStatistics) {
        this.isStatistics = isStatistics;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final ListNames other = (ListNames) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (isStatistics == null) {
            if (other.isStatistics != null)
                return false;
        }
        else if (!isStatistics.equals(other.isStatistics))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.isStatistics == null) ? 0 : this.isStatistics.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ListNames (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(isStatistics);

        sb.append(")");
        return sb.toString();
    }
}
