/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Roles implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID           id;
    private String         name;
    private String         description;
    private UUID           parentRoleId;
    private Boolean        isActive;
    private LocalDateTime  createdOn;
    private LocalDateTime  updatedOn;
    private UUID           createdBy;
    private UUID           updatedBy;
    private List<RoleNode> permissions;
    private UUID           orgId;

    public Roles() {}

    public Roles(Roles value) {
        this.id = value.id;
        this.name = value.name;
        this.description = value.description;
        this.parentRoleId = value.parentRoleId;
        this.isActive = value.isActive;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.permissions = value.permissions;
        this.orgId = value.orgId;
    }

    public Roles(
        UUID           id,
        String         name,
        String         description,
        UUID           parentRoleId,
        Boolean        isActive,
        LocalDateTime  createdOn,
        LocalDateTime  updatedOn,
        UUID           createdBy,
        UUID           updatedBy,
        List<RoleNode> permissions,
        UUID           orgId
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.parentRoleId = parentRoleId;
        this.isActive = isActive;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.permissions = permissions;
        this.orgId = orgId;
    }

    /**
     * Getter for <code>roles.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>roles.id</code>.
     */
    public Roles setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>roles.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>roles.name</code>.
     */
    public Roles setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>roles.description</code>.
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>roles.description</code>.
     */
    public Roles setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>roles.parent_role_id</code>.
     */
    public UUID getParentRoleId() {
        return this.parentRoleId;
    }

    /**
     * Setter for <code>roles.parent_role_id</code>.
     */
    public Roles setParentRoleId(UUID parentRoleId) {
        this.parentRoleId = parentRoleId;
        return this;
    }

    /**
     * Getter for <code>roles.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>roles.is_active</code>.
     */
    public Roles setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>roles.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>roles.created_on</code>.
     */
    public Roles setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>roles.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>roles.updated_on</code>.
     */
    public Roles setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>roles.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>roles.created_by</code>.
     */
    public Roles setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>roles.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>roles.updated_by</code>.
     */
    public Roles setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>roles.permissions</code>.
     */
    public List<RoleNode> getPermissions() {
        return this.permissions;
    }

    /**
     * Setter for <code>roles.permissions</code>.
     */
    public Roles setPermissions(List<RoleNode> permissions) {
        this.permissions = permissions;
        return this;
    }

    /**
     * Getter for <code>roles.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>roles.org_id</code>.
     */
    public Roles setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Roles other = (Roles) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        }
        else if (!description.equals(other.description))
            return false;
        if (parentRoleId == null) {
            if (other.parentRoleId != null)
                return false;
        }
        else if (!parentRoleId.equals(other.parentRoleId))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (permissions == null) {
            if (other.permissions != null)
                return false;
        }
        else if (!permissions.equals(other.permissions))
            return false;
        if (orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!orgId.equals(other.orgId))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.parentRoleId == null) ? 0 : this.parentRoleId.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.permissions == null) ? 0 : this.permissions.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Roles (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(parentRoleId);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(permissions);
        sb.append(", ").append(orgId);

        sb.append(")");
        return sb.toString();
    }
}
