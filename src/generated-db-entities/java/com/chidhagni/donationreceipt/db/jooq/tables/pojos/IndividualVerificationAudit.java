/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualVerificationAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID                           id;
    private String                         contactType;
    private String                         contactValue;
    private Boolean                        activationLink;
    private LocalDateTime                  activationLinkCreatedAt;
    private LocalDateTime                  activationLinkExpiresAt;
    private LocalDateTime                  activationLinkVerifiedAt;
    private String                         ipAddress;
    private UUID                           roleId;
    private String                         verificationStatus;
    private Boolean                        isActive;
    private UUID                           createdBy;
    private UUID                           updatedBy;
    private LocalDateTime                  createdOn;
    private LocalDateTime                  updatedOn;
    private IndividualVerificationMetaData metaData;
    private UUID                           orgId;
    private String                         otpCode;
    private LocalDateTime                  otpCreatedAt;
    private LocalDateTime                  otpExpiresAt;
    private LocalDateTime                  otpVerifiedAt;

    public IndividualVerificationAudit() {}

    public IndividualVerificationAudit(IndividualVerificationAudit value) {
        this.id = value.id;
        this.contactType = value.contactType;
        this.contactValue = value.contactValue;
        this.activationLink = value.activationLink;
        this.activationLinkCreatedAt = value.activationLinkCreatedAt;
        this.activationLinkExpiresAt = value.activationLinkExpiresAt;
        this.activationLinkVerifiedAt = value.activationLinkVerifiedAt;
        this.ipAddress = value.ipAddress;
        this.roleId = value.roleId;
        this.verificationStatus = value.verificationStatus;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.metaData = value.metaData;
        this.orgId = value.orgId;
        this.otpCode = value.otpCode;
        this.otpCreatedAt = value.otpCreatedAt;
        this.otpExpiresAt = value.otpExpiresAt;
        this.otpVerifiedAt = value.otpVerifiedAt;
    }

    public IndividualVerificationAudit(
        UUID                           id,
        String                         contactType,
        String                         contactValue,
        Boolean                        activationLink,
        LocalDateTime                  activationLinkCreatedAt,
        LocalDateTime                  activationLinkExpiresAt,
        LocalDateTime                  activationLinkVerifiedAt,
        String                         ipAddress,
        UUID                           roleId,
        String                         verificationStatus,
        Boolean                        isActive,
        UUID                           createdBy,
        UUID                           updatedBy,
        LocalDateTime                  createdOn,
        LocalDateTime                  updatedOn,
        IndividualVerificationMetaData metaData,
        UUID                           orgId,
        String                         otpCode,
        LocalDateTime                  otpCreatedAt,
        LocalDateTime                  otpExpiresAt,
        LocalDateTime                  otpVerifiedAt
    ) {
        this.id = id;
        this.contactType = contactType;
        this.contactValue = contactValue;
        this.activationLink = activationLink;
        this.activationLinkCreatedAt = activationLinkCreatedAt;
        this.activationLinkExpiresAt = activationLinkExpiresAt;
        this.activationLinkVerifiedAt = activationLinkVerifiedAt;
        this.ipAddress = ipAddress;
        this.roleId = roleId;
        this.verificationStatus = verificationStatus;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.metaData = metaData;
        this.orgId = orgId;
        this.otpCode = otpCode;
        this.otpCreatedAt = otpCreatedAt;
        this.otpExpiresAt = otpExpiresAt;
        this.otpVerifiedAt = otpVerifiedAt;
    }

    /**
     * Getter for <code>individual_verification_audit.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>individual_verification_audit.id</code>.
     */
    public IndividualVerificationAudit setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.contact_type</code>.
     */
    public String getContactType() {
        return this.contactType;
    }

    /**
     * Setter for <code>individual_verification_audit.contact_type</code>.
     */
    public IndividualVerificationAudit setContactType(String contactType) {
        this.contactType = contactType;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.contact_value</code>.
     */
    public String getContactValue() {
        return this.contactValue;
    }

    /**
     * Setter for <code>individual_verification_audit.contact_value</code>.
     */
    public IndividualVerificationAudit setContactValue(String contactValue) {
        this.contactValue = contactValue;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link</code>.
     */
    public Boolean getActivationLink() {
        return this.activationLink;
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link</code>.
     */
    public IndividualVerificationAudit setActivationLink(Boolean activationLink) {
        this.activationLink = activationLink;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link_created_at</code>.
     */
    public LocalDateTime getActivationLinkCreatedAt() {
        return this.activationLinkCreatedAt;
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link_created_at</code>.
     */
    public IndividualVerificationAudit setActivationLinkCreatedAt(LocalDateTime activationLinkCreatedAt) {
        this.activationLinkCreatedAt = activationLinkCreatedAt;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link_expires_at</code>.
     */
    public LocalDateTime getActivationLinkExpiresAt() {
        return this.activationLinkExpiresAt;
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link_expires_at</code>.
     */
    public IndividualVerificationAudit setActivationLinkExpiresAt(LocalDateTime activationLinkExpiresAt) {
        this.activationLinkExpiresAt = activationLinkExpiresAt;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.activation_link_verified_at</code>.
     */
    public LocalDateTime getActivationLinkVerifiedAt() {
        return this.activationLinkVerifiedAt;
    }

    /**
     * Setter for <code>individual_verification_audit.activation_link_verified_at</code>.
     */
    public IndividualVerificationAudit setActivationLinkVerifiedAt(LocalDateTime activationLinkVerifiedAt) {
        this.activationLinkVerifiedAt = activationLinkVerifiedAt;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.ip_address</code>.
     */
    public String getIpAddress() {
        return this.ipAddress;
    }

    /**
     * Setter for <code>individual_verification_audit.ip_address</code>.
     */
    public IndividualVerificationAudit setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.role_id</code>.
     */
    public UUID getRoleId() {
        return this.roleId;
    }

    /**
     * Setter for <code>individual_verification_audit.role_id</code>.
     */
    public IndividualVerificationAudit setRoleId(UUID roleId) {
        this.roleId = roleId;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.verification_status</code>.
     */
    public String getVerificationStatus() {
        return this.verificationStatus;
    }

    /**
     * Setter for <code>individual_verification_audit.verification_status</code>.
     */
    public IndividualVerificationAudit setVerificationStatus(String verificationStatus) {
        this.verificationStatus = verificationStatus;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>individual_verification_audit.is_active</code>.
     */
    public IndividualVerificationAudit setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>individual_verification_audit.created_by</code>.
     */
    public IndividualVerificationAudit setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>individual_verification_audit.updated_by</code>.
     */
    public IndividualVerificationAudit setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>individual_verification_audit.created_on</code>.
     */
    public IndividualVerificationAudit setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>individual_verification_audit.updated_on</code>.
     */
    public IndividualVerificationAudit setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.meta_data</code>.
     */
    public IndividualVerificationMetaData getMetaData() {
        return this.metaData;
    }

    /**
     * Setter for <code>individual_verification_audit.meta_data</code>.
     */
    public IndividualVerificationAudit setMetaData(IndividualVerificationMetaData metaData) {
        this.metaData = metaData;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>individual_verification_audit.org_id</code>.
     */
    public IndividualVerificationAudit setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_code</code>.
     */
    public String getOtpCode() {
        return this.otpCode;
    }

    /**
     * Setter for <code>individual_verification_audit.otp_code</code>.
     */
    public IndividualVerificationAudit setOtpCode(String otpCode) {
        this.otpCode = otpCode;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_created_at</code>.
     */
    public LocalDateTime getOtpCreatedAt() {
        return this.otpCreatedAt;
    }

    /**
     * Setter for <code>individual_verification_audit.otp_created_at</code>.
     */
    public IndividualVerificationAudit setOtpCreatedAt(LocalDateTime otpCreatedAt) {
        this.otpCreatedAt = otpCreatedAt;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_expires_at</code>.
     */
    public LocalDateTime getOtpExpiresAt() {
        return this.otpExpiresAt;
    }

    /**
     * Setter for <code>individual_verification_audit.otp_expires_at</code>.
     */
    public IndividualVerificationAudit setOtpExpiresAt(LocalDateTime otpExpiresAt) {
        this.otpExpiresAt = otpExpiresAt;
        return this;
    }

    /**
     * Getter for <code>individual_verification_audit.otp_verified_at</code>.
     */
    public LocalDateTime getOtpVerifiedAt() {
        return this.otpVerifiedAt;
    }

    /**
     * Setter for <code>individual_verification_audit.otp_verified_at</code>.
     */
    public IndividualVerificationAudit setOtpVerifiedAt(LocalDateTime otpVerifiedAt) {
        this.otpVerifiedAt = otpVerifiedAt;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final IndividualVerificationAudit other = (IndividualVerificationAudit) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (contactType == null) {
            if (other.contactType != null)
                return false;
        }
        else if (!contactType.equals(other.contactType))
            return false;
        if (contactValue == null) {
            if (other.contactValue != null)
                return false;
        }
        else if (!contactValue.equals(other.contactValue))
            return false;
        if (activationLink == null) {
            if (other.activationLink != null)
                return false;
        }
        else if (!activationLink.equals(other.activationLink))
            return false;
        if (activationLinkCreatedAt == null) {
            if (other.activationLinkCreatedAt != null)
                return false;
        }
        else if (!activationLinkCreatedAt.equals(other.activationLinkCreatedAt))
            return false;
        if (activationLinkExpiresAt == null) {
            if (other.activationLinkExpiresAt != null)
                return false;
        }
        else if (!activationLinkExpiresAt.equals(other.activationLinkExpiresAt))
            return false;
        if (activationLinkVerifiedAt == null) {
            if (other.activationLinkVerifiedAt != null)
                return false;
        }
        else if (!activationLinkVerifiedAt.equals(other.activationLinkVerifiedAt))
            return false;
        if (ipAddress == null) {
            if (other.ipAddress != null)
                return false;
        }
        else if (!ipAddress.equals(other.ipAddress))
            return false;
        if (roleId == null) {
            if (other.roleId != null)
                return false;
        }
        else if (!roleId.equals(other.roleId))
            return false;
        if (verificationStatus == null) {
            if (other.verificationStatus != null)
                return false;
        }
        else if (!verificationStatus.equals(other.verificationStatus))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (metaData == null) {
            if (other.metaData != null)
                return false;
        }
        else if (!metaData.equals(other.metaData))
            return false;
        if (orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!orgId.equals(other.orgId))
            return false;
        if (otpCode == null) {
            if (other.otpCode != null)
                return false;
        }
        else if (!otpCode.equals(other.otpCode))
            return false;
        if (otpCreatedAt == null) {
            if (other.otpCreatedAt != null)
                return false;
        }
        else if (!otpCreatedAt.equals(other.otpCreatedAt))
            return false;
        if (otpExpiresAt == null) {
            if (other.otpExpiresAt != null)
                return false;
        }
        else if (!otpExpiresAt.equals(other.otpExpiresAt))
            return false;
        if (otpVerifiedAt == null) {
            if (other.otpVerifiedAt != null)
                return false;
        }
        else if (!otpVerifiedAt.equals(other.otpVerifiedAt))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.contactType == null) ? 0 : this.contactType.hashCode());
        result = prime * result + ((this.contactValue == null) ? 0 : this.contactValue.hashCode());
        result = prime * result + ((this.activationLink == null) ? 0 : this.activationLink.hashCode());
        result = prime * result + ((this.activationLinkCreatedAt == null) ? 0 : this.activationLinkCreatedAt.hashCode());
        result = prime * result + ((this.activationLinkExpiresAt == null) ? 0 : this.activationLinkExpiresAt.hashCode());
        result = prime * result + ((this.activationLinkVerifiedAt == null) ? 0 : this.activationLinkVerifiedAt.hashCode());
        result = prime * result + ((this.ipAddress == null) ? 0 : this.ipAddress.hashCode());
        result = prime * result + ((this.roleId == null) ? 0 : this.roleId.hashCode());
        result = prime * result + ((this.verificationStatus == null) ? 0 : this.verificationStatus.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.metaData == null) ? 0 : this.metaData.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        result = prime * result + ((this.otpCode == null) ? 0 : this.otpCode.hashCode());
        result = prime * result + ((this.otpCreatedAt == null) ? 0 : this.otpCreatedAt.hashCode());
        result = prime * result + ((this.otpExpiresAt == null) ? 0 : this.otpExpiresAt.hashCode());
        result = prime * result + ((this.otpVerifiedAt == null) ? 0 : this.otpVerifiedAt.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IndividualVerificationAudit (");

        sb.append(id);
        sb.append(", ").append(contactType);
        sb.append(", ").append(contactValue);
        sb.append(", ").append(activationLink);
        sb.append(", ").append(activationLinkCreatedAt);
        sb.append(", ").append(activationLinkExpiresAt);
        sb.append(", ").append(activationLinkVerifiedAt);
        sb.append(", ").append(ipAddress);
        sb.append(", ").append(roleId);
        sb.append(", ").append(verificationStatus);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(metaData);
        sb.append(", ").append(orgId);
        sb.append(", ").append(otpCode);
        sb.append(", ").append(otpCreatedAt);
        sb.append(", ").append(otpExpiresAt);
        sb.append(", ").append(otpVerifiedAt);

        sb.append(")");
        return sb.toString();
    }
}
