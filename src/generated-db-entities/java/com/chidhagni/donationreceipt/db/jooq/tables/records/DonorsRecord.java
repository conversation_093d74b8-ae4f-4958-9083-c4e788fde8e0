/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.Donors;
import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorsRecord extends UpdatableRecordImpl<DonorsRecord> implements Record14<UUID, UUID, String, String, String, String, DonorMetaData, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>donors.id</code>.
     */
    public DonorsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>donors.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>donors.tenant_org_id</code>.
     */
    public DonorsRecord setTenantOrgId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>donors.tenant_org_id</code>.
     */
    public UUID getTenantOrgId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>donors.name</code>.
     */
    public DonorsRecord setName(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>donors.name</code>.
     */
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>donors.email</code>.
     */
    public DonorsRecord setEmail(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>donors.email</code>.
     */
    public String getEmail() {
        return (String) get(3);
    }

    /**
     * Setter for <code>donors.mobile_number</code>.
     */
    public DonorsRecord setMobileNumber(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>donors.mobile_number</code>.
     */
    public String getMobileNumber() {
        return (String) get(4);
    }

    /**
     * Setter for <code>donors.pan_no</code>.
     */
    public DonorsRecord setPanNo(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>donors.pan_no</code>.
     */
    public String getPanNo() {
        return (String) get(5);
    }

    /**
     * Setter for <code>donors.meta_data</code>.
     */
    public DonorsRecord setMetaData(DonorMetaData value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>donors.meta_data</code>.
     */
    public DonorMetaData getMetaData() {
        return (DonorMetaData) get(6);
    }

    /**
     * Setter for <code>donors.is_active</code>.
     */
    public DonorsRecord setIsActive(Boolean value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>donors.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(7);
    }

    /**
     * Setter for <code>donors.created_by</code>.
     */
    public DonorsRecord setCreatedBy(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>donors.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>donors.updated_by</code>.
     */
    public DonorsRecord setUpdatedBy(UUID value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>donors.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>donors.created_on</code>.
     */
    public DonorsRecord setCreatedOn(LocalDateTime value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>donors.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>donors.updated_on</code>.
     */
    public DonorsRecord setUpdatedOn(LocalDateTime value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>donors.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>donors.encrypted_pan_no</code>.
     */
    public DonorsRecord setEncryptedPanNo(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>donors.encrypted_pan_no</code>.
     */
    public String getEncryptedPanNo() {
        return (String) get(12);
    }

    /**
     * Setter for <code>donors.pan_no_nonce</code>.
     */
    public DonorsRecord setPanNoNonce(String value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>donors.pan_no_nonce</code>.
     */
    public String getPanNoNonce() {
        return (String) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, UUID, String, String, String, String, DonorMetaData, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, String> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<UUID, UUID, String, String, String, String, DonorMetaData, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, String> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Donors.DONORS.ID;
    }

    @Override
    public Field<UUID> field2() {
        return Donors.DONORS.TENANT_ORG_ID;
    }

    @Override
    public Field<String> field3() {
        return Donors.DONORS.NAME;
    }

    @Override
    public Field<String> field4() {
        return Donors.DONORS.EMAIL;
    }

    @Override
    public Field<String> field5() {
        return Donors.DONORS.MOBILE_NUMBER;
    }

    @Override
    public Field<String> field6() {
        return Donors.DONORS.PAN_NO;
    }

    @Override
    public Field<DonorMetaData> field7() {
        return Donors.DONORS.META_DATA;
    }

    @Override
    public Field<Boolean> field8() {
        return Donors.DONORS.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field9() {
        return Donors.DONORS.CREATED_BY;
    }

    @Override
    public Field<UUID> field10() {
        return Donors.DONORS.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return Donors.DONORS.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return Donors.DONORS.UPDATED_ON;
    }

    @Override
    public Field<String> field13() {
        return Donors.DONORS.ENCRYPTED_PAN_NO;
    }

    @Override
    public Field<String> field14() {
        return Donors.DONORS.PAN_NO_NONCE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getTenantOrgId();
    }

    @Override
    public String component3() {
        return getName();
    }

    @Override
    public String component4() {
        return getEmail();
    }

    @Override
    public String component5() {
        return getMobileNumber();
    }

    @Override
    public String component6() {
        return getPanNo();
    }

    @Override
    public DonorMetaData component7() {
        return getMetaData();
    }

    @Override
    public Boolean component8() {
        return getIsActive();
    }

    @Override
    public UUID component9() {
        return getCreatedBy();
    }

    @Override
    public UUID component10() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component11() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component12() {
        return getUpdatedOn();
    }

    @Override
    public String component13() {
        return getEncryptedPanNo();
    }

    @Override
    public String component14() {
        return getPanNoNonce();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getTenantOrgId();
    }

    @Override
    public String value3() {
        return getName();
    }

    @Override
    public String value4() {
        return getEmail();
    }

    @Override
    public String value5() {
        return getMobileNumber();
    }

    @Override
    public String value6() {
        return getPanNo();
    }

    @Override
    public DonorMetaData value7() {
        return getMetaData();
    }

    @Override
    public Boolean value8() {
        return getIsActive();
    }

    @Override
    public UUID value9() {
        return getCreatedBy();
    }

    @Override
    public UUID value10() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value11() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value12() {
        return getUpdatedOn();
    }

    @Override
    public String value13() {
        return getEncryptedPanNo();
    }

    @Override
    public String value14() {
        return getPanNoNonce();
    }

    @Override
    public DonorsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public DonorsRecord value2(UUID value) {
        setTenantOrgId(value);
        return this;
    }

    @Override
    public DonorsRecord value3(String value) {
        setName(value);
        return this;
    }

    @Override
    public DonorsRecord value4(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public DonorsRecord value5(String value) {
        setMobileNumber(value);
        return this;
    }

    @Override
    public DonorsRecord value6(String value) {
        setPanNo(value);
        return this;
    }

    @Override
    public DonorsRecord value7(DonorMetaData value) {
        setMetaData(value);
        return this;
    }

    @Override
    public DonorsRecord value8(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public DonorsRecord value9(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public DonorsRecord value10(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public DonorsRecord value11(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public DonorsRecord value12(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public DonorsRecord value13(String value) {
        setEncryptedPanNo(value);
        return this;
    }

    @Override
    public DonorsRecord value14(String value) {
        setPanNoNonce(value);
        return this;
    }

    @Override
    public DonorsRecord values(UUID value1, UUID value2, String value3, String value4, String value5, String value6, DonorMetaData value7, Boolean value8, UUID value9, UUID value10, LocalDateTime value11, LocalDateTime value12, String value13, String value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DonorsRecord
     */
    public DonorsRecord() {
        super(Donors.DONORS);
    }

    /**
     * Create a detached, initialised DonorsRecord
     */
    public DonorsRecord(UUID id, UUID tenantOrgId, String name, String email, String mobileNumber, String panNo, DonorMetaData metaData, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, String encryptedPanNo, String panNoNonce) {
        super(Donors.DONORS);

        setId(id);
        setTenantOrgId(tenantOrgId);
        setName(name);
        setEmail(email);
        setMobileNumber(mobileNumber);
        setPanNo(panNo);
        setMetaData(metaData);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setEncryptedPanNo(encryptedPanNo);
        setPanNoNonce(panNoNonce);
    }
}
