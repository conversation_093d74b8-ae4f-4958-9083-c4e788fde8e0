/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ImportBatchRecord extends UpdatableRecordImpl<ImportBatchRecord> implements Record12<UUID, UUID, String, String, Integer, Integer, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>import_batch.id</code>.
     */
    public ImportBatchRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>import_batch.tenant_org_id</code>.
     */
    public ImportBatchRecord setTenantOrgId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.tenant_org_id</code>.
     */
    public UUID getTenantOrgId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>import_batch.category</code>.
     */
    public ImportBatchRecord setCategory(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.category</code>.
     */
    public String getCategory() {
        return (String) get(2);
    }

    /**
     * Setter for <code>import_batch.file_name</code>.
     */
    public ImportBatchRecord setFileName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.file_name</code>.
     */
    public String getFileName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>import_batch.total_records</code>.
     */
    public ImportBatchRecord setTotalRecords(Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.total_records</code>.
     */
    public Integer getTotalRecords() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>import_batch.processed_records</code>.
     */
    public ImportBatchRecord setProcessedRecords(Integer value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.processed_records</code>.
     */
    public Integer getProcessedRecords() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>import_batch.status</code>.
     */
    public ImportBatchRecord setStatus(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.status</code>.
     */
    public String getStatus() {
        return (String) get(6);
    }

    /**
     * Setter for <code>import_batch.created_on</code>.
     */
    public ImportBatchRecord setCreatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>import_batch.updated_on</code>.
     */
    public ImportBatchRecord setUpdatedOn(LocalDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>import_batch.created_by</code>.
     */
    public ImportBatchRecord setCreatedBy(UUID value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>import_batch.updated_by</code>.
     */
    public ImportBatchRecord setUpdatedBy(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(10);
    }

    /**
     * Setter for <code>import_batch.is_active</code>.
     */
    public ImportBatchRecord setIsActive(Boolean value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>import_batch.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, UUID, String, String, Integer, Integer, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<UUID, UUID, String, String, Integer, Integer, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ImportBatch.IMPORT_BATCH.ID;
    }

    @Override
    public Field<UUID> field2() {
        return ImportBatch.IMPORT_BATCH.TENANT_ORG_ID;
    }

    @Override
    public Field<String> field3() {
        return ImportBatch.IMPORT_BATCH.CATEGORY;
    }

    @Override
    public Field<String> field4() {
        return ImportBatch.IMPORT_BATCH.FILE_NAME;
    }

    @Override
    public Field<Integer> field5() {
        return ImportBatch.IMPORT_BATCH.TOTAL_RECORDS;
    }

    @Override
    public Field<Integer> field6() {
        return ImportBatch.IMPORT_BATCH.PROCESSED_RECORDS;
    }

    @Override
    public Field<String> field7() {
        return ImportBatch.IMPORT_BATCH.STATUS;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return ImportBatch.IMPORT_BATCH.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return ImportBatch.IMPORT_BATCH.UPDATED_ON;
    }

    @Override
    public Field<UUID> field10() {
        return ImportBatch.IMPORT_BATCH.CREATED_BY;
    }

    @Override
    public Field<UUID> field11() {
        return ImportBatch.IMPORT_BATCH.UPDATED_BY;
    }

    @Override
    public Field<Boolean> field12() {
        return ImportBatch.IMPORT_BATCH.IS_ACTIVE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getTenantOrgId();
    }

    @Override
    public String component3() {
        return getCategory();
    }

    @Override
    public String component4() {
        return getFileName();
    }

    @Override
    public Integer component5() {
        return getTotalRecords();
    }

    @Override
    public Integer component6() {
        return getProcessedRecords();
    }

    @Override
    public String component7() {
        return getStatus();
    }

    @Override
    public LocalDateTime component8() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component9() {
        return getUpdatedOn();
    }

    @Override
    public UUID component10() {
        return getCreatedBy();
    }

    @Override
    public UUID component11() {
        return getUpdatedBy();
    }

    @Override
    public Boolean component12() {
        return getIsActive();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getTenantOrgId();
    }

    @Override
    public String value3() {
        return getCategory();
    }

    @Override
    public String value4() {
        return getFileName();
    }

    @Override
    public Integer value5() {
        return getTotalRecords();
    }

    @Override
    public Integer value6() {
        return getProcessedRecords();
    }

    @Override
    public String value7() {
        return getStatus();
    }

    @Override
    public LocalDateTime value8() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value9() {
        return getUpdatedOn();
    }

    @Override
    public UUID value10() {
        return getCreatedBy();
    }

    @Override
    public UUID value11() {
        return getUpdatedBy();
    }

    @Override
    public Boolean value12() {
        return getIsActive();
    }

    @Override
    public ImportBatchRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ImportBatchRecord value2(UUID value) {
        setTenantOrgId(value);
        return this;
    }

    @Override
    public ImportBatchRecord value3(String value) {
        setCategory(value);
        return this;
    }

    @Override
    public ImportBatchRecord value4(String value) {
        setFileName(value);
        return this;
    }

    @Override
    public ImportBatchRecord value5(Integer value) {
        setTotalRecords(value);
        return this;
    }

    @Override
    public ImportBatchRecord value6(Integer value) {
        setProcessedRecords(value);
        return this;
    }

    @Override
    public ImportBatchRecord value7(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public ImportBatchRecord value8(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public ImportBatchRecord value9(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public ImportBatchRecord value10(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public ImportBatchRecord value11(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public ImportBatchRecord value12(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public ImportBatchRecord values(UUID value1, UUID value2, String value3, String value4, Integer value5, Integer value6, String value7, LocalDateTime value8, LocalDateTime value9, UUID value10, UUID value11, Boolean value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ImportBatchRecord
     */
    public ImportBatchRecord() {
        super(ImportBatch.IMPORT_BATCH);
    }

    /**
     * Create a detached, initialised ImportBatchRecord
     */
    public ImportBatchRecord(UUID id, UUID tenantOrgId, String category, String fileName, Integer totalRecords, Integer processedRecords, String status, LocalDateTime createdOn, LocalDateTime updatedOn, UUID createdBy, UUID updatedBy, Boolean isActive) {
        super(ImportBatch.IMPORT_BATCH);

        setId(id);
        setTenantOrgId(tenantOrgId);
        setCategory(category);
        setFileName(fileName);
        setTotalRecords(totalRecords);
        setProcessedRecords(processedRecords);
        setStatus(status);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setIsActive(isActive);
    }
}
