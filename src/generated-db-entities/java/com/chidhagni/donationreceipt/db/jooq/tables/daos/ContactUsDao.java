/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.ContactUs;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ContactUsRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ContactUsDao extends DAOImpl<ContactUsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs, UUID> {

    /**
     * Create a new ContactUsDao without any configuration
     */
    public ContactUsDao() {
        super(ContactUs.CONTACT_US, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs.class);
    }

    /**
     * Create a new ContactUsDao with an attached configuration
     */
    @Autowired
    public ContactUsDao(Configuration configuration) {
        super(ContactUs.CONTACT_US, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchById(UUID... values) {
        return fetch(ContactUs.CONTACT_US.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs fetchOneById(UUID value) {
        return fetchOne(ContactUs.CONTACT_US.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByName(String... values) {
        return fetch(ContactUs.CONTACT_US.NAME, values);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByEmail(String... values) {
        return fetch(ContactUs.CONTACT_US.EMAIL, values);
    }

    /**
     * Fetch records that have <code>contact_number BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfContactNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.CONTACT_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>contact_number IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByContactNumber(String... values) {
        return fetch(ContactUs.CONTACT_US.CONTACT_NUMBER, values);
    }

    /**
     * Fetch records that have <code>message BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfMessage(String lowerInclusive, String upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.MESSAGE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>message IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByMessage(String... values) {
        return fetch(ContactUs.CONTACT_US.MESSAGE, values);
    }

    /**
     * Fetch records that have <code>ip_address BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfIpAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.IP_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ip_address IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByIpAddress(String... values) {
        return fetch(ContactUs.CONTACT_US.IP_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(ContactUs.CONTACT_US.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ContactUs.CONTACT_US.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(ContactUs.CONTACT_US.UPDATED_ON, values);
    }
}
