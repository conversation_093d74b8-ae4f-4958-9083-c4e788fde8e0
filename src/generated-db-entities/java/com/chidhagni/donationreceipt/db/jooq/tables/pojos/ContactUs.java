/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ContactUs implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        name;
    private String        email;
    private String        contactNumber;
    private String        message;
    private String        ipAddress;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;

    public ContactUs() {}

    public ContactUs(ContactUs value) {
        this.id = value.id;
        this.name = value.name;
        this.email = value.email;
        this.contactNumber = value.contactNumber;
        this.message = value.message;
        this.ipAddress = value.ipAddress;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
    }

    public ContactUs(
        UUID          id,
        String        name,
        String        email,
        String        contactNumber,
        String        message,
        String        ipAddress,
        LocalDateTime createdOn,
        LocalDateTime updatedOn
    ) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.contactNumber = contactNumber;
        this.message = message;
        this.ipAddress = ipAddress;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
    }

    /**
     * Getter for <code>contact_us.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>contact_us.id</code>.
     */
    public ContactUs setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>contact_us.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>contact_us.name</code>.
     */
    public ContactUs setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>contact_us.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>contact_us.email</code>.
     */
    public ContactUs setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>contact_us.contact_number</code>.
     */
    public String getContactNumber() {
        return this.contactNumber;
    }

    /**
     * Setter for <code>contact_us.contact_number</code>.
     */
    public ContactUs setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
        return this;
    }

    /**
     * Getter for <code>contact_us.message</code>.
     */
    public String getMessage() {
        return this.message;
    }

    /**
     * Setter for <code>contact_us.message</code>.
     */
    public ContactUs setMessage(String message) {
        this.message = message;
        return this;
    }

    /**
     * Getter for <code>contact_us.ip_address</code>.
     */
    public String getIpAddress() {
        return this.ipAddress;
    }

    /**
     * Setter for <code>contact_us.ip_address</code>.
     */
    public ContactUs setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    /**
     * Getter for <code>contact_us.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>contact_us.created_on</code>.
     */
    public ContactUs setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>contact_us.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>contact_us.updated_on</code>.
     */
    public ContactUs setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final ContactUs other = (ContactUs) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (email == null) {
            if (other.email != null)
                return false;
        }
        else if (!email.equals(other.email))
            return false;
        if (contactNumber == null) {
            if (other.contactNumber != null)
                return false;
        }
        else if (!contactNumber.equals(other.contactNumber))
            return false;
        if (message == null) {
            if (other.message != null)
                return false;
        }
        else if (!message.equals(other.message))
            return false;
        if (ipAddress == null) {
            if (other.ipAddress != null)
                return false;
        }
        else if (!ipAddress.equals(other.ipAddress))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.contactNumber == null) ? 0 : this.contactNumber.hashCode());
        result = prime * result + ((this.message == null) ? 0 : this.message.hashCode());
        result = prime * result + ((this.ipAddress == null) ? 0 : this.ipAddress.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ContactUs (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(email);
        sb.append(", ").append(contactNumber);
        sb.append(", ").append(message);
        sb.append(", ").append(ipAddress);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);

        sb.append(")");
        return sb.toString();
    }
}
