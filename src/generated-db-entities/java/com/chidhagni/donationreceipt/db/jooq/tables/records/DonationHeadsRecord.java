/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonationHeadsRecord extends UpdatableRecordImpl<DonationHeadsRecord> implements Record9<UUID, String, String, UUID, LocalDateTime, UUID, LocalDateTime, UUID, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>donation_heads.id</code>.
     */
    public DonationHeadsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>donation_heads.name</code>.
     */
    public DonationHeadsRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>donation_heads.description</code>.
     */
    public DonationHeadsRecord setDescription(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.description</code>.
     */
    public String getDescription() {
        return (String) get(2);
    }

    /**
     * Setter for <code>donation_heads.org_id</code>.
     */
    public DonationHeadsRecord setOrgId(UUID value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(3);
    }

    /**
     * Setter for <code>donation_heads.created_on</code>.
     */
    public DonationHeadsRecord setCreatedOn(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>donation_heads.created_by</code>.
     */
    public DonationHeadsRecord setCreatedBy(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>donation_heads.updated_on</code>.
     */
    public DonationHeadsRecord setUpdatedOn(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>donation_heads.updated_by</code>.
     */
    public DonationHeadsRecord setUpdatedBy(UUID value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>donation_heads.is_active</code>.
     */
    public DonationHeadsRecord setIsActive(Boolean value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>donation_heads.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, String, String, UUID, LocalDateTime, UUID, LocalDateTime, UUID, Boolean> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<UUID, String, String, UUID, LocalDateTime, UUID, LocalDateTime, UUID, Boolean> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return DonationHeads.DONATION_HEADS.ID;
    }

    @Override
    public Field<String> field2() {
        return DonationHeads.DONATION_HEADS.NAME;
    }

    @Override
    public Field<String> field3() {
        return DonationHeads.DONATION_HEADS.DESCRIPTION;
    }

    @Override
    public Field<UUID> field4() {
        return DonationHeads.DONATION_HEADS.ORG_ID;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return DonationHeads.DONATION_HEADS.CREATED_ON;
    }

    @Override
    public Field<UUID> field6() {
        return DonationHeads.DONATION_HEADS.CREATED_BY;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return DonationHeads.DONATION_HEADS.UPDATED_ON;
    }

    @Override
    public Field<UUID> field8() {
        return DonationHeads.DONATION_HEADS.UPDATED_BY;
    }

    @Override
    public Field<Boolean> field9() {
        return DonationHeads.DONATION_HEADS.IS_ACTIVE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getDescription();
    }

    @Override
    public UUID component4() {
        return getOrgId();
    }

    @Override
    public LocalDateTime component5() {
        return getCreatedOn();
    }

    @Override
    public UUID component6() {
        return getCreatedBy();
    }

    @Override
    public LocalDateTime component7() {
        return getUpdatedOn();
    }

    @Override
    public UUID component8() {
        return getUpdatedBy();
    }

    @Override
    public Boolean component9() {
        return getIsActive();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getDescription();
    }

    @Override
    public UUID value4() {
        return getOrgId();
    }

    @Override
    public LocalDateTime value5() {
        return getCreatedOn();
    }

    @Override
    public UUID value6() {
        return getCreatedBy();
    }

    @Override
    public LocalDateTime value7() {
        return getUpdatedOn();
    }

    @Override
    public UUID value8() {
        return getUpdatedBy();
    }

    @Override
    public Boolean value9() {
        return getIsActive();
    }

    @Override
    public DonationHeadsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value3(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value4(UUID value) {
        setOrgId(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value5(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value6(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value7(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value8(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public DonationHeadsRecord value9(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public DonationHeadsRecord values(UUID value1, String value2, String value3, UUID value4, LocalDateTime value5, UUID value6, LocalDateTime value7, UUID value8, Boolean value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DonationHeadsRecord
     */
    public DonationHeadsRecord() {
        super(DonationHeads.DONATION_HEADS);
    }

    /**
     * Create a detached, initialised DonationHeadsRecord
     */
    public DonationHeadsRecord(UUID id, String name, String description, UUID orgId, LocalDateTime createdOn, UUID createdBy, LocalDateTime updatedOn, UUID updatedBy, Boolean isActive) {
        super(DonationHeads.DONATION_HEADS);

        setId(id);
        setName(name);
        setDescription(description);
        setOrgId(orgId);
        setCreatedOn(createdOn);
        setCreatedBy(createdBy);
        setUpdatedOn(updatedOn);
        setUpdatedBy(updatedBy);
        setIsActive(isActive);
    }
}
