/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualPasswordResetAuditRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualPasswordResetAudit extends TableImpl<IndividualPasswordResetAuditRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>individual_password_reset_audit</code>
     */
    public static final IndividualPasswordResetAudit INDIVIDUAL_PASSWORD_RESET_AUDIT = new IndividualPasswordResetAudit();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IndividualPasswordResetAuditRecord> getRecordType() {
        return IndividualPasswordResetAuditRecord.class;
    }

    /**
     * The column <code>individual_password_reset_audit.id</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_password_reset_audit.individual_id</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, UUID> INDIVIDUAL_ID = createField(DSL.name("individual_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_password_reset_audit.email</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(200).nullable(false), this, "");

    /**
     * The column <code>individual_password_reset_audit.reset_link</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, String> RESET_LINK = createField(DSL.name("reset_link"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>individual_password_reset_audit.reset_link_requested_at</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, LocalDateTime> RESET_LINK_REQUESTED_AT = createField(DSL.name("reset_link_requested_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_password_reset_audit.reset_link_expires_at</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, LocalDateTime> RESET_LINK_EXPIRES_AT = createField(DSL.name("reset_link_expires_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_password_reset_audit.reset_completed_at</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, LocalDateTime> RESET_COMPLETED_AT = createField(DSL.name("reset_completed_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_password_reset_audit.reset_status</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, String> RESET_STATUS = createField(DSL.name("reset_status"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>individual_password_reset_audit.is_active</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN, this, "");

    /**
     * The column <code>individual_password_reset_audit.created_by</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_password_reset_audit.updated_by</code>.
     */
    public final TableField<IndividualPasswordResetAuditRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    private IndividualPasswordResetAudit(Name alias, Table<IndividualPasswordResetAuditRecord> aliased) {
        this(alias, aliased, null);
    }

    private IndividualPasswordResetAudit(Name alias, Table<IndividualPasswordResetAuditRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>individual_password_reset_audit</code> table reference
     */
    public IndividualPasswordResetAudit(String alias) {
        this(DSL.name(alias), INDIVIDUAL_PASSWORD_RESET_AUDIT);
    }

    /**
     * Create an aliased <code>individual_password_reset_audit</code> table reference
     */
    public IndividualPasswordResetAudit(Name alias) {
        this(alias, INDIVIDUAL_PASSWORD_RESET_AUDIT);
    }

    /**
     * Create a <code>individual_password_reset_audit</code> table reference
     */
    public IndividualPasswordResetAudit() {
        this(DSL.name("individual_password_reset_audit"), null);
    }

    public <O extends Record> IndividualPasswordResetAudit(Table<O> child, ForeignKey<O, IndividualPasswordResetAuditRecord> key) {
        super(child, key, INDIVIDUAL_PASSWORD_RESET_AUDIT);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<IndividualPasswordResetAuditRecord> getPrimaryKey() {
        return Keys.INDIVIDUAL_PASSWORD_RESET_AUDIT_PKEY;
    }

    @Override
    public List<UniqueKey<IndividualPasswordResetAuditRecord>> getKeys() {
        return Arrays.<UniqueKey<IndividualPasswordResetAuditRecord>>asList(Keys.INDIVIDUAL_PASSWORD_RESET_AUDIT_PKEY);
    }

    @Override
    public List<ForeignKey<IndividualPasswordResetAuditRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<IndividualPasswordResetAuditRecord, ?>>asList(Keys.INDIVIDUAL_PASSWORD_RESET_AUDIT__INDIVIDUAL_PASSWORD_RESET_AUDIT_INDIVIDUAL_ID_FKEY);
    }

    private transient Individual _individual;

    public Individual individual() {
        if (_individual == null)
            _individual = new Individual(this, Keys.INDIVIDUAL_PASSWORD_RESET_AUDIT__INDIVIDUAL_PASSWORD_RESET_AUDIT_INDIVIDUAL_ID_FKEY);

        return _individual;
    }

    @Override
    public IndividualPasswordResetAudit as(String alias) {
        return new IndividualPasswordResetAudit(DSL.name(alias), this);
    }

    @Override
    public IndividualPasswordResetAudit as(Name alias) {
        return new IndividualPasswordResetAudit(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualPasswordResetAudit rename(String name) {
        return new IndividualPasswordResetAudit(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualPasswordResetAudit rename(Name name) {
        return new IndividualPasswordResetAudit(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, UUID, String, String, LocalDateTime, LocalDateTime, LocalDateTime, String, Boolean, UUID, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
