/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.PaymentsRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Payments extends TableImpl<PaymentsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>payments</code>
     */
    public static final Payments PAYMENTS = new Payments();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaymentsRecord> getRecordType() {
        return PaymentsRecord.class;
    }

    /**
     * The column <code>payments.id</code>.
     */
    public final TableField<PaymentsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>payments.amount</code>.
     */
    public final TableField<PaymentsRecord, BigDecimal> AMOUNT = createField(DSL.name("amount"), SQLDataType.NUMERIC(15, 2).nullable(false), this, "");

    /**
     * The column <code>payments.payment_status</code>.
     */
    public final TableField<PaymentsRecord, String> PAYMENT_STATUS = createField(DSL.name("payment_status"), SQLDataType.VARCHAR(10), this, "");

    /**
     * The column <code>payments.razorpay_payment_method</code>.
     */
    public final TableField<PaymentsRecord, String> RAZORPAY_PAYMENT_METHOD = createField(DSL.name("razorpay_payment_method"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>payments.order_id</code>.
     */
    public final TableField<PaymentsRecord, String> ORDER_ID = createField(DSL.name("order_id"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>payments.payment_date</code>.
     */
    public final TableField<PaymentsRecord, LocalDateTime> PAYMENT_DATE = createField(DSL.name("payment_date"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>payments.donation_receipt_id</code>.
     */
    public final TableField<PaymentsRecord, UUID> DONATION_RECEIPT_ID = createField(DSL.name("donation_receipt_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>payments.is_active</code>.
     */
    public final TableField<PaymentsRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>payments.payment_type</code>.
     */
    public final TableField<PaymentsRecord, UUID> PAYMENT_TYPE = createField(DSL.name("payment_type"), SQLDataType.UUID, this, "");

    /**
     * The column <code>payments.created_by</code>.
     */
    public final TableField<PaymentsRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>payments.updated_by</code>.
     */
    public final TableField<PaymentsRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>payments.created_on</code>.
     */
    public final TableField<PaymentsRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>payments.updated_on</code>.
     */
    public final TableField<PaymentsRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>payments.razorpay_payment_id</code>.
     */
    public final TableField<PaymentsRecord, String> RAZORPAY_PAYMENT_ID = createField(DSL.name("razorpay_payment_id"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>payments.failure_description</code>.
     */
    public final TableField<PaymentsRecord, String> FAILURE_DESCRIPTION = createField(DSL.name("failure_description"), SQLDataType.VARCHAR(500), this, "");

    private Payments(Name alias, Table<PaymentsRecord> aliased) {
        this(alias, aliased, null);
    }

    private Payments(Name alias, Table<PaymentsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>payments</code> table reference
     */
    public Payments(String alias) {
        this(DSL.name(alias), PAYMENTS);
    }

    /**
     * Create an aliased <code>payments</code> table reference
     */
    public Payments(Name alias) {
        this(alias, PAYMENTS);
    }

    /**
     * Create a <code>payments</code> table reference
     */
    public Payments() {
        this(DSL.name("payments"), null);
    }

    public <O extends Record> Payments(Table<O> child, ForeignKey<O, PaymentsRecord> key) {
        super(child, key, PAYMENTS);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<PaymentsRecord> getPrimaryKey() {
        return Keys.PAYMENTS_PKEY;
    }

    @Override
    public List<UniqueKey<PaymentsRecord>> getKeys() {
        return Arrays.<UniqueKey<PaymentsRecord>>asList(Keys.PAYMENTS_PKEY);
    }

    @Override
    public List<ForeignKey<PaymentsRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<PaymentsRecord, ?>>asList(Keys.PAYMENTS__PAYMENTS_DONATION_RECEIPT_ID_FKEY);
    }

    private transient DonationReceipts _donationReceipts;

    public DonationReceipts donationReceipts() {
        if (_donationReceipts == null)
            _donationReceipts = new DonationReceipts(this, Keys.PAYMENTS__PAYMENTS_DONATION_RECEIPT_ID_FKEY);

        return _donationReceipts;
    }

    @Override
    public Payments as(String alias) {
        return new Payments(DSL.name(alias), this);
    }

    @Override
    public Payments as(Name alias) {
        return new Payments(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Payments rename(String name) {
        return new Payments(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Payments rename(Name name) {
        return new Payments(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, BigDecimal, String, String, String, LocalDateTime, UUID, Boolean, UUID, UUID, UUID, LocalDateTime, LocalDateTime, String, String> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
