/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualRoleRecord extends UpdatableRecordImpl<IndividualRoleRecord> implements Record8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>individual_role.id</code>.
     */
    public IndividualRoleRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>individual_role.individual_id</code>.
     */
    public IndividualRoleRecord setIndividualId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.individual_id</code>.
     */
    public UUID getIndividualId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>individual_role.role_id</code>.
     */
    public IndividualRoleRecord setRoleId(UUID value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.role_id</code>.
     */
    public UUID getRoleId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>individual_role.created_on</code>.
     */
    public IndividualRoleRecord setCreatedOn(LocalDateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>individual_role.updated_on</code>.
     */
    public IndividualRoleRecord setUpdatedOn(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>individual_role.created_by</code>.
     */
    public IndividualRoleRecord setCreatedBy(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>individual_role.updated_by</code>.
     */
    public IndividualRoleRecord setUpdatedBy(UUID value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>individual_role.org_id</code>.
     */
    public IndividualRoleRecord setOrgId(UUID value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>individual_role.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, UUID> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return IndividualRole.INDIVIDUAL_ROLE.ID;
    }

    @Override
    public Field<UUID> field2() {
        return IndividualRole.INDIVIDUAL_ROLE.INDIVIDUAL_ID;
    }

    @Override
    public Field<UUID> field3() {
        return IndividualRole.INDIVIDUAL_ROLE.ROLE_ID;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return IndividualRole.INDIVIDUAL_ROLE.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return IndividualRole.INDIVIDUAL_ROLE.UPDATED_ON;
    }

    @Override
    public Field<UUID> field6() {
        return IndividualRole.INDIVIDUAL_ROLE.CREATED_BY;
    }

    @Override
    public Field<UUID> field7() {
        return IndividualRole.INDIVIDUAL_ROLE.UPDATED_BY;
    }

    @Override
    public Field<UUID> field8() {
        return IndividualRole.INDIVIDUAL_ROLE.ORG_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getIndividualId();
    }

    @Override
    public UUID component3() {
        return getRoleId();
    }

    @Override
    public LocalDateTime component4() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component5() {
        return getUpdatedOn();
    }

    @Override
    public UUID component6() {
        return getCreatedBy();
    }

    @Override
    public UUID component7() {
        return getUpdatedBy();
    }

    @Override
    public UUID component8() {
        return getOrgId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getIndividualId();
    }

    @Override
    public UUID value3() {
        return getRoleId();
    }

    @Override
    public LocalDateTime value4() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value5() {
        return getUpdatedOn();
    }

    @Override
    public UUID value6() {
        return getCreatedBy();
    }

    @Override
    public UUID value7() {
        return getUpdatedBy();
    }

    @Override
    public UUID value8() {
        return getOrgId();
    }

    @Override
    public IndividualRoleRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value2(UUID value) {
        setIndividualId(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value3(UUID value) {
        setRoleId(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value4(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value5(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value6(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value7(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public IndividualRoleRecord value8(UUID value) {
        setOrgId(value);
        return this;
    }

    @Override
    public IndividualRoleRecord values(UUID value1, UUID value2, UUID value3, LocalDateTime value4, LocalDateTime value5, UUID value6, UUID value7, UUID value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IndividualRoleRecord
     */
    public IndividualRoleRecord() {
        super(IndividualRole.INDIVIDUAL_ROLE);
    }

    /**
     * Create a detached, initialised IndividualRoleRecord
     */
    public IndividualRoleRecord(UUID id, UUID individualId, UUID roleId, LocalDateTime createdOn, LocalDateTime updatedOn, UUID createdBy, UUID updatedBy, UUID orgId) {
        super(IndividualRole.INDIVIDUAL_ROLE);

        setId(id);
        setIndividualId(individualId);
        setRoleId(roleId);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setOrgId(orgId);
    }
}
