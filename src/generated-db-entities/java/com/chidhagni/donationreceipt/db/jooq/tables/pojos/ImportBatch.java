/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ImportBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          tenantOrgId;
    private String        category;
    private String        fileName;
    private Integer       totalRecords;
    private Integer       processedRecords;
    private String        status;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID          createdBy;
    private UUID          updatedBy;
    private Boolean       isActive;

    public ImportBatch() {}

    public ImportBatch(ImportBatch value) {
        this.id = value.id;
        this.tenantOrgId = value.tenantOrgId;
        this.category = value.category;
        this.fileName = value.fileName;
        this.totalRecords = value.totalRecords;
        this.processedRecords = value.processedRecords;
        this.status = value.status;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.isActive = value.isActive;
    }

    public ImportBatch(
        UUID          id,
        UUID          tenantOrgId,
        String        category,
        String        fileName,
        Integer       totalRecords,
        Integer       processedRecords,
        String        status,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        UUID          createdBy,
        UUID          updatedBy,
        Boolean       isActive
    ) {
        this.id = id;
        this.tenantOrgId = tenantOrgId;
        this.category = category;
        this.fileName = fileName;
        this.totalRecords = totalRecords;
        this.processedRecords = processedRecords;
        this.status = status;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.isActive = isActive;
    }

    /**
     * Getter for <code>import_batch.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>import_batch.id</code>.
     */
    public ImportBatch setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>import_batch.tenant_org_id</code>.
     */
    public UUID getTenantOrgId() {
        return this.tenantOrgId;
    }

    /**
     * Setter for <code>import_batch.tenant_org_id</code>.
     */
    public ImportBatch setTenantOrgId(UUID tenantOrgId) {
        this.tenantOrgId = tenantOrgId;
        return this;
    }

    /**
     * Getter for <code>import_batch.category</code>.
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>import_batch.category</code>.
     */
    public ImportBatch setCategory(String category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>import_batch.file_name</code>.
     */
    public String getFileName() {
        return this.fileName;
    }

    /**
     * Setter for <code>import_batch.file_name</code>.
     */
    public ImportBatch setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    /**
     * Getter for <code>import_batch.total_records</code>.
     */
    public Integer getTotalRecords() {
        return this.totalRecords;
    }

    /**
     * Setter for <code>import_batch.total_records</code>.
     */
    public ImportBatch setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
        return this;
    }

    /**
     * Getter for <code>import_batch.processed_records</code>.
     */
    public Integer getProcessedRecords() {
        return this.processedRecords;
    }

    /**
     * Setter for <code>import_batch.processed_records</code>.
     */
    public ImportBatch setProcessedRecords(Integer processedRecords) {
        this.processedRecords = processedRecords;
        return this;
    }

    /**
     * Getter for <code>import_batch.status</code>.
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>import_batch.status</code>.
     */
    public ImportBatch setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>import_batch.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>import_batch.created_on</code>.
     */
    public ImportBatch setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>import_batch.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>import_batch.updated_on</code>.
     */
    public ImportBatch setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>import_batch.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>import_batch.created_by</code>.
     */
    public ImportBatch setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>import_batch.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>import_batch.updated_by</code>.
     */
    public ImportBatch setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>import_batch.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>import_batch.is_active</code>.
     */
    public ImportBatch setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final ImportBatch other = (ImportBatch) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (tenantOrgId == null) {
            if (other.tenantOrgId != null)
                return false;
        }
        else if (!tenantOrgId.equals(other.tenantOrgId))
            return false;
        if (category == null) {
            if (other.category != null)
                return false;
        }
        else if (!category.equals(other.category))
            return false;
        if (fileName == null) {
            if (other.fileName != null)
                return false;
        }
        else if (!fileName.equals(other.fileName))
            return false;
        if (totalRecords == null) {
            if (other.totalRecords != null)
                return false;
        }
        else if (!totalRecords.equals(other.totalRecords))
            return false;
        if (processedRecords == null) {
            if (other.processedRecords != null)
                return false;
        }
        else if (!processedRecords.equals(other.processedRecords))
            return false;
        if (status == null) {
            if (other.status != null)
                return false;
        }
        else if (!status.equals(other.status))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.tenantOrgId == null) ? 0 : this.tenantOrgId.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.fileName == null) ? 0 : this.fileName.hashCode());
        result = prime * result + ((this.totalRecords == null) ? 0 : this.totalRecords.hashCode());
        result = prime * result + ((this.processedRecords == null) ? 0 : this.processedRecords.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ImportBatch (");

        sb.append(id);
        sb.append(", ").append(tenantOrgId);
        sb.append(", ").append(category);
        sb.append(", ").append(fileName);
        sb.append(", ").append(totalRecords);
        sb.append(", ").append(processedRecords);
        sb.append(", ").append(status);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(isActive);

        sb.append(")");
        return sb.toString();
    }
}
