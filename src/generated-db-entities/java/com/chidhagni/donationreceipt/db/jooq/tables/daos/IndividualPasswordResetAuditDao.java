/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualPasswordResetAuditRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class IndividualPasswordResetAuditDao extends DAOImpl<IndividualPasswordResetAuditRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit, UUID> {

    /**
     * Create a new IndividualPasswordResetAuditDao without any configuration
     */
    public IndividualPasswordResetAuditDao() {
        super(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit.class);
    }

    /**
     * Create a new IndividualPasswordResetAuditDao with an attached configuration
     */
    @Autowired
    public IndividualPasswordResetAuditDao(Configuration configuration) {
        super(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchById(UUID... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit fetchOneById(UUID value) {
        return fetchOne(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.ID, value);
    }

    /**
     * Fetch records that have <code>individual_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfIndividualId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.INDIVIDUAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>individual_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByIndividualId(UUID... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.INDIVIDUAL_ID, values);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByEmail(String... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.EMAIL, values);
    }

    /**
     * Fetch records that have <code>reset_link BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfResetLink(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_link IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByResetLink(String... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK, values);
    }

    /**
     * Fetch records that have <code>reset_link_requested_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfResetLinkRequestedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK_REQUESTED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_link_requested_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByResetLinkRequestedAt(LocalDateTime... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK_REQUESTED_AT, values);
    }

    /**
     * Fetch records that have <code>reset_link_expires_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfResetLinkExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK_EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_link_expires_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByResetLinkExpiresAt(LocalDateTime... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK_EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>reset_completed_at BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfResetCompletedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_COMPLETED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_completed_at IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByResetCompletedAt(LocalDateTime... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_COMPLETED_AT, values);
    }

    /**
     * Fetch records that have <code>reset_status BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfResetStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_status IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByResetStatus(String... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_STATUS, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByIsActive(Boolean... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByCreatedBy(UUID... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit> fetchByUpdatedBy(UUID... values) {
        return fetch(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.UPDATED_BY, values);
    }
}
