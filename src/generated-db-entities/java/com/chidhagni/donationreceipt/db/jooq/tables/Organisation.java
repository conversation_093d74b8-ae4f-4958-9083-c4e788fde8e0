/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.OrganisationRecord;
import com.chidhagni.donationreceipt.organisation.MetaDataDTO;
import com.chidhagni.donationreceipt.organisation.jooq.OrganisationMetaDataJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Organisation extends TableImpl<OrganisationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>organisation</code>
     */
    public static final Organisation ORGANISATION = new Organisation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OrganisationRecord> getRecordType() {
        return OrganisationRecord.class;
    }

    /**
     * The column <code>organisation.id</code>.
     */
    public final TableField<OrganisationRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>organisation.name</code>.
     */
    public final TableField<OrganisationRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>organisation.category</code>.
     */
    public final TableField<OrganisationRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>organisation.meta_data</code>.
     */
    public final TableField<OrganisationRecord, MetaDataDTO> META_DATA = createField(DSL.name("meta_data"), SQLDataType.JSONB, this, "", new OrganisationMetaDataJsonConverter());

    /**
     * The column <code>organisation.is_active</code>.
     */
    public final TableField<OrganisationRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>organisation.created_by</code>.
     */
    public final TableField<OrganisationRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>organisation.updated_by</code>.
     */
    public final TableField<OrganisationRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>organisation.created_on</code>.
     */
    public final TableField<OrganisationRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>organisation.updated_on</code>.
     */
    public final TableField<OrganisationRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    private Organisation(Name alias, Table<OrganisationRecord> aliased) {
        this(alias, aliased, null);
    }

    private Organisation(Name alias, Table<OrganisationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>organisation</code> table reference
     */
    public Organisation(String alias) {
        this(DSL.name(alias), ORGANISATION);
    }

    /**
     * Create an aliased <code>organisation</code> table reference
     */
    public Organisation(Name alias) {
        this(alias, ORGANISATION);
    }

    /**
     * Create a <code>organisation</code> table reference
     */
    public Organisation() {
        this(DSL.name("organisation"), null);
    }

    public <O extends Record> Organisation(Table<O> child, ForeignKey<O, OrganisationRecord> key) {
        super(child, key, ORGANISATION);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<OrganisationRecord> getPrimaryKey() {
        return Keys.ORGANISATION_PKEY;
    }

    @Override
    public List<UniqueKey<OrganisationRecord>> getKeys() {
        return Arrays.<UniqueKey<OrganisationRecord>>asList(Keys.ORGANISATION_PKEY);
    }

    @Override
    public Organisation as(String alias) {
        return new Organisation(DSL.name(alias), this);
    }

    @Override
    public Organisation as(Name alias) {
        return new Organisation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Organisation rename(String name) {
        return new Organisation(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Organisation rename(Name name) {
        return new Organisation(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, String, String, MetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
