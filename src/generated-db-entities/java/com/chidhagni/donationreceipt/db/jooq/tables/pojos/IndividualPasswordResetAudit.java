/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualPasswordResetAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          individualId;
    private String        email;
    private String        resetLink;
    private LocalDateTime resetLinkRequestedAt;
    private LocalDateTime resetLinkExpiresAt;
    private LocalDateTime resetCompletedAt;
    private String        resetStatus;
    private Boolean       isActive;
    private UUID          createdBy;
    private UUID          updatedBy;

    public IndividualPasswordResetAudit() {}

    public IndividualPasswordResetAudit(IndividualPasswordResetAudit value) {
        this.id = value.id;
        this.individualId = value.individualId;
        this.email = value.email;
        this.resetLink = value.resetLink;
        this.resetLinkRequestedAt = value.resetLinkRequestedAt;
        this.resetLinkExpiresAt = value.resetLinkExpiresAt;
        this.resetCompletedAt = value.resetCompletedAt;
        this.resetStatus = value.resetStatus;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
    }

    public IndividualPasswordResetAudit(
        UUID          id,
        UUID          individualId,
        String        email,
        String        resetLink,
        LocalDateTime resetLinkRequestedAt,
        LocalDateTime resetLinkExpiresAt,
        LocalDateTime resetCompletedAt,
        String        resetStatus,
        Boolean       isActive,
        UUID          createdBy,
        UUID          updatedBy
    ) {
        this.id = id;
        this.individualId = individualId;
        this.email = email;
        this.resetLink = resetLink;
        this.resetLinkRequestedAt = resetLinkRequestedAt;
        this.resetLinkExpiresAt = resetLinkExpiresAt;
        this.resetCompletedAt = resetCompletedAt;
        this.resetStatus = resetStatus;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    /**
     * Getter for <code>individual_password_reset_audit.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>individual_password_reset_audit.id</code>.
     */
    public IndividualPasswordResetAudit setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.individual_id</code>.
     */
    public UUID getIndividualId() {
        return this.individualId;
    }

    /**
     * Setter for <code>individual_password_reset_audit.individual_id</code>.
     */
    public IndividualPasswordResetAudit setIndividualId(UUID individualId) {
        this.individualId = individualId;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>individual_password_reset_audit.email</code>.
     */
    public IndividualPasswordResetAudit setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_link</code>.
     */
    public String getResetLink() {
        return this.resetLink;
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_link</code>.
     */
    public IndividualPasswordResetAudit setResetLink(String resetLink) {
        this.resetLink = resetLink;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_link_requested_at</code>.
     */
    public LocalDateTime getResetLinkRequestedAt() {
        return this.resetLinkRequestedAt;
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_link_requested_at</code>.
     */
    public IndividualPasswordResetAudit setResetLinkRequestedAt(LocalDateTime resetLinkRequestedAt) {
        this.resetLinkRequestedAt = resetLinkRequestedAt;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_link_expires_at</code>.
     */
    public LocalDateTime getResetLinkExpiresAt() {
        return this.resetLinkExpiresAt;
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_link_expires_at</code>.
     */
    public IndividualPasswordResetAudit setResetLinkExpiresAt(LocalDateTime resetLinkExpiresAt) {
        this.resetLinkExpiresAt = resetLinkExpiresAt;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_completed_at</code>.
     */
    public LocalDateTime getResetCompletedAt() {
        return this.resetCompletedAt;
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_completed_at</code>.
     */
    public IndividualPasswordResetAudit setResetCompletedAt(LocalDateTime resetCompletedAt) {
        this.resetCompletedAt = resetCompletedAt;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_status</code>.
     */
    public String getResetStatus() {
        return this.resetStatus;
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_status</code>.
     */
    public IndividualPasswordResetAudit setResetStatus(String resetStatus) {
        this.resetStatus = resetStatus;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>individual_password_reset_audit.is_active</code>.
     */
    public IndividualPasswordResetAudit setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>individual_password_reset_audit.created_by</code>.
     */
    public IndividualPasswordResetAudit setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>individual_password_reset_audit.updated_by</code>.
     */
    public IndividualPasswordResetAudit setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final IndividualPasswordResetAudit other = (IndividualPasswordResetAudit) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (individualId == null) {
            if (other.individualId != null)
                return false;
        }
        else if (!individualId.equals(other.individualId))
            return false;
        if (email == null) {
            if (other.email != null)
                return false;
        }
        else if (!email.equals(other.email))
            return false;
        if (resetLink == null) {
            if (other.resetLink != null)
                return false;
        }
        else if (!resetLink.equals(other.resetLink))
            return false;
        if (resetLinkRequestedAt == null) {
            if (other.resetLinkRequestedAt != null)
                return false;
        }
        else if (!resetLinkRequestedAt.equals(other.resetLinkRequestedAt))
            return false;
        if (resetLinkExpiresAt == null) {
            if (other.resetLinkExpiresAt != null)
                return false;
        }
        else if (!resetLinkExpiresAt.equals(other.resetLinkExpiresAt))
            return false;
        if (resetCompletedAt == null) {
            if (other.resetCompletedAt != null)
                return false;
        }
        else if (!resetCompletedAt.equals(other.resetCompletedAt))
            return false;
        if (resetStatus == null) {
            if (other.resetStatus != null)
                return false;
        }
        else if (!resetStatus.equals(other.resetStatus))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.individualId == null) ? 0 : this.individualId.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.resetLink == null) ? 0 : this.resetLink.hashCode());
        result = prime * result + ((this.resetLinkRequestedAt == null) ? 0 : this.resetLinkRequestedAt.hashCode());
        result = prime * result + ((this.resetLinkExpiresAt == null) ? 0 : this.resetLinkExpiresAt.hashCode());
        result = prime * result + ((this.resetCompletedAt == null) ? 0 : this.resetCompletedAt.hashCode());
        result = prime * result + ((this.resetStatus == null) ? 0 : this.resetStatus.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IndividualPasswordResetAudit (");

        sb.append(id);
        sb.append(", ").append(individualId);
        sb.append(", ").append(email);
        sb.append(", ").append(resetLink);
        sb.append(", ").append(resetLinkRequestedAt);
        sb.append(", ").append(resetLinkExpiresAt);
        sb.append(", ").append(resetCompletedAt);
        sb.append(", ").append(resetStatus);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);

        sb.append(")");
        return sb.toString();
    }
}
