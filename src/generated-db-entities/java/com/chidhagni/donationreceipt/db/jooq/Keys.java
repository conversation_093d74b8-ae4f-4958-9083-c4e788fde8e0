/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq;


import com.chidhagni.donationreceipt.db.jooq.tables.ContactUs;
import com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging;
import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualSessions;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.ListNames;
import com.chidhagni.donationreceipt.db.jooq.tables.ListValues;
import com.chidhagni.donationreceipt.db.jooq.tables.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.Payments;
import com.chidhagni.donationreceipt.db.jooq.tables.Resource;
import com.chidhagni.donationreceipt.db.jooq.tables.Roles;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ContactUsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DocumentRepoRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonationHeadsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonationReceiptsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorGroupMappingRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorGroupsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportBatchRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportStagingRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualPasswordResetAuditRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualPermissionRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualRoleRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualSessionsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualVerificationAuditRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ListNamesRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ListValuesRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.OrganisationRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.PaymentsRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ResourceRecord;
import com.chidhagni.donationreceipt.db.jooq.tables.records.RolesRecord;

import org.jooq.ForeignKey;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in 
 * the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<ContactUsRecord> CONTACT_US_PKEY = Internal.createUniqueKey(ContactUs.CONTACT_US, DSL.name("contact_us_pkey"), new TableField[] { ContactUs.CONTACT_US.ID }, true);
    public static final UniqueKey<DocumentRepoRecord> DOCUMENT_REPO_ID_PK = Internal.createUniqueKey(DocumentRepo.DOCUMENT_REPO, DSL.name("document_repo_id_pk"), new TableField[] { DocumentRepo.DOCUMENT_REPO.ID }, true);
    public static final UniqueKey<DonationHeadsRecord> DONATION_HEADS_PKEY = Internal.createUniqueKey(DonationHeads.DONATION_HEADS, DSL.name("donation_heads_pkey"), new TableField[] { DonationHeads.DONATION_HEADS.ID }, true);
    public static final UniqueKey<DonationReceiptsRecord> DONATION_RECEIPTS_PKEY = Internal.createUniqueKey(DonationReceipts.DONATION_RECEIPTS, DSL.name("donation_receipts_pkey"), new TableField[] { DonationReceipts.DONATION_RECEIPTS.ID }, true);
    public static final UniqueKey<DonorGroupMappingRecord> DONOR_GROUP_MAPPING_PKEY = Internal.createUniqueKey(DonorGroupMapping.DONOR_GROUP_MAPPING, DSL.name("donor_group_mapping_pkey"), new TableField[] { DonorGroupMapping.DONOR_GROUP_MAPPING.ID }, true);
    public static final UniqueKey<DonorGroupsRecord> DONOR_GROUPS_PKEY = Internal.createUniqueKey(DonorGroups.DONOR_GROUPS, DSL.name("donor_groups_pkey"), new TableField[] { DonorGroups.DONOR_GROUPS.ID }, true);
    public static final UniqueKey<DonorsRecord> DONORS_ID_PK = Internal.createUniqueKey(Donors.DONORS, DSL.name("donors_id_pk"), new TableField[] { Donors.DONORS.ID }, true);
    public static final UniqueKey<ImportBatchRecord> IMPORT_BATCH_PKEY = Internal.createUniqueKey(ImportBatch.IMPORT_BATCH, DSL.name("import_batch_pkey"), new TableField[] { ImportBatch.IMPORT_BATCH.ID }, true);
    public static final UniqueKey<ImportStagingRecord> IMPORT_STAGING_PKEY = Internal.createUniqueKey(ImportStaging.IMPORT_STAGING, DSL.name("import_staging_pkey"), new TableField[] { ImportStaging.IMPORT_STAGING.ID }, true);
    public static final UniqueKey<IndividualRecord> INDIVIDUAL_EMAIL_UNIQUE = Internal.createUniqueKey(Individual.INDIVIDUAL, DSL.name("individual_email_unique"), new TableField[] { Individual.INDIVIDUAL.EMAIL }, true);
    public static final UniqueKey<IndividualRecord> INDIVIDUAL_PKEY = Internal.createUniqueKey(Individual.INDIVIDUAL, DSL.name("individual_pkey"), new TableField[] { Individual.INDIVIDUAL.ID }, true);
    public static final UniqueKey<IndividualPasswordResetAuditRecord> INDIVIDUAL_PASSWORD_RESET_AUDIT_PKEY = Internal.createUniqueKey(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT, DSL.name("individual_password_reset_audit_pkey"), new TableField[] { IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.ID }, true);
    public static final UniqueKey<IndividualPermissionRecord> INDIVIDUAL_PERMISSION_PK = Internal.createUniqueKey(IndividualPermission.INDIVIDUAL_PERMISSION, DSL.name("individual_permission_pk"), new TableField[] { IndividualPermission.INDIVIDUAL_PERMISSION.ID }, true);
    public static final UniqueKey<IndividualRoleRecord> INDIVIDUAL_ROLE_ID_PK = Internal.createUniqueKey(IndividualRole.INDIVIDUAL_ROLE, DSL.name("individual_role_id_pk"), new TableField[] { IndividualRole.INDIVIDUAL_ROLE.ID }, true);
    public static final UniqueKey<IndividualSessionsRecord> INDIVIDUAL_SESSIONS_PKEY = Internal.createUniqueKey(IndividualSessions.INDIVIDUAL_SESSIONS, DSL.name("individual_sessions_pkey"), new TableField[] { IndividualSessions.INDIVIDUAL_SESSIONS.ID }, true);
    public static final UniqueKey<IndividualVerificationAuditRecord> INDIVIDUAL_VERIFICATION_AUDIT_PKEY = Internal.createUniqueKey(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT, DSL.name("individual_verification_audit_pkey"), new TableField[] { IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ID }, true);
    public static final UniqueKey<ListNamesRecord> LIST_NAMES_ID_PK = Internal.createUniqueKey(ListNames.LIST_NAMES, DSL.name("list_names_id_pk"), new TableField[] { ListNames.LIST_NAMES.ID }, true);
    public static final UniqueKey<ListNamesRecord> LIST_NAMES_NAME_KEY = Internal.createUniqueKey(ListNames.LIST_NAMES, DSL.name("list_names_name_key"), new TableField[] { ListNames.LIST_NAMES.NAME }, true);
    public static final UniqueKey<ListValuesRecord> LIST_VALUES_ID_PK = Internal.createUniqueKey(ListValues.LIST_VALUES, DSL.name("list_values_id_pk"), new TableField[] { ListValues.LIST_VALUES.ID }, true);
    public static final UniqueKey<OrganisationRecord> ORGANISATION_PKEY = Internal.createUniqueKey(Organisation.ORGANISATION, DSL.name("organisation_pkey"), new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final UniqueKey<PaymentsRecord> PAYMENTS_PKEY = Internal.createUniqueKey(Payments.PAYMENTS, DSL.name("payments_pkey"), new TableField[] { Payments.PAYMENTS.ID }, true);
    public static final UniqueKey<ResourceRecord> RESOURCE_ID_PK = Internal.createUniqueKey(Resource.RESOURCE, DSL.name("resource_id_pk"), new TableField[] { Resource.RESOURCE.ID }, true);
    public static final UniqueKey<RolesRecord> ROLES_ID_PK = Internal.createUniqueKey(Roles.ROLES, DSL.name("roles_id_pk"), new TableField[] { Roles.ROLES.ID }, true);

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    public static final ForeignKey<DocumentRepoRecord, ResourceRecord> DOCUMENT_REPO__DOCUMENTS_REPO_CATEGORY_FK = Internal.createForeignKey(DocumentRepo.DOCUMENT_REPO, DSL.name("documents_repo_category_fk"), new TableField[] { DocumentRepo.DOCUMENT_REPO.CATEGORY }, Keys.RESOURCE_ID_PK, new TableField[] { Resource.RESOURCE.ID }, true);
    public static final ForeignKey<DocumentRepoRecord, ResourceRecord> DOCUMENT_REPO__DOCUMENTS_REPO_SUB_CATEGORY_FK = Internal.createForeignKey(DocumentRepo.DOCUMENT_REPO, DSL.name("documents_repo_sub_category_fk"), new TableField[] { DocumentRepo.DOCUMENT_REPO.SUB_CATEGORY }, Keys.RESOURCE_ID_PK, new TableField[] { Resource.RESOURCE.ID }, true);
    public static final ForeignKey<DonationHeadsRecord, OrganisationRecord> DONATION_HEADS__DONATION_HEADS_ORGANISATION_FK = Internal.createForeignKey(DonationHeads.DONATION_HEADS, DSL.name("donation_heads_organisation_fk"), new TableField[] { DonationHeads.DONATION_HEADS.ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<DonationReceiptsRecord, OrganisationRecord> DONATION_RECEIPTS__DONATION_RECEIPT_ORG_FK = Internal.createForeignKey(DonationReceipts.DONATION_RECEIPTS, DSL.name("donation_receipt_org_fk"), new TableField[] { DonationReceipts.DONATION_RECEIPTS.TENANT_ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<DonationReceiptsRecord, DonorsRecord> DONATION_RECEIPTS__FK_DONOR_ID = Internal.createForeignKey(DonationReceipts.DONATION_RECEIPTS, DSL.name("fk_donor_id"), new TableField[] { DonationReceipts.DONATION_RECEIPTS.DONOR_ID }, Keys.DONORS_ID_PK, new TableField[] { Donors.DONORS.ID }, true);
    public static final ForeignKey<DonationReceiptsRecord, OrganisationRecord> DONATION_RECEIPTS__FK_DONOR_ORGANISATION_ID = Internal.createForeignKey(DonationReceipts.DONATION_RECEIPTS, DSL.name("fk_donor_organisation_id"), new TableField[] { DonationReceipts.DONATION_RECEIPTS.DONOR_ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<DonorGroupMappingRecord, DonorsRecord> DONOR_GROUP_MAPPING__FK_DONOR = Internal.createForeignKey(DonorGroupMapping.DONOR_GROUP_MAPPING, DSL.name("fk_donor"), new TableField[] { DonorGroupMapping.DONOR_GROUP_MAPPING.DONOR_ID }, Keys.DONORS_ID_PK, new TableField[] { Donors.DONORS.ID }, true);
    public static final ForeignKey<DonorGroupMappingRecord, DonorGroupsRecord> DONOR_GROUP_MAPPING__FK_GROUP = Internal.createForeignKey(DonorGroupMapping.DONOR_GROUP_MAPPING, DSL.name("fk_group"), new TableField[] { DonorGroupMapping.DONOR_GROUP_MAPPING.GROUP_ID }, Keys.DONOR_GROUPS_PKEY, new TableField[] { DonorGroups.DONOR_GROUPS.ID }, true);
    public static final ForeignKey<DonorGroupsRecord, OrganisationRecord> DONOR_GROUPS__FK_DONOR_GROUPS_ORG = Internal.createForeignKey(DonorGroups.DONOR_GROUPS, DSL.name("fk_donor_groups_org"), new TableField[] { DonorGroups.DONOR_GROUPS.ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<DonorsRecord, OrganisationRecord> DONORS__TENANT_ORG_ID_FK = Internal.createForeignKey(Donors.DONORS, DSL.name("tenant_org_id_fk"), new TableField[] { Donors.DONORS.TENANT_ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<ImportBatchRecord, OrganisationRecord> IMPORT_BATCH__IMPORT_BATCH_TENANT_ORG_ID_FKEY = Internal.createForeignKey(ImportBatch.IMPORT_BATCH, DSL.name("import_batch_tenant_org_id_fkey"), new TableField[] { ImportBatch.IMPORT_BATCH.TENANT_ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<ImportStagingRecord, ImportBatchRecord> IMPORT_STAGING__IMPORT_STAGING_IMPORT_BATCH_ID_FKEY = Internal.createForeignKey(ImportStaging.IMPORT_STAGING, DSL.name("import_staging_import_batch_id_fkey"), new TableField[] { ImportStaging.IMPORT_STAGING.IMPORT_BATCH_ID }, Keys.IMPORT_BATCH_PKEY, new TableField[] { ImportBatch.IMPORT_BATCH.ID }, true);
    public static final ForeignKey<IndividualPasswordResetAuditRecord, IndividualRecord> INDIVIDUAL_PASSWORD_RESET_AUDIT__INDIVIDUAL_PASSWORD_RESET_AUDIT_INDIVIDUAL_ID_FKEY = Internal.createForeignKey(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT, DSL.name("individual_password_reset_audit_individual_id_fkey"), new TableField[] { IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.INDIVIDUAL_ID }, Keys.INDIVIDUAL_PKEY, new TableField[] { Individual.INDIVIDUAL.ID }, true);
    public static final ForeignKey<IndividualPermissionRecord, IndividualRecord> INDIVIDUAL_PERMISSION__INDIVIDUAL_PERMISSION_INDIVIDUAL_FK = Internal.createForeignKey(IndividualPermission.INDIVIDUAL_PERMISSION, DSL.name("individual_permission_individual_fk"), new TableField[] { IndividualPermission.INDIVIDUAL_PERMISSION.INDIVIDUAL_ID }, Keys.INDIVIDUAL_PKEY, new TableField[] { Individual.INDIVIDUAL.ID }, true);
    public static final ForeignKey<IndividualPermissionRecord, OrganisationRecord> INDIVIDUAL_PERMISSION__INDIVIDUAL_PERMISSION_ORGANISATION_FK = Internal.createForeignKey(IndividualPermission.INDIVIDUAL_PERMISSION, DSL.name("individual_permission_organisation_fk"), new TableField[] { IndividualPermission.INDIVIDUAL_PERMISSION.ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<IndividualRoleRecord, IndividualRecord> INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_INDIVIDUAL_ID_FK = Internal.createForeignKey(IndividualRole.INDIVIDUAL_ROLE, DSL.name("individual_role_individual_id_fk"), new TableField[] { IndividualRole.INDIVIDUAL_ROLE.INDIVIDUAL_ID }, Keys.INDIVIDUAL_PKEY, new TableField[] { Individual.INDIVIDUAL.ID }, true);
    public static final ForeignKey<IndividualRoleRecord, OrganisationRecord> INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_ORGANISATION_FK = Internal.createForeignKey(IndividualRole.INDIVIDUAL_ROLE, DSL.name("individual_role_organisation_fk"), new TableField[] { IndividualRole.INDIVIDUAL_ROLE.ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<IndividualRoleRecord, RolesRecord> INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_ROLE_ID_FK = Internal.createForeignKey(IndividualRole.INDIVIDUAL_ROLE, DSL.name("individual_role_role_id_fk"), new TableField[] { IndividualRole.INDIVIDUAL_ROLE.ROLE_ID }, Keys.ROLES_ID_PK, new TableField[] { Roles.ROLES.ID }, true);
    public static final ForeignKey<IndividualSessionsRecord, IndividualRecord> INDIVIDUAL_SESSIONS__INDIVIDUAL_SESSIONS_IND_FK = Internal.createForeignKey(IndividualSessions.INDIVIDUAL_SESSIONS, DSL.name("individual_sessions_ind_fk"), new TableField[] { IndividualSessions.INDIVIDUAL_SESSIONS.INDIVIDUAL_ID }, Keys.INDIVIDUAL_PKEY, new TableField[] { Individual.INDIVIDUAL.ID }, true);
    public static final ForeignKey<IndividualVerificationAuditRecord, OrganisationRecord> INDIVIDUAL_VERIFICATION_AUDIT__FK_ORG_ID_IND_VERIFICATION = Internal.createForeignKey(IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT, DSL.name("fk_org_id_ind_verification"), new TableField[] { IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<ListValuesRecord, ListNamesRecord> LIST_VALUES__FK_LIST_NAMES = Internal.createForeignKey(ListValues.LIST_VALUES, DSL.name("fk_list_names"), new TableField[] { ListValues.LIST_VALUES.LIST_NAMES_ID }, Keys.LIST_NAMES_ID_PK, new TableField[] { ListNames.LIST_NAMES.ID }, true);
    public static final ForeignKey<PaymentsRecord, DonationReceiptsRecord> PAYMENTS__PAYMENTS_DONATION_RECEIPT_ID_FKEY = Internal.createForeignKey(Payments.PAYMENTS, DSL.name("payments_donation_receipt_id_fkey"), new TableField[] { Payments.PAYMENTS.DONATION_RECEIPT_ID }, Keys.DONATION_RECEIPTS_PKEY, new TableField[] { DonationReceipts.DONATION_RECEIPTS.ID }, true);
    public static final ForeignKey<ResourceRecord, ResourceRecord> RESOURCE__RESOURCE_PARENT_RESOURCE_FK = Internal.createForeignKey(Resource.RESOURCE, DSL.name("resource_parent_resource_fk"), new TableField[] { Resource.RESOURCE.PARENT_RESOURCE_ID }, Keys.RESOURCE_ID_PK, new TableField[] { Resource.RESOURCE.ID }, true);
    public static final ForeignKey<RolesRecord, OrganisationRecord> ROLES__FK_ROLES_ORG = Internal.createForeignKey(Roles.ROLES, DSL.name("fk_roles_org"), new TableField[] { Roles.ROLES.ORG_ID }, Keys.ORGANISATION_PKEY, new TableField[] { Organisation.ORGANISATION.ID }, true);
    public static final ForeignKey<RolesRecord, RolesRecord> ROLES__ROLES_PARENT_ROLE_FK = Internal.createForeignKey(Roles.ROLES, DSL.name("roles_parent_role_fk"), new TableField[] { Roles.ROLES.PARENT_ROLE_ID }, Keys.ROLES_ID_PK, new TableField[] { Roles.ROLES.ID }, true);
}
