/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportStagingRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ImportStagingDao extends DAOImpl<ImportStagingRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging, UUID> {

    /**
     * Create a new ImportStagingDao without any configuration
     */
    public ImportStagingDao() {
        super(ImportStaging.IMPORT_STAGING, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging.class);
    }

    /**
     * Create a new ImportStagingDao with an attached configuration
     */
    @Autowired
    public ImportStagingDao(Configuration configuration) {
        super(ImportStaging.IMPORT_STAGING, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchById(UUID... values) {
        return fetch(ImportStaging.IMPORT_STAGING.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging fetchOneById(UUID value) {
        return fetchOne(ImportStaging.IMPORT_STAGING.ID, value);
    }

    /**
     * Fetch records that have <code>import_batch_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfImportBatchId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.IMPORT_BATCH_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>import_batch_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByImportBatchId(UUID... values) {
        return fetch(ImportStaging.IMPORT_STAGING.IMPORT_BATCH_ID, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByCategory(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByName(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.NAME, values);
    }

    /**
     * Fetch records that have <code>mobile_number BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfMobileNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.MOBILE_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mobile_number IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByMobileNumber(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.MOBILE_NUMBER, values);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByEmail(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.EMAIL, values);
    }

    /**
     * Fetch records that have <code>pan_no BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfPanNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.PAN_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>pan_no IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByPanNo(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.PAN_NO, values);
    }

    /**
     * Fetch records that have <code>meta_data BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfMetaData(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.META_DATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>meta_data IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByMetaData(JSONB... values) {
        return fetch(ImportStaging.IMPORT_STAGING.META_DATA, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByStatus(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.STATUS, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(ImportStaging.IMPORT_STAGING.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(ImportStaging.IMPORT_STAGING.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByCreatedBy(UUID... values) {
        return fetch(ImportStaging.IMPORT_STAGING.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByUpdatedBy(UUID... values) {
        return fetch(ImportStaging.IMPORT_STAGING.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByIsActive(Boolean... values) {
        return fetch(ImportStaging.IMPORT_STAGING.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>encrypted_pan_no BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfEncryptedPanNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.ENCRYPTED_PAN_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>encrypted_pan_no IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByEncryptedPanNo(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.ENCRYPTED_PAN_NO, values);
    }

    /**
     * Fetch records that have <code>pan_no_nonce BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchRangeOfPanNoNonce(String lowerInclusive, String upperInclusive) {
        return fetchRange(ImportStaging.IMPORT_STAGING.PAN_NO_NONCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>pan_no_nonce IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ImportStaging> fetchByPanNoNonce(String... values) {
        return fetch(ImportStaging.IMPORT_STAGING.PAN_NO_NONCE, values);
    }
}
