/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.Payments;
import com.chidhagni.donationreceipt.db.jooq.tables.records.PaymentsRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class PaymentsDao extends DAOImpl<PaymentsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments, UUID> {

    /**
     * Create a new PaymentsDao without any configuration
     */
    public PaymentsDao() {
        super(Payments.PAYMENTS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments.class);
    }

    /**
     * Create a new PaymentsDao with an attached configuration
     */
    @Autowired
    public PaymentsDao(Configuration configuration) {
        super(Payments.PAYMENTS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Payments.PAYMENTS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchById(UUID... values) {
        return fetch(Payments.PAYMENTS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments fetchOneById(UUID value) {
        return fetchOne(Payments.PAYMENTS.ID, value);
    }

    /**
     * Fetch records that have <code>amount BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfAmount(BigDecimal lowerInclusive, BigDecimal upperInclusive) {
        return fetchRange(Payments.PAYMENTS.AMOUNT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>amount IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByAmount(BigDecimal... values) {
        return fetch(Payments.PAYMENTS.AMOUNT, values);
    }

    /**
     * Fetch records that have <code>payment_status BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfPaymentStatus(String lowerInclusive, String upperInclusive) {
        return fetchRange(Payments.PAYMENTS.PAYMENT_STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>payment_status IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByPaymentStatus(String... values) {
        return fetch(Payments.PAYMENTS.PAYMENT_STATUS, values);
    }

    /**
     * Fetch records that have <code>razorpay_payment_method BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfRazorpayPaymentMethod(String lowerInclusive, String upperInclusive) {
        return fetchRange(Payments.PAYMENTS.RAZORPAY_PAYMENT_METHOD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>razorpay_payment_method IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByRazorpayPaymentMethod(String... values) {
        return fetch(Payments.PAYMENTS.RAZORPAY_PAYMENT_METHOD, values);
    }

    /**
     * Fetch records that have <code>order_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfOrderId(String lowerInclusive, String upperInclusive) {
        return fetchRange(Payments.PAYMENTS.ORDER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>order_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByOrderId(String... values) {
        return fetch(Payments.PAYMENTS.ORDER_ID, values);
    }

    /**
     * Fetch records that have <code>payment_date BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfPaymentDate(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Payments.PAYMENTS.PAYMENT_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>payment_date IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByPaymentDate(LocalDateTime... values) {
        return fetch(Payments.PAYMENTS.PAYMENT_DATE, values);
    }

    /**
     * Fetch records that have <code>donation_receipt_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfDonationReceiptId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Payments.PAYMENTS.DONATION_RECEIPT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>donation_receipt_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByDonationReceiptId(UUID... values) {
        return fetch(Payments.PAYMENTS.DONATION_RECEIPT_ID, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Payments.PAYMENTS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByIsActive(Boolean... values) {
        return fetch(Payments.PAYMENTS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>payment_type BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfPaymentType(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Payments.PAYMENTS.PAYMENT_TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>payment_type IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByPaymentType(UUID... values) {
        return fetch(Payments.PAYMENTS.PAYMENT_TYPE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Payments.PAYMENTS.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByCreatedBy(UUID... values) {
        return fetch(Payments.PAYMENTS.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Payments.PAYMENTS.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByUpdatedBy(UUID... values) {
        return fetch(Payments.PAYMENTS.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Payments.PAYMENTS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Payments.PAYMENTS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Payments.PAYMENTS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Payments.PAYMENTS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>razorpay_payment_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfRazorpayPaymentId(String lowerInclusive, String upperInclusive) {
        return fetchRange(Payments.PAYMENTS.RAZORPAY_PAYMENT_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>razorpay_payment_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByRazorpayPaymentId(String... values) {
        return fetch(Payments.PAYMENTS.RAZORPAY_PAYMENT_ID, values);
    }

    /**
     * Fetch records that have <code>failure_description BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchRangeOfFailureDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(Payments.PAYMENTS.FAILURE_DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>failure_description IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Payments> fetchByFailureDescription(String... values) {
        return fetch(Payments.PAYMENTS.FAILURE_DESCRIPTION, values);
    }
}
