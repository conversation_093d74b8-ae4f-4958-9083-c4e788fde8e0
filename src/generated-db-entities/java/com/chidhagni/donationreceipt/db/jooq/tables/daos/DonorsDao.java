/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorsRecord;
import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DonorsDao extends DAOImpl<DonorsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors, UUID> {

    /**
     * Create a new DonorsDao without any configuration
     */
    public DonorsDao() {
        super(Donors.DONORS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors.class);
    }

    /**
     * Create a new DonorsDao with an attached configuration
     */
    @Autowired
    public DonorsDao(Configuration configuration) {
        super(Donors.DONORS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Donors.DONORS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchById(UUID... values) {
        return fetch(Donors.DONORS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors fetchOneById(UUID value) {
        return fetchOne(Donors.DONORS.ID, value);
    }

    /**
     * Fetch records that have <code>tenant_org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfTenantOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Donors.DONORS.TENANT_ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>tenant_org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByTenantOrgId(UUID... values) {
        return fetch(Donors.DONORS.TENANT_ORG_ID, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Donors.DONORS.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByName(String... values) {
        return fetch(Donors.DONORS.NAME, values);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(Donors.DONORS.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByEmail(String... values) {
        return fetch(Donors.DONORS.EMAIL, values);
    }

    /**
     * Fetch records that have <code>mobile_number BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfMobileNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(Donors.DONORS.MOBILE_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mobile_number IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByMobileNumber(String... values) {
        return fetch(Donors.DONORS.MOBILE_NUMBER, values);
    }

    /**
     * Fetch records that have <code>pan_no BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfPanNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(Donors.DONORS.PAN_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>pan_no IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByPanNo(String... values) {
        return fetch(Donors.DONORS.PAN_NO, values);
    }

    /**
     * Fetch records that have <code>meta_data BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfMetaData(DonorMetaData lowerInclusive, DonorMetaData upperInclusive) {
        return fetchRange(Donors.DONORS.META_DATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>meta_data IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByMetaData(DonorMetaData... values) {
        return fetch(Donors.DONORS.META_DATA, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Donors.DONORS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByIsActive(Boolean... values) {
        return fetch(Donors.DONORS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Donors.DONORS.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByCreatedBy(UUID... values) {
        return fetch(Donors.DONORS.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Donors.DONORS.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByUpdatedBy(UUID... values) {
        return fetch(Donors.DONORS.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Donors.DONORS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Donors.DONORS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Donors.DONORS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Donors.DONORS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>encrypted_pan_no BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfEncryptedPanNo(String lowerInclusive, String upperInclusive) {
        return fetchRange(Donors.DONORS.ENCRYPTED_PAN_NO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>encrypted_pan_no IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByEncryptedPanNo(String... values) {
        return fetch(Donors.DONORS.ENCRYPTED_PAN_NO, values);
    }

    /**
     * Fetch records that have <code>pan_no_nonce BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchRangeOfPanNoNonce(String lowerInclusive, String upperInclusive) {
        return fetchRange(Donors.DONORS.PAN_NO_NONCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>pan_no_nonce IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors> fetchByPanNoNonce(String... values) {
        return fetch(Donors.DONORS.PAN_NO_NONCE, values);
    }
}
