/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.documentrepo.dto.request.TagsDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DocumentRepo implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID                                                                                id;
    private UUID                                                                                category;
    private UUID                                                                                subCategory;
    private com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO       sender;
    private List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> recipients;
    private String                                                                              path;
    private LocalDateTime                                                                       fileDate;
    private UUID                                                                                createdBy;
    private UUID                                                                                updatedBy;
    private LocalDateTime                                                                       createdOn;
    private LocalDateTime                                                                       updatedOn;
    private String                                                                              remarks;
    private TagsDTO                                                                             tags;
    private Boolean                                                                             isActive;

    public DocumentRepo() {}

    public DocumentRepo(DocumentRepo value) {
        this.id = value.id;
        this.category = value.category;
        this.subCategory = value.subCategory;
        this.sender = value.sender;
        this.recipients = value.recipients;
        this.path = value.path;
        this.fileDate = value.fileDate;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.remarks = value.remarks;
        this.tags = value.tags;
        this.isActive = value.isActive;
    }

    public DocumentRepo(
        UUID                                                                                id,
        UUID                                                                                category,
        UUID                                                                                subCategory,
        com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO       sender,
        List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> recipients,
        String                                                                              path,
        LocalDateTime                                                                       fileDate,
        UUID                                                                                createdBy,
        UUID                                                                                updatedBy,
        LocalDateTime                                                                       createdOn,
        LocalDateTime                                                                       updatedOn,
        String                                                                              remarks,
        TagsDTO                                                                             tags,
        Boolean                                                                             isActive
    ) {
        this.id = id;
        this.category = category;
        this.subCategory = subCategory;
        this.sender = sender;
        this.recipients = recipients;
        this.path = path;
        this.fileDate = fileDate;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.remarks = remarks;
        this.tags = tags;
        this.isActive = isActive;
    }

    /**
     * Getter for <code>document_repo.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>document_repo.id</code>.
     */
    public DocumentRepo setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>document_repo.category</code>.
     */
    public UUID getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>document_repo.category</code>.
     */
    public DocumentRepo setCategory(UUID category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>document_repo.sub_category</code>.
     */
    public UUID getSubCategory() {
        return this.subCategory;
    }

    /**
     * Setter for <code>document_repo.sub_category</code>.
     */
    public DocumentRepo setSubCategory(UUID subCategory) {
        this.subCategory = subCategory;
        return this;
    }

    /**
     * Getter for <code>document_repo.sender</code>.
     */
    public com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO getSender() {
        return this.sender;
    }

    /**
     * Setter for <code>document_repo.sender</code>.
     */
    public DocumentRepo setSender(com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO sender) {
        this.sender = sender;
        return this;
    }

    /**
     * Getter for <code>document_repo.recipients</code>.
     */
    public List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> getRecipients() {
        return this.recipients;
    }

    /**
     * Setter for <code>document_repo.recipients</code>.
     */
    public DocumentRepo setRecipients(List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> recipients) {
        this.recipients = recipients;
        return this;
    }

    /**
     * Getter for <code>document_repo.path</code>.
     */
    public String getPath() {
        return this.path;
    }

    /**
     * Setter for <code>document_repo.path</code>.
     */
    public DocumentRepo setPath(String path) {
        this.path = path;
        return this;
    }

    /**
     * Getter for <code>document_repo.file_date</code>.
     */
    public LocalDateTime getFileDate() {
        return this.fileDate;
    }

    /**
     * Setter for <code>document_repo.file_date</code>.
     */
    public DocumentRepo setFileDate(LocalDateTime fileDate) {
        this.fileDate = fileDate;
        return this;
    }

    /**
     * Getter for <code>document_repo.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>document_repo.created_by</code>.
     */
    public DocumentRepo setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>document_repo.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>document_repo.updated_by</code>.
     */
    public DocumentRepo setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>document_repo.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>document_repo.created_on</code>.
     */
    public DocumentRepo setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>document_repo.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>document_repo.updated_on</code>.
     */
    public DocumentRepo setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>document_repo.remarks</code>.
     */
    public String getRemarks() {
        return this.remarks;
    }

    /**
     * Setter for <code>document_repo.remarks</code>.
     */
    public DocumentRepo setRemarks(String remarks) {
        this.remarks = remarks;
        return this;
    }

    /**
     * Getter for <code>document_repo.tags</code>.
     */
    public TagsDTO getTags() {
        return this.tags;
    }

    /**
     * Setter for <code>document_repo.tags</code>.
     */
    public DocumentRepo setTags(TagsDTO tags) {
        this.tags = tags;
        return this;
    }

    /**
     * Getter for <code>document_repo.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>document_repo.is_active</code>.
     */
    public DocumentRepo setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final DocumentRepo other = (DocumentRepo) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (category == null) {
            if (other.category != null)
                return false;
        }
        else if (!category.equals(other.category))
            return false;
        if (subCategory == null) {
            if (other.subCategory != null)
                return false;
        }
        else if (!subCategory.equals(other.subCategory))
            return false;
        if (sender == null) {
            if (other.sender != null)
                return false;
        }
        else if (!sender.equals(other.sender))
            return false;
        if (recipients == null) {
            if (other.recipients != null)
                return false;
        }
        else if (!recipients.equals(other.recipients))
            return false;
        if (path == null) {
            if (other.path != null)
                return false;
        }
        else if (!path.equals(other.path))
            return false;
        if (fileDate == null) {
            if (other.fileDate != null)
                return false;
        }
        else if (!fileDate.equals(other.fileDate))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (remarks == null) {
            if (other.remarks != null)
                return false;
        }
        else if (!remarks.equals(other.remarks))
            return false;
        if (tags == null) {
            if (other.tags != null)
                return false;
        }
        else if (!tags.equals(other.tags))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.subCategory == null) ? 0 : this.subCategory.hashCode());
        result = prime * result + ((this.sender == null) ? 0 : this.sender.hashCode());
        result = prime * result + ((this.recipients == null) ? 0 : this.recipients.hashCode());
        result = prime * result + ((this.path == null) ? 0 : this.path.hashCode());
        result = prime * result + ((this.fileDate == null) ? 0 : this.fileDate.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.remarks == null) ? 0 : this.remarks.hashCode());
        result = prime * result + ((this.tags == null) ? 0 : this.tags.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DocumentRepo (");

        sb.append(id);
        sb.append(", ").append(category);
        sb.append(", ").append(subCategory);
        sb.append(", ").append(sender);
        sb.append(", ").append(recipients);
        sb.append(", ").append(path);
        sb.append(", ").append(fileDate);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(remarks);
        sb.append(", ").append(tags);
        sb.append(", ").append(isActive);

        sb.append(")");
        return sb.toString();
    }
}
