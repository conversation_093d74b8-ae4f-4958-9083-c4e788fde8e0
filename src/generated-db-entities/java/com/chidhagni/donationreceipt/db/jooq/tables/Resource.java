/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ResourceRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Resource extends TableImpl<ResourceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>resource</code>
     */
    public static final Resource RESOURCE = new Resource();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResourceRecord> getRecordType() {
        return ResourceRecord.class;
    }

    /**
     * The column <code>resource.id</code>.
     */
    public final TableField<ResourceRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>resource.name</code>.
     */
    public final TableField<ResourceRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>resource.description</code>.
     */
    public final TableField<ResourceRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>resource.type</code>.
     */
    public final TableField<ResourceRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(50).nullable(false), this, "");

    /**
     * The column <code>resource.parent_resource_id</code>.
     */
    public final TableField<ResourceRecord, UUID> PARENT_RESOURCE_ID = createField(DSL.name("parent_resource_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>resource.validations</code>.
     */
    public final TableField<ResourceRecord, JSONB> VALIDATIONS = createField(DSL.name("validations"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>resource.is_active</code>.
     */
    public final TableField<ResourceRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>resource.created_on</code>.
     */
    public final TableField<ResourceRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>resource.updated_on</code>.
     */
    public final TableField<ResourceRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>resource.created_by</code>.
     */
    public final TableField<ResourceRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>resource.updated_by</code>.
     */
    public final TableField<ResourceRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    private Resource(Name alias, Table<ResourceRecord> aliased) {
        this(alias, aliased, null);
    }

    private Resource(Name alias, Table<ResourceRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>resource</code> table reference
     */
    public Resource(String alias) {
        this(DSL.name(alias), RESOURCE);
    }

    /**
     * Create an aliased <code>resource</code> table reference
     */
    public Resource(Name alias) {
        this(alias, RESOURCE);
    }

    /**
     * Create a <code>resource</code> table reference
     */
    public Resource() {
        this(DSL.name("resource"), null);
    }

    public <O extends Record> Resource(Table<O> child, ForeignKey<O, ResourceRecord> key) {
        super(child, key, RESOURCE);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<ResourceRecord> getPrimaryKey() {
        return Keys.RESOURCE_ID_PK;
    }

    @Override
    public List<UniqueKey<ResourceRecord>> getKeys() {
        return Arrays.<UniqueKey<ResourceRecord>>asList(Keys.RESOURCE_ID_PK);
    }

    @Override
    public List<ForeignKey<ResourceRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<ResourceRecord, ?>>asList(Keys.RESOURCE__RESOURCE_PARENT_RESOURCE_FK);
    }

    private transient Resource _resource;

    public Resource resource() {
        if (_resource == null)
            _resource = new Resource(this, Keys.RESOURCE__RESOURCE_PARENT_RESOURCE_FK);

        return _resource;
    }

    @Override
    public Resource as(String alias) {
        return new Resource(DSL.name(alias), this);
    }

    @Override
    public Resource as(Name alias) {
        return new Resource(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Resource rename(String name) {
        return new Resource(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Resource rename(Name name) {
        return new Resource(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, String, String, String, UUID, JSONB, Boolean, LocalDateTime, LocalDateTime, UUID, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
