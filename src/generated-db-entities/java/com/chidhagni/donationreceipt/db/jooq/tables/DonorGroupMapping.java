/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Indexes;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorGroupMappingRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorGroupMapping extends TableImpl<DonorGroupMappingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>donor_group_mapping</code>
     */
    public static final DonorGroupMapping DONOR_GROUP_MAPPING = new DonorGroupMapping();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DonorGroupMappingRecord> getRecordType() {
        return DonorGroupMappingRecord.class;
    }

    /**
     * The column <code>donor_group_mapping.id</code>.
     */
    public final TableField<DonorGroupMappingRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donor_group_mapping.donor_id</code>.
     */
    public final TableField<DonorGroupMappingRecord, UUID> DONOR_ID = createField(DSL.name("donor_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donor_group_mapping.group_id</code>.
     */
    public final TableField<DonorGroupMappingRecord, UUID> GROUP_ID = createField(DSL.name("group_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donor_group_mapping.created_on</code>.
     */
    public final TableField<DonorGroupMappingRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>donor_group_mapping.updated_on</code>.
     */
    public final TableField<DonorGroupMappingRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>donor_group_mapping.created_by</code>.
     */
    public final TableField<DonorGroupMappingRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donor_group_mapping.updated_by</code>.
     */
    public final TableField<DonorGroupMappingRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donor_group_mapping.is_active</code>.
     */
    public final TableField<DonorGroupMappingRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    private DonorGroupMapping(Name alias, Table<DonorGroupMappingRecord> aliased) {
        this(alias, aliased, null);
    }

    private DonorGroupMapping(Name alias, Table<DonorGroupMappingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>donor_group_mapping</code> table reference
     */
    public DonorGroupMapping(String alias) {
        this(DSL.name(alias), DONOR_GROUP_MAPPING);
    }

    /**
     * Create an aliased <code>donor_group_mapping</code> table reference
     */
    public DonorGroupMapping(Name alias) {
        this(alias, DONOR_GROUP_MAPPING);
    }

    /**
     * Create a <code>donor_group_mapping</code> table reference
     */
    public DonorGroupMapping() {
        this(DSL.name("donor_group_mapping"), null);
    }

    public <O extends Record> DonorGroupMapping(Table<O> child, ForeignKey<O, DonorGroupMappingRecord> key) {
        super(child, key, DONOR_GROUP_MAPPING);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.IDX_DONOR_GROUP_MAPPING_CREATED_ON, Indexes.IDX_DONOR_GROUP_MAPPING_DONOR_ID, Indexes.IDX_DONOR_GROUP_MAPPING_GROUP_ID);
    }

    @Override
    public UniqueKey<DonorGroupMappingRecord> getPrimaryKey() {
        return Keys.DONOR_GROUP_MAPPING_PKEY;
    }

    @Override
    public List<UniqueKey<DonorGroupMappingRecord>> getKeys() {
        return Arrays.<UniqueKey<DonorGroupMappingRecord>>asList(Keys.DONOR_GROUP_MAPPING_PKEY);
    }

    @Override
    public List<ForeignKey<DonorGroupMappingRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<DonorGroupMappingRecord, ?>>asList(Keys.DONOR_GROUP_MAPPING__FK_DONOR, Keys.DONOR_GROUP_MAPPING__FK_GROUP);
    }

    private transient Donors _donors;
    private transient DonorGroups _donorGroups;

    public Donors donors() {
        if (_donors == null)
            _donors = new Donors(this, Keys.DONOR_GROUP_MAPPING__FK_DONOR);

        return _donors;
    }

    public DonorGroups donorGroups() {
        if (_donorGroups == null)
            _donorGroups = new DonorGroups(this, Keys.DONOR_GROUP_MAPPING__FK_GROUP);

        return _donorGroups;
    }

    @Override
    public DonorGroupMapping as(String alias) {
        return new DonorGroupMapping(DSL.name(alias), this);
    }

    @Override
    public DonorGroupMapping as(Name alias) {
        return new DonorGroupMapping(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DonorGroupMapping rename(String name) {
        return new DonorGroupMapping(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DonorGroupMapping rename(Name name) {
        return new DonorGroupMapping(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
