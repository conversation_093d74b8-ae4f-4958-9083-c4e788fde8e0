/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualPermissionRecord;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class IndividualPermissionDao extends DAOImpl<IndividualPermissionRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission, UUID> {

    /**
     * Create a new IndividualPermissionDao without any configuration
     */
    public IndividualPermissionDao() {
        super(IndividualPermission.INDIVIDUAL_PERMISSION, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission.class);
    }

    /**
     * Create a new IndividualPermissionDao with an attached configuration
     */
    @Autowired
    public IndividualPermissionDao(Configuration configuration) {
        super(IndividualPermission.INDIVIDUAL_PERMISSION, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchById(UUID... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission fetchOneById(UUID value) {
        return fetchOne(IndividualPermission.INDIVIDUAL_PERMISSION.ID, value);
    }

    /**
     * Fetch records that have <code>individual_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfIndividualId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.INDIVIDUAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>individual_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByIndividualId(UUID... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.INDIVIDUAL_ID, values);
    }

    /**
     * Fetch records that have <code>permissions BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfPermissions(List<RoleNode> lowerInclusive, List<RoleNode> upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.PERMISSIONS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>permissions IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByPermissions(List<RoleNode>... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.PERMISSIONS, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByCreatedBy(UUID... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByUpdatedBy(UUID... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>remarks BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfRemarks(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.REMARKS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>remarks IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByRemarks(String... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.REMARKS, values);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualPermission.INDIVIDUAL_PERMISSION.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission> fetchByOrgId(UUID... values) {
        return fetch(IndividualPermission.INDIVIDUAL_PERMISSION.ORG_ID, values);
    }
}
