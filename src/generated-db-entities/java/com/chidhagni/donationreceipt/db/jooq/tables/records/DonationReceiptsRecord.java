/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonationReceiptsRecord extends UpdatableRecordImpl<DonationReceiptsRecord> implements Record14<UUID, String, UUID, DonationReceiptMetaDataDTO, UUID, UUID, LocalDate, LocalDateTime, UUID, LocalDateTime, UUID, Boolean, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>donation_receipts.id</code>.
     */
    public DonationReceiptsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>donation_receipts.receipt_no</code>.
     */
    public DonationReceiptsRecord setReceiptNo(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.receipt_no</code>.
     */
    public String getReceiptNo() {
        return (String) get(1);
    }

    /**
     * Setter for <code>donation_receipts.tenant_org_id</code>.
     */
    public DonationReceiptsRecord setTenantOrgId(UUID value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.tenant_org_id</code>.
     */
    public UUID getTenantOrgId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>donation_receipts.meta_data</code>.
     */
    public DonationReceiptsRecord setMetaData(DonationReceiptMetaDataDTO value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.meta_data</code>.
     */
    public DonationReceiptMetaDataDTO getMetaData() {
        return (DonationReceiptMetaDataDTO) get(3);
    }

    /**
     * Setter for <code>donation_receipts.donation_type_id</code>.
     */
    public DonationReceiptsRecord setDonationTypeId(UUID value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donation_type_id</code>.
     */
    public UUID getDonationTypeId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>donation_receipts.donation_head_id</code>.
     */
    public DonationReceiptsRecord setDonationHeadId(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donation_head_id</code>.
     */
    public UUID getDonationHeadId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>donation_receipts.receipt_date</code>.
     */
    public DonationReceiptsRecord setReceiptDate(LocalDate value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.receipt_date</code>.
     */
    public LocalDate getReceiptDate() {
        return (LocalDate) get(6);
    }

    /**
     * Setter for <code>donation_receipts.created_on</code>.
     */
    public DonationReceiptsRecord setCreatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>donation_receipts.created_by</code>.
     */
    public DonationReceiptsRecord setCreatedBy(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>donation_receipts.updated_on</code>.
     */
    public DonationReceiptsRecord setUpdatedOn(LocalDateTime value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>donation_receipts.updated_by</code>.
     */
    public DonationReceiptsRecord setUpdatedBy(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(10);
    }

    /**
     * Setter for <code>donation_receipts.is_active</code>.
     */
    public DonationReceiptsRecord setIsActive(Boolean value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(11);
    }

    /**
     * Setter for <code>donation_receipts.donor_id</code>.
     */
    public DonationReceiptsRecord setDonorId(UUID value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donor_id</code>.
     */
    public UUID getDonorId() {
        return (UUID) get(12);
    }

    /**
     * Setter for <code>donation_receipts.donor_org_id</code>.
     */
    public DonationReceiptsRecord setDonorOrgId(UUID value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donor_org_id</code>.
     */
    public UUID getDonorOrgId() {
        return (UUID) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, String, UUID, DonationReceiptMetaDataDTO, UUID, UUID, LocalDate, LocalDateTime, UUID, LocalDateTime, UUID, Boolean, UUID, UUID> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<UUID, String, UUID, DonationReceiptMetaDataDTO, UUID, UUID, LocalDate, LocalDateTime, UUID, LocalDateTime, UUID, Boolean, UUID, UUID> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return DonationReceipts.DONATION_RECEIPTS.ID;
    }

    @Override
    public Field<String> field2() {
        return DonationReceipts.DONATION_RECEIPTS.RECEIPT_NO;
    }

    @Override
    public Field<UUID> field3() {
        return DonationReceipts.DONATION_RECEIPTS.TENANT_ORG_ID;
    }

    @Override
    public Field<DonationReceiptMetaDataDTO> field4() {
        return DonationReceipts.DONATION_RECEIPTS.META_DATA;
    }

    @Override
    public Field<UUID> field5() {
        return DonationReceipts.DONATION_RECEIPTS.DONATION_TYPE_ID;
    }

    @Override
    public Field<UUID> field6() {
        return DonationReceipts.DONATION_RECEIPTS.DONATION_HEAD_ID;
    }

    @Override
    public Field<LocalDate> field7() {
        return DonationReceipts.DONATION_RECEIPTS.RECEIPT_DATE;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return DonationReceipts.DONATION_RECEIPTS.CREATED_ON;
    }

    @Override
    public Field<UUID> field9() {
        return DonationReceipts.DONATION_RECEIPTS.CREATED_BY;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return DonationReceipts.DONATION_RECEIPTS.UPDATED_ON;
    }

    @Override
    public Field<UUID> field11() {
        return DonationReceipts.DONATION_RECEIPTS.UPDATED_BY;
    }

    @Override
    public Field<Boolean> field12() {
        return DonationReceipts.DONATION_RECEIPTS.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field13() {
        return DonationReceipts.DONATION_RECEIPTS.DONOR_ID;
    }

    @Override
    public Field<UUID> field14() {
        return DonationReceipts.DONATION_RECEIPTS.DONOR_ORG_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getReceiptNo();
    }

    @Override
    public UUID component3() {
        return getTenantOrgId();
    }

    @Override
    public DonationReceiptMetaDataDTO component4() {
        return getMetaData();
    }

    @Override
    public UUID component5() {
        return getDonationTypeId();
    }

    @Override
    public UUID component6() {
        return getDonationHeadId();
    }

    @Override
    public LocalDate component7() {
        return getReceiptDate();
    }

    @Override
    public LocalDateTime component8() {
        return getCreatedOn();
    }

    @Override
    public UUID component9() {
        return getCreatedBy();
    }

    @Override
    public LocalDateTime component10() {
        return getUpdatedOn();
    }

    @Override
    public UUID component11() {
        return getUpdatedBy();
    }

    @Override
    public Boolean component12() {
        return getIsActive();
    }

    @Override
    public UUID component13() {
        return getDonorId();
    }

    @Override
    public UUID component14() {
        return getDonorOrgId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getReceiptNo();
    }

    @Override
    public UUID value3() {
        return getTenantOrgId();
    }

    @Override
    public DonationReceiptMetaDataDTO value4() {
        return getMetaData();
    }

    @Override
    public UUID value5() {
        return getDonationTypeId();
    }

    @Override
    public UUID value6() {
        return getDonationHeadId();
    }

    @Override
    public LocalDate value7() {
        return getReceiptDate();
    }

    @Override
    public LocalDateTime value8() {
        return getCreatedOn();
    }

    @Override
    public UUID value9() {
        return getCreatedBy();
    }

    @Override
    public LocalDateTime value10() {
        return getUpdatedOn();
    }

    @Override
    public UUID value11() {
        return getUpdatedBy();
    }

    @Override
    public Boolean value12() {
        return getIsActive();
    }

    @Override
    public UUID value13() {
        return getDonorId();
    }

    @Override
    public UUID value14() {
        return getDonorOrgId();
    }

    @Override
    public DonationReceiptsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value2(String value) {
        setReceiptNo(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value3(UUID value) {
        setTenantOrgId(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value4(DonationReceiptMetaDataDTO value) {
        setMetaData(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value5(UUID value) {
        setDonationTypeId(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value6(UUID value) {
        setDonationHeadId(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value7(LocalDate value) {
        setReceiptDate(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value8(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value9(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value10(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value11(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value12(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value13(UUID value) {
        setDonorId(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord value14(UUID value) {
        setDonorOrgId(value);
        return this;
    }

    @Override
    public DonationReceiptsRecord values(UUID value1, String value2, UUID value3, DonationReceiptMetaDataDTO value4, UUID value5, UUID value6, LocalDate value7, LocalDateTime value8, UUID value9, LocalDateTime value10, UUID value11, Boolean value12, UUID value13, UUID value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DonationReceiptsRecord
     */
    public DonationReceiptsRecord() {
        super(DonationReceipts.DONATION_RECEIPTS);
    }

    /**
     * Create a detached, initialised DonationReceiptsRecord
     */
    public DonationReceiptsRecord(UUID id, String receiptNo, UUID tenantOrgId, DonationReceiptMetaDataDTO metaData, UUID donationTypeId, UUID donationHeadId, LocalDate receiptDate, LocalDateTime createdOn, UUID createdBy, LocalDateTime updatedOn, UUID updatedBy, Boolean isActive, UUID donorId, UUID donorOrgId) {
        super(DonationReceipts.DONATION_RECEIPTS);

        setId(id);
        setReceiptNo(receiptNo);
        setTenantOrgId(tenantOrgId);
        setMetaData(metaData);
        setDonationTypeId(donationTypeId);
        setDonationHeadId(donationHeadId);
        setReceiptDate(receiptDate);
        setCreatedOn(createdOn);
        setCreatedBy(createdBy);
        setUpdatedOn(updatedOn);
        setUpdatedBy(updatedBy);
        setIsActive(isActive);
        setDonorId(donorId);
        setDonorOrgId(donorOrgId);
    }
}
