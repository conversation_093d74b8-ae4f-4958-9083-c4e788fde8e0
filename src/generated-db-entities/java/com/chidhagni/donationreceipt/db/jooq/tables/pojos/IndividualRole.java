/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualRole implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          individualId;
    private UUID          roleId;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID          createdBy;
    private UUID          updatedBy;
    private UUID          orgId;

    public IndividualRole() {}

    public IndividualRole(IndividualRole value) {
        this.id = value.id;
        this.individualId = value.individualId;
        this.roleId = value.roleId;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.orgId = value.orgId;
    }

    public IndividualRole(
        UUID          id,
        UUID          individualId,
        UUID          roleId,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        UUID          createdBy,
        UUID          updatedBy,
        UUID          orgId
    ) {
        this.id = id;
        this.individualId = individualId;
        this.roleId = roleId;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.orgId = orgId;
    }

    /**
     * Getter for <code>individual_role.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>individual_role.id</code>.
     */
    public IndividualRole setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>individual_role.individual_id</code>.
     */
    public UUID getIndividualId() {
        return this.individualId;
    }

    /**
     * Setter for <code>individual_role.individual_id</code>.
     */
    public IndividualRole setIndividualId(UUID individualId) {
        this.individualId = individualId;
        return this;
    }

    /**
     * Getter for <code>individual_role.role_id</code>.
     */
    public UUID getRoleId() {
        return this.roleId;
    }

    /**
     * Setter for <code>individual_role.role_id</code>.
     */
    public IndividualRole setRoleId(UUID roleId) {
        this.roleId = roleId;
        return this;
    }

    /**
     * Getter for <code>individual_role.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>individual_role.created_on</code>.
     */
    public IndividualRole setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>individual_role.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>individual_role.updated_on</code>.
     */
    public IndividualRole setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>individual_role.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>individual_role.created_by</code>.
     */
    public IndividualRole setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>individual_role.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>individual_role.updated_by</code>.
     */
    public IndividualRole setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>individual_role.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>individual_role.org_id</code>.
     */
    public IndividualRole setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final IndividualRole other = (IndividualRole) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (individualId == null) {
            if (other.individualId != null)
                return false;
        }
        else if (!individualId.equals(other.individualId))
            return false;
        if (roleId == null) {
            if (other.roleId != null)
                return false;
        }
        else if (!roleId.equals(other.roleId))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!orgId.equals(other.orgId))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.individualId == null) ? 0 : this.individualId.hashCode());
        result = prime * result + ((this.roleId == null) ? 0 : this.roleId.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IndividualRole (");

        sb.append(id);
        sb.append(", ").append(individualId);
        sb.append(", ").append(roleId);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(orgId);

        sb.append(")");
        return sb.toString();
    }
}
