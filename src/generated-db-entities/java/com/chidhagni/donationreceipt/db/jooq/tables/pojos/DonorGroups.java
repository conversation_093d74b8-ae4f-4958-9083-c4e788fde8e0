/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupFilters;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorGroups implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID              id;
    private String            name;
    private String            description;
    private DonorGroupFilters filters;
    private UUID              createdBy;
    private UUID              updatedBy;
    private LocalDateTime     createdOn;
    private LocalDateTime     updatedOn;
    private Boolean           isActive;
    private UUID              orgId;

    public DonorGroups() {}

    public DonorGroups(DonorGroups value) {
        this.id = value.id;
        this.name = value.name;
        this.description = value.description;
        this.filters = value.filters;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.isActive = value.isActive;
        this.orgId = value.orgId;
    }

    public DonorGroups(
        UUID              id,
        String            name,
        String            description,
        DonorGroupFilters filters,
        UUID              createdBy,
        UUID              updatedBy,
        LocalDateTime     createdOn,
        LocalDateTime     updatedOn,
        Boolean           isActive,
        UUID              orgId
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.filters = filters;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.isActive = isActive;
        this.orgId = orgId;
    }

    /**
     * Getter for <code>donor_groups.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>donor_groups.id</code>.
     */
    public DonorGroups setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>donor_groups.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>donor_groups.name</code>.
     */
    public DonorGroups setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>donor_groups.description</code>.
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>donor_groups.description</code>.
     */
    public DonorGroups setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>donor_groups.filters</code>.
     */
    public DonorGroupFilters getFilters() {
        return this.filters;
    }

    /**
     * Setter for <code>donor_groups.filters</code>.
     */
    public DonorGroups setFilters(DonorGroupFilters filters) {
        this.filters = filters;
        return this;
    }

    /**
     * Getter for <code>donor_groups.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>donor_groups.created_by</code>.
     */
    public DonorGroups setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>donor_groups.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>donor_groups.updated_by</code>.
     */
    public DonorGroups setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>donor_groups.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>donor_groups.created_on</code>.
     */
    public DonorGroups setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>donor_groups.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>donor_groups.updated_on</code>.
     */
    public DonorGroups setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>donor_groups.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>donor_groups.is_active</code>.
     */
    public DonorGroups setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>donor_groups.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>donor_groups.org_id</code>.
     */
    public DonorGroups setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final DonorGroups other = (DonorGroups) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        }
        else if (!description.equals(other.description))
            return false;
        if (filters == null) {
            if (other.filters != null)
                return false;
        }
        else if (!filters.equals(other.filters))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!orgId.equals(other.orgId))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.filters == null) ? 0 : this.filters.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DonorGroups (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(filters);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(isActive);
        sb.append(", ").append(orgId);

        sb.append(")");
        return sb.toString();
    }
}
