/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorGroupMappingRecord extends UpdatableRecordImpl<DonorGroupMappingRecord> implements Record8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>donor_group_mapping.id</code>.
     */
    public DonorGroupMappingRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>donor_group_mapping.donor_id</code>.
     */
    public DonorGroupMappingRecord setDonorId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.donor_id</code>.
     */
    public UUID getDonorId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>donor_group_mapping.group_id</code>.
     */
    public DonorGroupMappingRecord setGroupId(UUID value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.group_id</code>.
     */
    public UUID getGroupId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>donor_group_mapping.created_on</code>.
     */
    public DonorGroupMappingRecord setCreatedOn(LocalDateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>donor_group_mapping.updated_on</code>.
     */
    public DonorGroupMappingRecord setUpdatedOn(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>donor_group_mapping.created_by</code>.
     */
    public DonorGroupMappingRecord setCreatedBy(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>donor_group_mapping.updated_by</code>.
     */
    public DonorGroupMappingRecord setUpdatedBy(UUID value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>donor_group_mapping.is_active</code>.
     */
    public DonorGroupMappingRecord setIsActive(Boolean value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.ID;
    }

    @Override
    public Field<UUID> field2() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.DONOR_ID;
    }

    @Override
    public Field<UUID> field3() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.GROUP_ID;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.UPDATED_ON;
    }

    @Override
    public Field<UUID> field6() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.CREATED_BY;
    }

    @Override
    public Field<UUID> field7() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.UPDATED_BY;
    }

    @Override
    public Field<Boolean> field8() {
        return DonorGroupMapping.DONOR_GROUP_MAPPING.IS_ACTIVE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getDonorId();
    }

    @Override
    public UUID component3() {
        return getGroupId();
    }

    @Override
    public LocalDateTime component4() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component5() {
        return getUpdatedOn();
    }

    @Override
    public UUID component6() {
        return getCreatedBy();
    }

    @Override
    public UUID component7() {
        return getUpdatedBy();
    }

    @Override
    public Boolean component8() {
        return getIsActive();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getDonorId();
    }

    @Override
    public UUID value3() {
        return getGroupId();
    }

    @Override
    public LocalDateTime value4() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value5() {
        return getUpdatedOn();
    }

    @Override
    public UUID value6() {
        return getCreatedBy();
    }

    @Override
    public UUID value7() {
        return getUpdatedBy();
    }

    @Override
    public Boolean value8() {
        return getIsActive();
    }

    @Override
    public DonorGroupMappingRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value2(UUID value) {
        setDonorId(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value3(UUID value) {
        setGroupId(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value4(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value5(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value6(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value7(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord value8(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public DonorGroupMappingRecord values(UUID value1, UUID value2, UUID value3, LocalDateTime value4, LocalDateTime value5, UUID value6, UUID value7, Boolean value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DonorGroupMappingRecord
     */
    public DonorGroupMappingRecord() {
        super(DonorGroupMapping.DONOR_GROUP_MAPPING);
    }

    /**
     * Create a detached, initialised DonorGroupMappingRecord
     */
    public DonorGroupMappingRecord(UUID id, UUID donorId, UUID groupId, LocalDateTime createdOn, LocalDateTime updatedOn, UUID createdBy, UUID updatedBy, Boolean isActive) {
        super(DonorGroupMapping.DONOR_GROUP_MAPPING);

        setId(id);
        setDonorId(donorId);
        setGroupId(groupId);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setIsActive(isActive);
    }
}
