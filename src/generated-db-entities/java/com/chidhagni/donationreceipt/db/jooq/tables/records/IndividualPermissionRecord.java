/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPermission;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualPermissionRecord extends UpdatableRecordImpl<IndividualPermissionRecord> implements Record9<UUID, UUID, List<RoleNode>, LocalDateTime, UUID, LocalDateTime, UUID, String, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>individual_permission.id</code>.
     */
    public IndividualPermissionRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>individual_permission.individual_id</code>.
     */
    public IndividualPermissionRecord setIndividualId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.individual_id</code>.
     */
    public UUID getIndividualId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>individual_permission.permissions</code>.
     */
    public IndividualPermissionRecord setPermissions(List<RoleNode> value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.permissions</code>.
     */
    public List<RoleNode> getPermissions() {
        return (List<RoleNode>) get(2);
    }

    /**
     * Setter for <code>individual_permission.created_on</code>.
     */
    public IndividualPermissionRecord setCreatedOn(LocalDateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>individual_permission.created_by</code>.
     */
    public IndividualPermissionRecord setCreatedBy(UUID value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>individual_permission.updated_on</code>.
     */
    public IndividualPermissionRecord setUpdatedOn(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>individual_permission.updated_by</code>.
     */
    public IndividualPermissionRecord setUpdatedBy(UUID value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>individual_permission.remarks</code>.
     */
    public IndividualPermissionRecord setRemarks(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.remarks</code>.
     */
    public String getRemarks() {
        return (String) get(7);
    }

    /**
     * Setter for <code>individual_permission.org_id</code>.
     */
    public IndividualPermissionRecord setOrgId(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>individual_permission.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, UUID, List<RoleNode>, LocalDateTime, UUID, LocalDateTime, UUID, String, UUID> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<UUID, UUID, List<RoleNode>, LocalDateTime, UUID, LocalDateTime, UUID, String, UUID> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.ID;
    }

    @Override
    public Field<UUID> field2() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.INDIVIDUAL_ID;
    }

    @Override
    public Field<List<RoleNode>> field3() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.PERMISSIONS;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.CREATED_ON;
    }

    @Override
    public Field<UUID> field5() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.CREATED_BY;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.UPDATED_ON;
    }

    @Override
    public Field<UUID> field7() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.UPDATED_BY;
    }

    @Override
    public Field<String> field8() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.REMARKS;
    }

    @Override
    public Field<UUID> field9() {
        return IndividualPermission.INDIVIDUAL_PERMISSION.ORG_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getIndividualId();
    }

    @Override
    public List<RoleNode> component3() {
        return getPermissions();
    }

    @Override
    public LocalDateTime component4() {
        return getCreatedOn();
    }

    @Override
    public UUID component5() {
        return getCreatedBy();
    }

    @Override
    public LocalDateTime component6() {
        return getUpdatedOn();
    }

    @Override
    public UUID component7() {
        return getUpdatedBy();
    }

    @Override
    public String component8() {
        return getRemarks();
    }

    @Override
    public UUID component9() {
        return getOrgId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getIndividualId();
    }

    @Override
    public List<RoleNode> value3() {
        return getPermissions();
    }

    @Override
    public LocalDateTime value4() {
        return getCreatedOn();
    }

    @Override
    public UUID value5() {
        return getCreatedBy();
    }

    @Override
    public LocalDateTime value6() {
        return getUpdatedOn();
    }

    @Override
    public UUID value7() {
        return getUpdatedBy();
    }

    @Override
    public String value8() {
        return getRemarks();
    }

    @Override
    public UUID value9() {
        return getOrgId();
    }

    @Override
    public IndividualPermissionRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value2(UUID value) {
        setIndividualId(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value3(List<RoleNode> value) {
        setPermissions(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value4(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value5(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value6(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value7(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value8(String value) {
        setRemarks(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord value9(UUID value) {
        setOrgId(value);
        return this;
    }

    @Override
    public IndividualPermissionRecord values(UUID value1, UUID value2, List<RoleNode> value3, LocalDateTime value4, UUID value5, LocalDateTime value6, UUID value7, String value8, UUID value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IndividualPermissionRecord
     */
    public IndividualPermissionRecord() {
        super(IndividualPermission.INDIVIDUAL_PERMISSION);
    }

    /**
     * Create a detached, initialised IndividualPermissionRecord
     */
    public IndividualPermissionRecord(UUID id, UUID individualId, List<RoleNode> permissions, LocalDateTime createdOn, UUID createdBy, LocalDateTime updatedOn, UUID updatedBy, String remarks, UUID orgId) {
        super(IndividualPermission.INDIVIDUAL_PERMISSION);

        setId(id);
        setIndividualId(individualId);
        setPermissions(permissions);
        setCreatedOn(createdOn);
        setCreatedBy(createdBy);
        setUpdatedOn(updatedOn);
        setUpdatedBy(updatedBy);
        setRemarks(remarks);
        setOrgId(orgId);
    }
}
