/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorGroupsRecord;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupFilters;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class DonorGroupsDao extends DAOImpl<DonorGroupsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups, UUID> {

    /**
     * Create a new DonorGroupsDao without any configuration
     */
    public DonorGroupsDao() {
        super(DonorGroups.DONOR_GROUPS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups.class);
    }

    /**
     * Create a new DonorGroupsDao with an attached configuration
     */
    @Autowired
    public DonorGroupsDao(Configuration configuration) {
        super(DonorGroups.DONOR_GROUPS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchById(UUID... values) {
        return fetch(DonorGroups.DONOR_GROUPS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups fetchOneById(UUID value) {
        return fetchOne(DonorGroups.DONOR_GROUPS.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByName(String... values) {
        return fetch(DonorGroups.DONOR_GROUPS.NAME, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByDescription(String... values) {
        return fetch(DonorGroups.DONOR_GROUPS.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>filters BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfFilters(DonorGroupFilters lowerInclusive, DonorGroupFilters upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.FILTERS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>filters IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByFilters(DonorGroupFilters... values) {
        return fetch(DonorGroups.DONOR_GROUPS.FILTERS, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByCreatedBy(UUID... values) {
        return fetch(DonorGroups.DONOR_GROUPS.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByUpdatedBy(UUID... values) {
        return fetch(DonorGroups.DONOR_GROUPS.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(DonorGroups.DONOR_GROUPS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(DonorGroups.DONOR_GROUPS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByIsActive(Boolean... values) {
        return fetch(DonorGroups.DONOR_GROUPS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(DonorGroups.DONOR_GROUPS.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups> fetchByOrgId(UUID... values) {
        return fetch(DonorGroups.DONOR_GROUPS.ORG_ID, values);
    }
}
