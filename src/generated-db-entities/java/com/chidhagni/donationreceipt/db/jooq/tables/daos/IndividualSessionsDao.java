/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualSessions;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualSessionsRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class IndividualSessionsDao extends DAOImpl<IndividualSessionsRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions, UUID> {

    /**
     * Create a new IndividualSessionsDao without any configuration
     */
    public IndividualSessionsDao() {
        super(IndividualSessions.INDIVIDUAL_SESSIONS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions.class);
    }

    /**
     * Create a new IndividualSessionsDao with an attached configuration
     */
    @Autowired
    public IndividualSessionsDao(Configuration configuration) {
        super(IndividualSessions.INDIVIDUAL_SESSIONS, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchById(UUID... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions fetchOneById(UUID value) {
        return fetchOne(IndividualSessions.INDIVIDUAL_SESSIONS.ID, value);
    }

    /**
     * Fetch records that have <code>individual_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfIndividualId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.INDIVIDUAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>individual_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByIndividualId(UUID... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.INDIVIDUAL_ID, values);
    }

    /**
     * Fetch records that have <code>accesstoken BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfAccesstoken(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>accesstoken IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByAccesstoken(String... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN, values);
    }

    /**
     * Fetch records that have <code>accesstoken_expirytime BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfAccesstokenExpirytime(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN_EXPIRYTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>accesstoken_expirytime IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByAccesstokenExpirytime(LocalDateTime... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN_EXPIRYTIME, values);
    }

    /**
     * Fetch records that have <code>accesstoken_generated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfAccesstokenGeneratedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN_GENERATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>accesstoken_generated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByAccesstokenGeneratedOn(LocalDateTime... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN_GENERATED_ON, values);
    }

    /**
     * Fetch records that have <code>refreshtoken BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfRefreshtoken(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>refreshtoken IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByRefreshtoken(String... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN, values);
    }

    /**
     * Fetch records that have <code>refreshtoken_expirytime BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfRefreshtokenExpirytime(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN_EXPIRYTIME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>refreshtoken_expirytime IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByRefreshtokenExpirytime(LocalDateTime... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN_EXPIRYTIME, values);
    }

    /**
     * Fetch records that have <code>refreshtoken_generated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfRefreshtokenGeneratedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN_GENERATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>refreshtoken_generated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByRefreshtokenGeneratedOn(LocalDateTime... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN_GENERATED_ON, values);
    }

    /**
     * Fetch records that have <code>ipaddress BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchRangeOfIpaddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(IndividualSessions.INDIVIDUAL_SESSIONS.IPADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ipaddress IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualSessions> fetchByIpaddress(String... values) {
        return fetch(IndividualSessions.INDIVIDUAL_SESSIONS.IPADDRESS, values);
    }
}
