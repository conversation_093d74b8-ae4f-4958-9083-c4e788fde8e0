/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ListValuesRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ListValues extends TableImpl<ListValuesRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>list_values</code>
     */
    public static final ListValues LIST_VALUES = new ListValues();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ListValuesRecord> getRecordType() {
        return ListValuesRecord.class;
    }

    /**
     * The column <code>list_values.id</code>.
     */
    public final TableField<ListValuesRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>list_values.name</code>.
     */
    public final TableField<ListValuesRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>list_values.list_names_id</code>.
     */
    public final TableField<ListValuesRecord, UUID> LIST_NAMES_ID = createField(DSL.name("list_names_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>list_values.is_active</code>.
     */
    public final TableField<ListValuesRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>list_values.created_by</code>.
     */
    public final TableField<ListValuesRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>list_values.updated_by</code>.
     */
    public final TableField<ListValuesRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>list_values.created_on</code>.
     */
    public final TableField<ListValuesRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>list_values.updated_on</code>.
     */
    public final TableField<ListValuesRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    private ListValues(Name alias, Table<ListValuesRecord> aliased) {
        this(alias, aliased, null);
    }

    private ListValues(Name alias, Table<ListValuesRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>list_values</code> table reference
     */
    public ListValues(String alias) {
        this(DSL.name(alias), LIST_VALUES);
    }

    /**
     * Create an aliased <code>list_values</code> table reference
     */
    public ListValues(Name alias) {
        this(alias, LIST_VALUES);
    }

    /**
     * Create a <code>list_values</code> table reference
     */
    public ListValues() {
        this(DSL.name("list_values"), null);
    }

    public <O extends Record> ListValues(Table<O> child, ForeignKey<O, ListValuesRecord> key) {
        super(child, key, LIST_VALUES);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<ListValuesRecord> getPrimaryKey() {
        return Keys.LIST_VALUES_ID_PK;
    }

    @Override
    public List<UniqueKey<ListValuesRecord>> getKeys() {
        return Arrays.<UniqueKey<ListValuesRecord>>asList(Keys.LIST_VALUES_ID_PK);
    }

    @Override
    public List<ForeignKey<ListValuesRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<ListValuesRecord, ?>>asList(Keys.LIST_VALUES__FK_LIST_NAMES);
    }

    private transient ListNames _listNames;

    public ListNames listNames() {
        if (_listNames == null)
            _listNames = new ListNames(this, Keys.LIST_VALUES__FK_LIST_NAMES);

        return _listNames;
    }

    @Override
    public ListValues as(String alias) {
        return new ListValues(DSL.name(alias), this);
    }

    @Override
    public ListValues as(Name alias) {
        return new ListValues(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ListValues rename(String name) {
        return new ListValues(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ListValues rename(Name name) {
        return new ListValues(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, String, UUID, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
