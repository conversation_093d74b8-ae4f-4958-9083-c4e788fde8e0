/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualRoleRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class IndividualRoleDao extends DAOImpl<IndividualRoleRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole, UUID> {

    /**
     * Create a new IndividualRoleDao without any configuration
     */
    public IndividualRoleDao() {
        super(IndividualRole.INDIVIDUAL_ROLE, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole.class);
    }

    /**
     * Create a new IndividualRoleDao with an attached configuration
     */
    @Autowired
    public IndividualRoleDao(Configuration configuration) {
        super(IndividualRole.INDIVIDUAL_ROLE, com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchById(UUID... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole fetchOneById(UUID value) {
        return fetchOne(IndividualRole.INDIVIDUAL_ROLE.ID, value);
    }

    /**
     * Fetch records that have <code>individual_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfIndividualId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.INDIVIDUAL_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>individual_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByIndividualId(UUID... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.INDIVIDUAL_ID, values);
    }

    /**
     * Fetch records that have <code>role_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfRoleId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.ROLE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>role_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByRoleId(UUID... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.ROLE_ID, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByCreatedBy(UUID... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByUpdatedBy(UUID... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(IndividualRole.INDIVIDUAL_ROLE.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole> fetchByOrgId(UUID... values) {
        return fetch(IndividualRole.INDIVIDUAL_ROLE.ORG_ID, values);
    }
}
