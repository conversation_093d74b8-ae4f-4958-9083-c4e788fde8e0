/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.records.OrganisationRecord;
import com.chidhagni.donationreceipt.organisation.MetaDataDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class OrganisationDao extends DAOImpl<OrganisationRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation, UUID> {

    /**
     * Create a new OrganisationDao without any configuration
     */
    public OrganisationDao() {
        super(Organisation.ORGANISATION, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation.class);
    }

    /**
     * Create a new OrganisationDao with an attached configuration
     */
    @Autowired
    public OrganisationDao(Configuration configuration) {
        super(Organisation.ORGANISATION, com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchById(UUID... values) {
        return fetch(Organisation.ORGANISATION.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation fetchOneById(UUID value) {
        return fetchOne(Organisation.ORGANISATION.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByName(String... values) {
        return fetch(Organisation.ORGANISATION.NAME, values);
    }

    /**
     * Fetch records that have <code>category BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfCategory(String lowerInclusive, String upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.CATEGORY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>category IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByCategory(String... values) {
        return fetch(Organisation.ORGANISATION.CATEGORY, values);
    }

    /**
     * Fetch records that have <code>meta_data BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfMetaData(MetaDataDTO lowerInclusive, MetaDataDTO upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.META_DATA, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>meta_data IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByMetaData(MetaDataDTO... values) {
        return fetch(Organisation.ORGANISATION.META_DATA, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByIsActive(Boolean... values) {
        return fetch(Organisation.ORGANISATION.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByCreatedBy(UUID... values) {
        return fetch(Organisation.ORGANISATION.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByUpdatedBy(UUID... values) {
        return fetch(Organisation.ORGANISATION.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Organisation.ORGANISATION.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Organisation.ORGANISATION.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Organisation.ORGANISATION.UPDATED_ON, values);
    }
}
