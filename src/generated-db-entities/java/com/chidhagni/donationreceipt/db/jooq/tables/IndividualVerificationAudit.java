/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualVerificationAuditRecord;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData;
import com.chidhagni.donationreceipt.individualverificationaudit.jooq.IndividualVerificationJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row21;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualVerificationAudit extends TableImpl<IndividualVerificationAuditRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>individual_verification_audit</code>
     */
    public static final IndividualVerificationAudit INDIVIDUAL_VERIFICATION_AUDIT = new IndividualVerificationAudit();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IndividualVerificationAuditRecord> getRecordType() {
        return IndividualVerificationAuditRecord.class;
    }

    /**
     * The column <code>individual_verification_audit.id</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_verification_audit.contact_type</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, String> CONTACT_TYPE = createField(DSL.name("contact_type"), SQLDataType.VARCHAR(50).nullable(false), this, "");

    /**
     * The column <code>individual_verification_audit.contact_value</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, String> CONTACT_VALUE = createField(DSL.name("contact_value"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>individual_verification_audit.activation_link</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, Boolean> ACTIVATION_LINK = createField(DSL.name("activation_link"), SQLDataType.BOOLEAN.defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>individual_verification_audit.activation_link_created_at</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> ACTIVATION_LINK_CREATED_AT = createField(DSL.name("activation_link_created_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.activation_link_expires_at</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> ACTIVATION_LINK_EXPIRES_AT = createField(DSL.name("activation_link_expires_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.activation_link_verified_at</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> ACTIVATION_LINK_VERIFIED_AT = createField(DSL.name("activation_link_verified_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.ip_address</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, String> IP_ADDRESS = createField(DSL.name("ip_address"), SQLDataType.VARCHAR(40), this, "");

    /**
     * The column <code>individual_verification_audit.role_id</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, UUID> ROLE_ID = createField(DSL.name("role_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_verification_audit.verification_status</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, String> VERIFICATION_STATUS = createField(DSL.name("verification_status"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>individual_verification_audit.is_active</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN, this, "");

    /**
     * The column <code>individual_verification_audit.created_by</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_verification_audit.updated_by</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_verification_audit.created_on</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.updated_on</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.meta_data</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, IndividualVerificationMetaData> META_DATA = createField(DSL.name("meta_data"), SQLDataType.JSONB, this, "", new IndividualVerificationJsonConverter());

    /**
     * The column <code>individual_verification_audit.org_id</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_verification_audit.otp_code</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, String> OTP_CODE = createField(DSL.name("otp_code"), SQLDataType.VARCHAR(10), this, "");

    /**
     * The column <code>individual_verification_audit.otp_created_at</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> OTP_CREATED_AT = createField(DSL.name("otp_created_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.otp_expires_at</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> OTP_EXPIRES_AT = createField(DSL.name("otp_expires_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_verification_audit.otp_verified_at</code>.
     */
    public final TableField<IndividualVerificationAuditRecord, LocalDateTime> OTP_VERIFIED_AT = createField(DSL.name("otp_verified_at"), SQLDataType.LOCALDATETIME(6), this, "");

    private IndividualVerificationAudit(Name alias, Table<IndividualVerificationAuditRecord> aliased) {
        this(alias, aliased, null);
    }

    private IndividualVerificationAudit(Name alias, Table<IndividualVerificationAuditRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>individual_verification_audit</code> table reference
     */
    public IndividualVerificationAudit(String alias) {
        this(DSL.name(alias), INDIVIDUAL_VERIFICATION_AUDIT);
    }

    /**
     * Create an aliased <code>individual_verification_audit</code> table reference
     */
    public IndividualVerificationAudit(Name alias) {
        this(alias, INDIVIDUAL_VERIFICATION_AUDIT);
    }

    /**
     * Create a <code>individual_verification_audit</code> table reference
     */
    public IndividualVerificationAudit() {
        this(DSL.name("individual_verification_audit"), null);
    }

    public <O extends Record> IndividualVerificationAudit(Table<O> child, ForeignKey<O, IndividualVerificationAuditRecord> key) {
        super(child, key, INDIVIDUAL_VERIFICATION_AUDIT);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<IndividualVerificationAuditRecord> getPrimaryKey() {
        return Keys.INDIVIDUAL_VERIFICATION_AUDIT_PKEY;
    }

    @Override
    public List<UniqueKey<IndividualVerificationAuditRecord>> getKeys() {
        return Arrays.<UniqueKey<IndividualVerificationAuditRecord>>asList(Keys.INDIVIDUAL_VERIFICATION_AUDIT_PKEY);
    }

    @Override
    public List<ForeignKey<IndividualVerificationAuditRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<IndividualVerificationAuditRecord, ?>>asList(Keys.INDIVIDUAL_VERIFICATION_AUDIT__FK_ORG_ID_IND_VERIFICATION);
    }

    private transient Organisation _organisation;

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.INDIVIDUAL_VERIFICATION_AUDIT__FK_ORG_ID_IND_VERIFICATION);

        return _organisation;
    }

    @Override
    public IndividualVerificationAudit as(String alias) {
        return new IndividualVerificationAudit(DSL.name(alias), this);
    }

    @Override
    public IndividualVerificationAudit as(Name alias) {
        return new IndividualVerificationAudit(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualVerificationAudit rename(String name) {
        return new IndividualVerificationAudit(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualVerificationAudit rename(Name name) {
        return new IndividualVerificationAudit(name, null);
    }

    // -------------------------------------------------------------------------
    // Row21 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row21<UUID, String, String, Boolean, LocalDateTime, LocalDateTime, LocalDateTime, String, UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, IndividualVerificationMetaData, UUID, String, LocalDateTime, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row21) super.fieldsRow();
    }
}
