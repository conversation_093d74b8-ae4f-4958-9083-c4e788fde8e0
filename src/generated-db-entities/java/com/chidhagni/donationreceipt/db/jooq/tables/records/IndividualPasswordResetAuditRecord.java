/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPasswordResetAudit;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualPasswordResetAuditRecord extends UpdatableRecordImpl<IndividualPasswordResetAuditRecord> implements Record11<UUID, UUID, String, String, LocalDateTime, LocalDateTime, LocalDateTime, String, Boolean, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>individual_password_reset_audit.id</code>.
     */
    public IndividualPasswordResetAuditRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>individual_password_reset_audit.individual_id</code>.
     */
    public IndividualPasswordResetAuditRecord setIndividualId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.individual_id</code>.
     */
    public UUID getIndividualId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>individual_password_reset_audit.email</code>.
     */
    public IndividualPasswordResetAuditRecord setEmail(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.email</code>.
     */
    public String getEmail() {
        return (String) get(2);
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_link</code>.
     */
    public IndividualPasswordResetAuditRecord setResetLink(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_link</code>.
     */
    public String getResetLink() {
        return (String) get(3);
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_link_requested_at</code>.
     */
    public IndividualPasswordResetAuditRecord setResetLinkRequestedAt(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_link_requested_at</code>.
     */
    public LocalDateTime getResetLinkRequestedAt() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_link_expires_at</code>.
     */
    public IndividualPasswordResetAuditRecord setResetLinkExpiresAt(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_link_expires_at</code>.
     */
    public LocalDateTime getResetLinkExpiresAt() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_completed_at</code>.
     */
    public IndividualPasswordResetAuditRecord setResetCompletedAt(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_completed_at</code>.
     */
    public LocalDateTime getResetCompletedAt() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>individual_password_reset_audit.reset_status</code>.
     */
    public IndividualPasswordResetAuditRecord setResetStatus(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.reset_status</code>.
     */
    public String getResetStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>individual_password_reset_audit.is_active</code>.
     */
    public IndividualPasswordResetAuditRecord setIsActive(Boolean value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(8);
    }

    /**
     * Setter for <code>individual_password_reset_audit.created_by</code>.
     */
    public IndividualPasswordResetAuditRecord setCreatedBy(UUID value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>individual_password_reset_audit.updated_by</code>.
     */
    public IndividualPasswordResetAuditRecord setUpdatedBy(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>individual_password_reset_audit.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, UUID, String, String, LocalDateTime, LocalDateTime, LocalDateTime, String, Boolean, UUID, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<UUID, UUID, String, String, LocalDateTime, LocalDateTime, LocalDateTime, String, Boolean, UUID, UUID> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.ID;
    }

    @Override
    public Field<UUID> field2() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.INDIVIDUAL_ID;
    }

    @Override
    public Field<String> field3() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.EMAIL;
    }

    @Override
    public Field<String> field4() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK_REQUESTED_AT;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_LINK_EXPIRES_AT;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_COMPLETED_AT;
    }

    @Override
    public Field<String> field8() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.RESET_STATUS;
    }

    @Override
    public Field<Boolean> field9() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field10() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.CREATED_BY;
    }

    @Override
    public Field<UUID> field11() {
        return IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT.UPDATED_BY;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getIndividualId();
    }

    @Override
    public String component3() {
        return getEmail();
    }

    @Override
    public String component4() {
        return getResetLink();
    }

    @Override
    public LocalDateTime component5() {
        return getResetLinkRequestedAt();
    }

    @Override
    public LocalDateTime component6() {
        return getResetLinkExpiresAt();
    }

    @Override
    public LocalDateTime component7() {
        return getResetCompletedAt();
    }

    @Override
    public String component8() {
        return getResetStatus();
    }

    @Override
    public Boolean component9() {
        return getIsActive();
    }

    @Override
    public UUID component10() {
        return getCreatedBy();
    }

    @Override
    public UUID component11() {
        return getUpdatedBy();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getIndividualId();
    }

    @Override
    public String value3() {
        return getEmail();
    }

    @Override
    public String value4() {
        return getResetLink();
    }

    @Override
    public LocalDateTime value5() {
        return getResetLinkRequestedAt();
    }

    @Override
    public LocalDateTime value6() {
        return getResetLinkExpiresAt();
    }

    @Override
    public LocalDateTime value7() {
        return getResetCompletedAt();
    }

    @Override
    public String value8() {
        return getResetStatus();
    }

    @Override
    public Boolean value9() {
        return getIsActive();
    }

    @Override
    public UUID value10() {
        return getCreatedBy();
    }

    @Override
    public UUID value11() {
        return getUpdatedBy();
    }

    @Override
    public IndividualPasswordResetAuditRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value2(UUID value) {
        setIndividualId(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value3(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value4(String value) {
        setResetLink(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value5(LocalDateTime value) {
        setResetLinkRequestedAt(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value6(LocalDateTime value) {
        setResetLinkExpiresAt(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value7(LocalDateTime value) {
        setResetCompletedAt(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value8(String value) {
        setResetStatus(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value9(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value10(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord value11(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public IndividualPasswordResetAuditRecord values(UUID value1, UUID value2, String value3, String value4, LocalDateTime value5, LocalDateTime value6, LocalDateTime value7, String value8, Boolean value9, UUID value10, UUID value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IndividualPasswordResetAuditRecord
     */
    public IndividualPasswordResetAuditRecord() {
        super(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT);
    }

    /**
     * Create a detached, initialised IndividualPasswordResetAuditRecord
     */
    public IndividualPasswordResetAuditRecord(UUID id, UUID individualId, String email, String resetLink, LocalDateTime resetLinkRequestedAt, LocalDateTime resetLinkExpiresAt, LocalDateTime resetCompletedAt, String resetStatus, Boolean isActive, UUID createdBy, UUID updatedBy) {
        super(IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT);

        setId(id);
        setIndividualId(individualId);
        setEmail(email);
        setResetLink(resetLink);
        setResetLinkRequestedAt(resetLinkRequestedAt);
        setResetLinkExpiresAt(resetLinkExpiresAt);
        setResetCompletedAt(resetCompletedAt);
        setResetStatus(resetStatus);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
    }
}
