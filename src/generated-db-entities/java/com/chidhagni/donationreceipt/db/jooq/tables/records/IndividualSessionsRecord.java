/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.IndividualSessions;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualSessionsRecord extends UpdatableRecordImpl<IndividualSessionsRecord> implements Record9<UUID, UUID, String, LocalDateTime, LocalDateTime, String, LocalDateTime, LocalDateTime, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>individual_sessions.id</code>.
     */
    public IndividualSessionsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>individual_sessions.individual_id</code>.
     */
    public IndividualSessionsRecord setIndividualId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.individual_id</code>.
     */
    public UUID getIndividualId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>individual_sessions.accesstoken</code>.
     */
    public IndividualSessionsRecord setAccesstoken(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.accesstoken</code>.
     */
    public String getAccesstoken() {
        return (String) get(2);
    }

    /**
     * Setter for <code>individual_sessions.accesstoken_expirytime</code>.
     */
    public IndividualSessionsRecord setAccesstokenExpirytime(LocalDateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.accesstoken_expirytime</code>.
     */
    public LocalDateTime getAccesstokenExpirytime() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>individual_sessions.accesstoken_generated_on</code>.
     */
    public IndividualSessionsRecord setAccesstokenGeneratedOn(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.accesstoken_generated_on</code>.
     */
    public LocalDateTime getAccesstokenGeneratedOn() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>individual_sessions.refreshtoken</code>.
     */
    public IndividualSessionsRecord setRefreshtoken(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.refreshtoken</code>.
     */
    public String getRefreshtoken() {
        return (String) get(5);
    }

    /**
     * Setter for <code>individual_sessions.refreshtoken_expirytime</code>.
     */
    public IndividualSessionsRecord setRefreshtokenExpirytime(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.refreshtoken_expirytime</code>.
     */
    public LocalDateTime getRefreshtokenExpirytime() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>individual_sessions.refreshtoken_generated_on</code>.
     */
    public IndividualSessionsRecord setRefreshtokenGeneratedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.refreshtoken_generated_on</code>.
     */
    public LocalDateTime getRefreshtokenGeneratedOn() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>individual_sessions.ipaddress</code>.
     */
    public IndividualSessionsRecord setIpaddress(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>individual_sessions.ipaddress</code>.
     */
    public String getIpaddress() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, UUID, String, LocalDateTime, LocalDateTime, String, LocalDateTime, LocalDateTime, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<UUID, UUID, String, LocalDateTime, LocalDateTime, String, LocalDateTime, LocalDateTime, String> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.ID;
    }

    @Override
    public Field<UUID> field2() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.INDIVIDUAL_ID;
    }

    @Override
    public Field<String> field3() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN_EXPIRYTIME;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.ACCESSTOKEN_GENERATED_ON;
    }

    @Override
    public Field<String> field6() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN_EXPIRYTIME;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.REFRESHTOKEN_GENERATED_ON;
    }

    @Override
    public Field<String> field9() {
        return IndividualSessions.INDIVIDUAL_SESSIONS.IPADDRESS;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getIndividualId();
    }

    @Override
    public String component3() {
        return getAccesstoken();
    }

    @Override
    public LocalDateTime component4() {
        return getAccesstokenExpirytime();
    }

    @Override
    public LocalDateTime component5() {
        return getAccesstokenGeneratedOn();
    }

    @Override
    public String component6() {
        return getRefreshtoken();
    }

    @Override
    public LocalDateTime component7() {
        return getRefreshtokenExpirytime();
    }

    @Override
    public LocalDateTime component8() {
        return getRefreshtokenGeneratedOn();
    }

    @Override
    public String component9() {
        return getIpaddress();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getIndividualId();
    }

    @Override
    public String value3() {
        return getAccesstoken();
    }

    @Override
    public LocalDateTime value4() {
        return getAccesstokenExpirytime();
    }

    @Override
    public LocalDateTime value5() {
        return getAccesstokenGeneratedOn();
    }

    @Override
    public String value6() {
        return getRefreshtoken();
    }

    @Override
    public LocalDateTime value7() {
        return getRefreshtokenExpirytime();
    }

    @Override
    public LocalDateTime value8() {
        return getRefreshtokenGeneratedOn();
    }

    @Override
    public String value9() {
        return getIpaddress();
    }

    @Override
    public IndividualSessionsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value2(UUID value) {
        setIndividualId(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value3(String value) {
        setAccesstoken(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value4(LocalDateTime value) {
        setAccesstokenExpirytime(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value5(LocalDateTime value) {
        setAccesstokenGeneratedOn(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value6(String value) {
        setRefreshtoken(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value7(LocalDateTime value) {
        setRefreshtokenExpirytime(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value8(LocalDateTime value) {
        setRefreshtokenGeneratedOn(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord value9(String value) {
        setIpaddress(value);
        return this;
    }

    @Override
    public IndividualSessionsRecord values(UUID value1, UUID value2, String value3, LocalDateTime value4, LocalDateTime value5, String value6, LocalDateTime value7, LocalDateTime value8, String value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IndividualSessionsRecord
     */
    public IndividualSessionsRecord() {
        super(IndividualSessions.INDIVIDUAL_SESSIONS);
    }

    /**
     * Create a detached, initialised IndividualSessionsRecord
     */
    public IndividualSessionsRecord(UUID id, UUID individualId, String accesstoken, LocalDateTime accesstokenExpirytime, LocalDateTime accesstokenGeneratedOn, String refreshtoken, LocalDateTime refreshtokenExpirytime, LocalDateTime refreshtokenGeneratedOn, String ipaddress) {
        super(IndividualSessions.INDIVIDUAL_SESSIONS);

        setId(id);
        setIndividualId(individualId);
        setAccesstoken(accesstoken);
        setAccesstokenExpirytime(accesstokenExpirytime);
        setAccesstokenGeneratedOn(accesstokenGeneratedOn);
        setRefreshtoken(refreshtoken);
        setRefreshtokenExpirytime(refreshtokenExpirytime);
        setRefreshtokenGeneratedOn(refreshtokenGeneratedOn);
        setIpaddress(ipaddress);
    }
}
