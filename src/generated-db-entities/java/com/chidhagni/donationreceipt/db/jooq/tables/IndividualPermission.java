/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualPermissionRecord;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import com.chidhagni.donationreceipt.roles.utils.NodeJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualPermission extends TableImpl<IndividualPermissionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>individual_permission</code>
     */
    public static final IndividualPermission INDIVIDUAL_PERMISSION = new IndividualPermission();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IndividualPermissionRecord> getRecordType() {
        return IndividualPermissionRecord.class;
    }

    /**
     * The column <code>individual_permission.id</code>.
     */
    public final TableField<IndividualPermissionRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_permission.individual_id</code>.
     */
    public final TableField<IndividualPermissionRecord, UUID> INDIVIDUAL_ID = createField(DSL.name("individual_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_permission.permissions</code>.
     */
    public final TableField<IndividualPermissionRecord, List<RoleNode>> PERMISSIONS = createField(DSL.name("permissions"), SQLDataType.JSONB.nullable(false), this, "", new NodeJsonConverter());

    /**
     * The column <code>individual_permission.created_on</code>.
     */
    public final TableField<IndividualPermissionRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_permission.created_by</code>.
     */
    public final TableField<IndividualPermissionRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_permission.updated_on</code>.
     */
    public final TableField<IndividualPermissionRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>individual_permission.updated_by</code>.
     */
    public final TableField<IndividualPermissionRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_permission.remarks</code>.
     */
    public final TableField<IndividualPermissionRecord, String> REMARKS = createField(DSL.name("remarks"), SQLDataType.VARCHAR(512), this, "");

    /**
     * The column <code>individual_permission.org_id</code>.
     */
    public final TableField<IndividualPermissionRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID, this, "");

    private IndividualPermission(Name alias, Table<IndividualPermissionRecord> aliased) {
        this(alias, aliased, null);
    }

    private IndividualPermission(Name alias, Table<IndividualPermissionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>individual_permission</code> table reference
     */
    public IndividualPermission(String alias) {
        this(DSL.name(alias), INDIVIDUAL_PERMISSION);
    }

    /**
     * Create an aliased <code>individual_permission</code> table reference
     */
    public IndividualPermission(Name alias) {
        this(alias, INDIVIDUAL_PERMISSION);
    }

    /**
     * Create a <code>individual_permission</code> table reference
     */
    public IndividualPermission() {
        this(DSL.name("individual_permission"), null);
    }

    public <O extends Record> IndividualPermission(Table<O> child, ForeignKey<O, IndividualPermissionRecord> key) {
        super(child, key, INDIVIDUAL_PERMISSION);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<IndividualPermissionRecord> getPrimaryKey() {
        return Keys.INDIVIDUAL_PERMISSION_PK;
    }

    @Override
    public List<UniqueKey<IndividualPermissionRecord>> getKeys() {
        return Arrays.<UniqueKey<IndividualPermissionRecord>>asList(Keys.INDIVIDUAL_PERMISSION_PK);
    }

    @Override
    public List<ForeignKey<IndividualPermissionRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<IndividualPermissionRecord, ?>>asList(Keys.INDIVIDUAL_PERMISSION__INDIVIDUAL_PERMISSION_INDIVIDUAL_FK, Keys.INDIVIDUAL_PERMISSION__INDIVIDUAL_PERMISSION_ORGANISATION_FK);
    }

    private transient Individual _individual;
    private transient Organisation _organisation;

    public Individual individual() {
        if (_individual == null)
            _individual = new Individual(this, Keys.INDIVIDUAL_PERMISSION__INDIVIDUAL_PERMISSION_INDIVIDUAL_FK);

        return _individual;
    }

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.INDIVIDUAL_PERMISSION__INDIVIDUAL_PERMISSION_ORGANISATION_FK);

        return _organisation;
    }

    @Override
    public IndividualPermission as(String alias) {
        return new IndividualPermission(DSL.name(alias), this);
    }

    @Override
    public IndividualPermission as(Name alias) {
        return new IndividualPermission(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualPermission rename(String name) {
        return new IndividualPermission(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualPermission rename(Name name) {
        return new IndividualPermission(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, UUID, List<RoleNode>, LocalDateTime, UUID, LocalDateTime, UUID, String, UUID> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
