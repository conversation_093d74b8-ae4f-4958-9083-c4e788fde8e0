/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.ContactUs;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ContactUsRecord extends UpdatableRecordImpl<ContactUsRecord> implements Record8<UUID, String, String, String, String, String, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>contact_us.id</code>.
     */
    public ContactUsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>contact_us.name</code>.
     */
    public ContactUsRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>contact_us.email</code>.
     */
    public ContactUsRecord setEmail(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.email</code>.
     */
    public String getEmail() {
        return (String) get(2);
    }

    /**
     * Setter for <code>contact_us.contact_number</code>.
     */
    public ContactUsRecord setContactNumber(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.contact_number</code>.
     */
    public String getContactNumber() {
        return (String) get(3);
    }

    /**
     * Setter for <code>contact_us.message</code>.
     */
    public ContactUsRecord setMessage(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.message</code>.
     */
    public String getMessage() {
        return (String) get(4);
    }

    /**
     * Setter for <code>contact_us.ip_address</code>.
     */
    public ContactUsRecord setIpAddress(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.ip_address</code>.
     */
    public String getIpAddress() {
        return (String) get(5);
    }

    /**
     * Setter for <code>contact_us.created_on</code>.
     */
    public ContactUsRecord setCreatedOn(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>contact_us.updated_on</code>.
     */
    public ContactUsRecord setUpdatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>contact_us.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, String, String, String, String, String, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, String, String, String, String, String, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ContactUs.CONTACT_US.ID;
    }

    @Override
    public Field<String> field2() {
        return ContactUs.CONTACT_US.NAME;
    }

    @Override
    public Field<String> field3() {
        return ContactUs.CONTACT_US.EMAIL;
    }

    @Override
    public Field<String> field4() {
        return ContactUs.CONTACT_US.CONTACT_NUMBER;
    }

    @Override
    public Field<String> field5() {
        return ContactUs.CONTACT_US.MESSAGE;
    }

    @Override
    public Field<String> field6() {
        return ContactUs.CONTACT_US.IP_ADDRESS;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return ContactUs.CONTACT_US.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return ContactUs.CONTACT_US.UPDATED_ON;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getEmail();
    }

    @Override
    public String component4() {
        return getContactNumber();
    }

    @Override
    public String component5() {
        return getMessage();
    }

    @Override
    public String component6() {
        return getIpAddress();
    }

    @Override
    public LocalDateTime component7() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component8() {
        return getUpdatedOn();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getEmail();
    }

    @Override
    public String value4() {
        return getContactNumber();
    }

    @Override
    public String value5() {
        return getMessage();
    }

    @Override
    public String value6() {
        return getIpAddress();
    }

    @Override
    public LocalDateTime value7() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value8() {
        return getUpdatedOn();
    }

    @Override
    public ContactUsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ContactUsRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public ContactUsRecord value3(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public ContactUsRecord value4(String value) {
        setContactNumber(value);
        return this;
    }

    @Override
    public ContactUsRecord value5(String value) {
        setMessage(value);
        return this;
    }

    @Override
    public ContactUsRecord value6(String value) {
        setIpAddress(value);
        return this;
    }

    @Override
    public ContactUsRecord value7(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public ContactUsRecord value8(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public ContactUsRecord values(UUID value1, String value2, String value3, String value4, String value5, String value6, LocalDateTime value7, LocalDateTime value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ContactUsRecord
     */
    public ContactUsRecord() {
        super(ContactUs.CONTACT_US);
    }

    /**
     * Create a detached, initialised ContactUsRecord
     */
    public ContactUsRecord(UUID id, String name, String email, String contactNumber, String message, String ipAddress, LocalDateTime createdOn, LocalDateTime updatedOn) {
        super(ContactUs.CONTACT_US);

        setId(id);
        setName(name);
        setEmail(email);
        setContactNumber(contactNumber);
        setMessage(message);
        setIpAddress(ipAddress);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
    }
}
