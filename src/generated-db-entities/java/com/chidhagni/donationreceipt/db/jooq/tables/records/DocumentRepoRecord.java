/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo;
import com.chidhagni.donationreceipt.documentrepo.dto.request.TagsDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DocumentRepoRecord extends UpdatableRecordImpl<DocumentRepoRecord> implements Record14<UUID, UUID, UUID, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>, String, LocalDateTime, UUID, UUID, LocalDateTime, LocalDateTime, String, TagsDTO, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>document_repo.id</code>.
     */
    public DocumentRepoRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>document_repo.category</code>.
     */
    public DocumentRepoRecord setCategory(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.category</code>.
     */
    public UUID getCategory() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>document_repo.sub_category</code>.
     */
    public DocumentRepoRecord setSubCategory(UUID value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.sub_category</code>.
     */
    public UUID getSubCategory() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>document_repo.sender</code>.
     */
    public DocumentRepoRecord setSender(com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.sender</code>.
     */
    public com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO getSender() {
        return (com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO) get(3);
    }

    /**
     * Setter for <code>document_repo.recipients</code>.
     */
    public DocumentRepoRecord setRecipients(List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.recipients</code>.
     */
    public List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> getRecipients() {
        return (List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>) get(4);
    }

    /**
     * Setter for <code>document_repo.path</code>.
     */
    public DocumentRepoRecord setPath(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.path</code>.
     */
    public String getPath() {
        return (String) get(5);
    }

    /**
     * Setter for <code>document_repo.file_date</code>.
     */
    public DocumentRepoRecord setFileDate(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.file_date</code>.
     */
    public LocalDateTime getFileDate() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>document_repo.created_by</code>.
     */
    public DocumentRepoRecord setCreatedBy(UUID value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>document_repo.updated_by</code>.
     */
    public DocumentRepoRecord setUpdatedBy(UUID value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>document_repo.created_on</code>.
     */
    public DocumentRepoRecord setCreatedOn(LocalDateTime value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>document_repo.updated_on</code>.
     */
    public DocumentRepoRecord setUpdatedOn(LocalDateTime value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>document_repo.remarks</code>.
     */
    public DocumentRepoRecord setRemarks(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.remarks</code>.
     */
    public String getRemarks() {
        return (String) get(11);
    }

    /**
     * Setter for <code>document_repo.tags</code>.
     */
    public DocumentRepoRecord setTags(TagsDTO value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.tags</code>.
     */
    public TagsDTO getTags() {
        return (TagsDTO) get(12);
    }

    /**
     * Setter for <code>document_repo.is_active</code>.
     */
    public DocumentRepoRecord setIsActive(Boolean value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>document_repo.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, UUID, UUID, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>, String, LocalDateTime, UUID, UUID, LocalDateTime, LocalDateTime, String, TagsDTO, Boolean> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<UUID, UUID, UUID, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>, String, LocalDateTime, UUID, UUID, LocalDateTime, LocalDateTime, String, TagsDTO, Boolean> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return DocumentRepo.DOCUMENT_REPO.ID;
    }

    @Override
    public Field<UUID> field2() {
        return DocumentRepo.DOCUMENT_REPO.CATEGORY;
    }

    @Override
    public Field<UUID> field3() {
        return DocumentRepo.DOCUMENT_REPO.SUB_CATEGORY;
    }

    @Override
    public Field<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> field4() {
        return DocumentRepo.DOCUMENT_REPO.SENDER;
    }

    @Override
    public Field<List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>> field5() {
        return DocumentRepo.DOCUMENT_REPO.RECIPIENTS;
    }

    @Override
    public Field<String> field6() {
        return DocumentRepo.DOCUMENT_REPO.PATH;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return DocumentRepo.DOCUMENT_REPO.FILE_DATE;
    }

    @Override
    public Field<UUID> field8() {
        return DocumentRepo.DOCUMENT_REPO.CREATED_BY;
    }

    @Override
    public Field<UUID> field9() {
        return DocumentRepo.DOCUMENT_REPO.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return DocumentRepo.DOCUMENT_REPO.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return DocumentRepo.DOCUMENT_REPO.UPDATED_ON;
    }

    @Override
    public Field<String> field12() {
        return DocumentRepo.DOCUMENT_REPO.REMARKS;
    }

    @Override
    public Field<TagsDTO> field13() {
        return DocumentRepo.DOCUMENT_REPO.TAGS;
    }

    @Override
    public Field<Boolean> field14() {
        return DocumentRepo.DOCUMENT_REPO.IS_ACTIVE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getCategory();
    }

    @Override
    public UUID component3() {
        return getSubCategory();
    }

    @Override
    public com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO component4() {
        return getSender();
    }

    @Override
    public List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> component5() {
        return getRecipients();
    }

    @Override
    public String component6() {
        return getPath();
    }

    @Override
    public LocalDateTime component7() {
        return getFileDate();
    }

    @Override
    public UUID component8() {
        return getCreatedBy();
    }

    @Override
    public UUID component9() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component10() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component11() {
        return getUpdatedOn();
    }

    @Override
    public String component12() {
        return getRemarks();
    }

    @Override
    public TagsDTO component13() {
        return getTags();
    }

    @Override
    public Boolean component14() {
        return getIsActive();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getCategory();
    }

    @Override
    public UUID value3() {
        return getSubCategory();
    }

    @Override
    public com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO value4() {
        return getSender();
    }

    @Override
    public List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> value5() {
        return getRecipients();
    }

    @Override
    public String value6() {
        return getPath();
    }

    @Override
    public LocalDateTime value7() {
        return getFileDate();
    }

    @Override
    public UUID value8() {
        return getCreatedBy();
    }

    @Override
    public UUID value9() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value10() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value11() {
        return getUpdatedOn();
    }

    @Override
    public String value12() {
        return getRemarks();
    }

    @Override
    public TagsDTO value13() {
        return getTags();
    }

    @Override
    public Boolean value14() {
        return getIsActive();
    }

    @Override
    public DocumentRepoRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value2(UUID value) {
        setCategory(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value3(UUID value) {
        setSubCategory(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value4(com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO value) {
        setSender(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value5(List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> value) {
        setRecipients(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value6(String value) {
        setPath(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value7(LocalDateTime value) {
        setFileDate(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value8(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value9(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value10(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value11(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value12(String value) {
        setRemarks(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value13(TagsDTO value) {
        setTags(value);
        return this;
    }

    @Override
    public DocumentRepoRecord value14(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public DocumentRepoRecord values(UUID value1, UUID value2, UUID value3, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO value4, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> value5, String value6, LocalDateTime value7, UUID value8, UUID value9, LocalDateTime value10, LocalDateTime value11, String value12, TagsDTO value13, Boolean value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DocumentRepoRecord
     */
    public DocumentRepoRecord() {
        super(DocumentRepo.DOCUMENT_REPO);
    }

    /**
     * Create a detached, initialised DocumentRepoRecord
     */
    public DocumentRepoRecord(UUID id, UUID category, UUID subCategory, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO sender, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> recipients, String path, LocalDateTime fileDate, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, String remarks, TagsDTO tags, Boolean isActive) {
        super(DocumentRepo.DOCUMENT_REPO);

        setId(id);
        setCategory(category);
        setSubCategory(subCategory);
        setSender(sender);
        setRecipients(recipients);
        setPath(path);
        setFileDate(fileDate);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setRemarks(remarks);
        setTags(tags);
        setIsActive(isActive);
    }
}
