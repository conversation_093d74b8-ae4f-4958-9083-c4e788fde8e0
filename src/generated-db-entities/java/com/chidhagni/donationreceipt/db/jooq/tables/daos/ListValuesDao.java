/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.daos;


import com.chidhagni.donationreceipt.db.jooq.tables.ListValues;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ListValuesRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ListValuesDao extends DAOImpl<ListValuesRecord, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues, UUID> {

    /**
     * Create a new ListValuesDao without any configuration
     */
    public ListValuesDao() {
        super(ListValues.LIST_VALUES, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues.class);
    }

    /**
     * Create a new ListValuesDao with an attached configuration
     */
    @Autowired
    public ListValuesDao(Configuration configuration) {
        super(ListValues.LIST_VALUES, com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchById(UUID... values) {
        return fetch(ListValues.LIST_VALUES.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues fetchOneById(UUID value) {
        return fetchOne(ListValues.LIST_VALUES.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByName(String... values) {
        return fetch(ListValues.LIST_VALUES.NAME, values);
    }

    /**
     * Fetch records that have <code>list_names_id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfListNamesId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.LIST_NAMES_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>list_names_id IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByListNamesId(UUID... values) {
        return fetch(ListValues.LIST_VALUES.LIST_NAMES_ID, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByIsActive(Boolean... values) {
        return fetch(ListValues.LIST_VALUES.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByCreatedBy(UUID... values) {
        return fetch(ListValues.LIST_VALUES.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByUpdatedBy(UUID... values) {
        return fetch(ListValues.LIST_VALUES.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(ListValues.LIST_VALUES.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ListValues.LIST_VALUES.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.donationreceipt.db.jooq.tables.pojos.ListValues> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(ListValues.LIST_VALUES.UPDATED_ON, values);
    }
}
