/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.JSONB;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ImportStaging implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          importBatchId;
    private String        category;
    private String        name;
    private String        mobileNumber;
    private String        email;
    private String        panNo;
    private JSONB         metaData;
    private String        status;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID          createdBy;
    private UUID          updatedBy;
    private Boolean       isActive;
    private String        encryptedPanNo;
    private String        panNoNonce;

    public ImportStaging() {}

    public ImportStaging(ImportStaging value) {
        this.id = value.id;
        this.importBatchId = value.importBatchId;
        this.category = value.category;
        this.name = value.name;
        this.mobileNumber = value.mobileNumber;
        this.email = value.email;
        this.panNo = value.panNo;
        this.metaData = value.metaData;
        this.status = value.status;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.isActive = value.isActive;
        this.encryptedPanNo = value.encryptedPanNo;
        this.panNoNonce = value.panNoNonce;
    }

    public ImportStaging(
        UUID          id,
        UUID          importBatchId,
        String        category,
        String        name,
        String        mobileNumber,
        String        email,
        String        panNo,
        JSONB         metaData,
        String        status,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        UUID          createdBy,
        UUID          updatedBy,
        Boolean       isActive,
        String        encryptedPanNo,
        String        panNoNonce
    ) {
        this.id = id;
        this.importBatchId = importBatchId;
        this.category = category;
        this.name = name;
        this.mobileNumber = mobileNumber;
        this.email = email;
        this.panNo = panNo;
        this.metaData = metaData;
        this.status = status;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.isActive = isActive;
        this.encryptedPanNo = encryptedPanNo;
        this.panNoNonce = panNoNonce;
    }

    /**
     * Getter for <code>import_staging.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>import_staging.id</code>.
     */
    public ImportStaging setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>import_staging.import_batch_id</code>.
     */
    public UUID getImportBatchId() {
        return this.importBatchId;
    }

    /**
     * Setter for <code>import_staging.import_batch_id</code>.
     */
    public ImportStaging setImportBatchId(UUID importBatchId) {
        this.importBatchId = importBatchId;
        return this;
    }

    /**
     * Getter for <code>import_staging.category</code>.
     */
    public String getCategory() {
        return this.category;
    }

    /**
     * Setter for <code>import_staging.category</code>.
     */
    public ImportStaging setCategory(String category) {
        this.category = category;
        return this;
    }

    /**
     * Getter for <code>import_staging.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>import_staging.name</code>.
     */
    public ImportStaging setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>import_staging.mobile_number</code>.
     */
    public String getMobileNumber() {
        return this.mobileNumber;
    }

    /**
     * Setter for <code>import_staging.mobile_number</code>.
     */
    public ImportStaging setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
        return this;
    }

    /**
     * Getter for <code>import_staging.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>import_staging.email</code>.
     */
    public ImportStaging setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>import_staging.pan_no</code>.
     */
    public String getPanNo() {
        return this.panNo;
    }

    /**
     * Setter for <code>import_staging.pan_no</code>.
     */
    public ImportStaging setPanNo(String panNo) {
        this.panNo = panNo;
        return this;
    }

    /**
     * Getter for <code>import_staging.meta_data</code>.
     */
    public JSONB getMetaData() {
        return this.metaData;
    }

    /**
     * Setter for <code>import_staging.meta_data</code>.
     */
    public ImportStaging setMetaData(JSONB metaData) {
        this.metaData = metaData;
        return this;
    }

    /**
     * Getter for <code>import_staging.status</code>.
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>import_staging.status</code>.
     */
    public ImportStaging setStatus(String status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>import_staging.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>import_staging.created_on</code>.
     */
    public ImportStaging setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>import_staging.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>import_staging.updated_on</code>.
     */
    public ImportStaging setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>import_staging.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>import_staging.created_by</code>.
     */
    public ImportStaging setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>import_staging.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>import_staging.updated_by</code>.
     */
    public ImportStaging setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>import_staging.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>import_staging.is_active</code>.
     */
    public ImportStaging setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>import_staging.encrypted_pan_no</code>.
     */
    public String getEncryptedPanNo() {
        return this.encryptedPanNo;
    }

    /**
     * Setter for <code>import_staging.encrypted_pan_no</code>.
     */
    public ImportStaging setEncryptedPanNo(String encryptedPanNo) {
        this.encryptedPanNo = encryptedPanNo;
        return this;
    }

    /**
     * Getter for <code>import_staging.pan_no_nonce</code>.
     */
    public String getPanNoNonce() {
        return this.panNoNonce;
    }

    /**
     * Setter for <code>import_staging.pan_no_nonce</code>.
     */
    public ImportStaging setPanNoNonce(String panNoNonce) {
        this.panNoNonce = panNoNonce;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final ImportStaging other = (ImportStaging) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (importBatchId == null) {
            if (other.importBatchId != null)
                return false;
        }
        else if (!importBatchId.equals(other.importBatchId))
            return false;
        if (category == null) {
            if (other.category != null)
                return false;
        }
        else if (!category.equals(other.category))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (mobileNumber == null) {
            if (other.mobileNumber != null)
                return false;
        }
        else if (!mobileNumber.equals(other.mobileNumber))
            return false;
        if (email == null) {
            if (other.email != null)
                return false;
        }
        else if (!email.equals(other.email))
            return false;
        if (panNo == null) {
            if (other.panNo != null)
                return false;
        }
        else if (!panNo.equals(other.panNo))
            return false;
        if (metaData == null) {
            if (other.metaData != null)
                return false;
        }
        else if (!metaData.equals(other.metaData))
            return false;
        if (status == null) {
            if (other.status != null)
                return false;
        }
        else if (!status.equals(other.status))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (encryptedPanNo == null) {
            if (other.encryptedPanNo != null)
                return false;
        }
        else if (!encryptedPanNo.equals(other.encryptedPanNo))
            return false;
        if (panNoNonce == null) {
            if (other.panNoNonce != null)
                return false;
        }
        else if (!panNoNonce.equals(other.panNoNonce))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.importBatchId == null) ? 0 : this.importBatchId.hashCode());
        result = prime * result + ((this.category == null) ? 0 : this.category.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.mobileNumber == null) ? 0 : this.mobileNumber.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.panNo == null) ? 0 : this.panNo.hashCode());
        result = prime * result + ((this.metaData == null) ? 0 : this.metaData.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.encryptedPanNo == null) ? 0 : this.encryptedPanNo.hashCode());
        result = prime * result + ((this.panNoNonce == null) ? 0 : this.panNoNonce.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ImportStaging (");

        sb.append(id);
        sb.append(", ").append(importBatchId);
        sb.append(", ").append(category);
        sb.append(", ").append(name);
        sb.append(", ").append(mobileNumber);
        sb.append(", ").append(email);
        sb.append(", ").append(panNo);
        sb.append(", ").append(metaData);
        sb.append(", ").append(status);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(isActive);
        sb.append(", ").append(encryptedPanNo);
        sb.append(", ").append(panNoNonce);

        sb.append(")");
        return sb.toString();
    }
}
