/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupFilters;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorGroupsRecord extends UpdatableRecordImpl<DonorGroupsRecord> implements Record10<UUID, String, String, DonorGroupFilters, UUID, UUID, LocalDateTime, LocalDateTime, Boolean, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>donor_groups.id</code>.
     */
    public DonorGroupsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>donor_groups.name</code>.
     */
    public DonorGroupsRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>donor_groups.description</code>.
     */
    public DonorGroupsRecord setDescription(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.description</code>.
     */
    public String getDescription() {
        return (String) get(2);
    }

    /**
     * Setter for <code>donor_groups.filters</code>.
     */
    public DonorGroupsRecord setFilters(DonorGroupFilters value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.filters</code>.
     */
    public DonorGroupFilters getFilters() {
        return (DonorGroupFilters) get(3);
    }

    /**
     * Setter for <code>donor_groups.created_by</code>.
     */
    public DonorGroupsRecord setCreatedBy(UUID value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>donor_groups.updated_by</code>.
     */
    public DonorGroupsRecord setUpdatedBy(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>donor_groups.created_on</code>.
     */
    public DonorGroupsRecord setCreatedOn(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>donor_groups.updated_on</code>.
     */
    public DonorGroupsRecord setUpdatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>donor_groups.is_active</code>.
     */
    public DonorGroupsRecord setIsActive(Boolean value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(8);
    }

    /**
     * Setter for <code>donor_groups.org_id</code>.
     */
    public DonorGroupsRecord setOrgId(UUID value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>donor_groups.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<UUID, String, String, DonorGroupFilters, UUID, UUID, LocalDateTime, LocalDateTime, Boolean, UUID> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<UUID, String, String, DonorGroupFilters, UUID, UUID, LocalDateTime, LocalDateTime, Boolean, UUID> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return DonorGroups.DONOR_GROUPS.ID;
    }

    @Override
    public Field<String> field2() {
        return DonorGroups.DONOR_GROUPS.NAME;
    }

    @Override
    public Field<String> field3() {
        return DonorGroups.DONOR_GROUPS.DESCRIPTION;
    }

    @Override
    public Field<DonorGroupFilters> field4() {
        return DonorGroups.DONOR_GROUPS.FILTERS;
    }

    @Override
    public Field<UUID> field5() {
        return DonorGroups.DONOR_GROUPS.CREATED_BY;
    }

    @Override
    public Field<UUID> field6() {
        return DonorGroups.DONOR_GROUPS.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return DonorGroups.DONOR_GROUPS.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return DonorGroups.DONOR_GROUPS.UPDATED_ON;
    }

    @Override
    public Field<Boolean> field9() {
        return DonorGroups.DONOR_GROUPS.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field10() {
        return DonorGroups.DONOR_GROUPS.ORG_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getDescription();
    }

    @Override
    public DonorGroupFilters component4() {
        return getFilters();
    }

    @Override
    public UUID component5() {
        return getCreatedBy();
    }

    @Override
    public UUID component6() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component7() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component8() {
        return getUpdatedOn();
    }

    @Override
    public Boolean component9() {
        return getIsActive();
    }

    @Override
    public UUID component10() {
        return getOrgId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getDescription();
    }

    @Override
    public DonorGroupFilters value4() {
        return getFilters();
    }

    @Override
    public UUID value5() {
        return getCreatedBy();
    }

    @Override
    public UUID value6() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value7() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value8() {
        return getUpdatedOn();
    }

    @Override
    public Boolean value9() {
        return getIsActive();
    }

    @Override
    public UUID value10() {
        return getOrgId();
    }

    @Override
    public DonorGroupsRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value3(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value4(DonorGroupFilters value) {
        setFilters(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value5(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value6(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value7(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value8(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value9(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public DonorGroupsRecord value10(UUID value) {
        setOrgId(value);
        return this;
    }

    @Override
    public DonorGroupsRecord values(UUID value1, String value2, String value3, DonorGroupFilters value4, UUID value5, UUID value6, LocalDateTime value7, LocalDateTime value8, Boolean value9, UUID value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DonorGroupsRecord
     */
    public DonorGroupsRecord() {
        super(DonorGroups.DONOR_GROUPS);
    }

    /**
     * Create a detached, initialised DonorGroupsRecord
     */
    public DonorGroupsRecord(UUID id, String name, String description, DonorGroupFilters filters, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, Boolean isActive, UUID orgId) {
        super(DonorGroups.DONOR_GROUPS);

        setId(id);
        setName(name);
        setDescription(description);
        setFilters(filters);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setIsActive(isActive);
        setOrgId(orgId);
    }
}
