/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonorGroupMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private UUID          donorId;
    private UUID          groupId;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID          createdBy;
    private UUID          updatedBy;
    private Boolean       isActive;

    public DonorGroupMapping() {}

    public DonorGroupMapping(DonorGroupMapping value) {
        this.id = value.id;
        this.donorId = value.donorId;
        this.groupId = value.groupId;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.isActive = value.isActive;
    }

    public DonorGroupMapping(
        UUID          id,
        UUID          donorId,
        UUID          groupId,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        UUID          createdBy,
        UUID          updatedBy,
        Boolean       isActive
    ) {
        this.id = id;
        this.donorId = donorId;
        this.groupId = groupId;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.isActive = isActive;
    }

    /**
     * Getter for <code>donor_group_mapping.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>donor_group_mapping.id</code>.
     */
    public DonorGroupMapping setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.donor_id</code>.
     */
    public UUID getDonorId() {
        return this.donorId;
    }

    /**
     * Setter for <code>donor_group_mapping.donor_id</code>.
     */
    public DonorGroupMapping setDonorId(UUID donorId) {
        this.donorId = donorId;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.group_id</code>.
     */
    public UUID getGroupId() {
        return this.groupId;
    }

    /**
     * Setter for <code>donor_group_mapping.group_id</code>.
     */
    public DonorGroupMapping setGroupId(UUID groupId) {
        this.groupId = groupId;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>donor_group_mapping.created_on</code>.
     */
    public DonorGroupMapping setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>donor_group_mapping.updated_on</code>.
     */
    public DonorGroupMapping setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>donor_group_mapping.created_by</code>.
     */
    public DonorGroupMapping setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>donor_group_mapping.updated_by</code>.
     */
    public DonorGroupMapping setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>donor_group_mapping.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>donor_group_mapping.is_active</code>.
     */
    public DonorGroupMapping setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final DonorGroupMapping other = (DonorGroupMapping) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (donorId == null) {
            if (other.donorId != null)
                return false;
        }
        else if (!donorId.equals(other.donorId))
            return false;
        if (groupId == null) {
            if (other.groupId != null)
                return false;
        }
        else if (!groupId.equals(other.groupId))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.donorId == null) ? 0 : this.donorId.hashCode());
        result = prime * result + ((this.groupId == null) ? 0 : this.groupId.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DonorGroupMapping (");

        sb.append(id);
        sb.append(", ").append(donorId);
        sb.append(", ").append(groupId);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(isActive);

        sb.append(")");
        return sb.toString();
    }
}
