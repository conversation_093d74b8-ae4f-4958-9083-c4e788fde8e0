/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonationHeadsRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonationHeads extends TableImpl<DonationHeadsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>donation_heads</code>
     */
    public static final DonationHeads DONATION_HEADS = new DonationHeads();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DonationHeadsRecord> getRecordType() {
        return DonationHeadsRecord.class;
    }

    /**
     * The column <code>donation_heads.id</code>.
     */
    public final TableField<DonationHeadsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donation_heads.name</code>.
     */
    public final TableField<DonationHeadsRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>donation_heads.description</code>.
     */
    public final TableField<DonationHeadsRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>donation_heads.org_id</code>.
     */
    public final TableField<DonationHeadsRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donation_heads.created_on</code>.
     */
    public final TableField<DonationHeadsRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>donation_heads.created_by</code>.
     */
    public final TableField<DonationHeadsRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donation_heads.updated_on</code>.
     */
    public final TableField<DonationHeadsRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>donation_heads.updated_by</code>.
     */
    public final TableField<DonationHeadsRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donation_heads.is_active</code>.
     */
    public final TableField<DonationHeadsRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    private DonationHeads(Name alias, Table<DonationHeadsRecord> aliased) {
        this(alias, aliased, null);
    }

    private DonationHeads(Name alias, Table<DonationHeadsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>donation_heads</code> table reference
     */
    public DonationHeads(String alias) {
        this(DSL.name(alias), DONATION_HEADS);
    }

    /**
     * Create an aliased <code>donation_heads</code> table reference
     */
    public DonationHeads(Name alias) {
        this(alias, DONATION_HEADS);
    }

    /**
     * Create a <code>donation_heads</code> table reference
     */
    public DonationHeads() {
        this(DSL.name("donation_heads"), null);
    }

    public <O extends Record> DonationHeads(Table<O> child, ForeignKey<O, DonationHeadsRecord> key) {
        super(child, key, DONATION_HEADS);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<DonationHeadsRecord> getPrimaryKey() {
        return Keys.DONATION_HEADS_PKEY;
    }

    @Override
    public List<UniqueKey<DonationHeadsRecord>> getKeys() {
        return Arrays.<UniqueKey<DonationHeadsRecord>>asList(Keys.DONATION_HEADS_PKEY);
    }

    @Override
    public List<ForeignKey<DonationHeadsRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<DonationHeadsRecord, ?>>asList(Keys.DONATION_HEADS__DONATION_HEADS_ORGANISATION_FK);
    }

    private transient Organisation _organisation;

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.DONATION_HEADS__DONATION_HEADS_ORGANISATION_FK);

        return _organisation;
    }

    @Override
    public DonationHeads as(String alias) {
        return new DonationHeads(DSL.name(alias), this);
    }

    @Override
    public DonationHeads as(Name alias) {
        return new DonationHeads(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DonationHeads rename(String name) {
        return new DonationHeads(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DonationHeads rename(Name name) {
        return new DonationHeads(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, String, String, UUID, LocalDateTime, UUID, LocalDateTime, UUID, Boolean> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
