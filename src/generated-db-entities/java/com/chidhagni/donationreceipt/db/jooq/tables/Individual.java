/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualRecord;
import com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO;
import com.chidhagni.donationreceipt.individual.jooq.IndividualMetaDataJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Individual extends TableImpl<IndividualRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>individual</code>
     */
    public static final Individual INDIVIDUAL = new Individual();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IndividualRecord> getRecordType() {
        return IndividualRecord.class;
    }

    /**
     * The column <code>individual.id</code>.
     */
    public final TableField<IndividualRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual.name</code>.
     */
    public final TableField<IndividualRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>individual.email</code>.
     */
    public final TableField<IndividualRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>individual.mobile_number</code>.
     */
    public final TableField<IndividualRecord, String> MOBILE_NUMBER = createField(DSL.name("mobile_number"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>individual.password</code>.
     */
    public final TableField<IndividualRecord, String> PASSWORD = createField(DSL.name("password"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>individual.social_login_provider</code>.
     */
    public final TableField<IndividualRecord, String> SOCIAL_LOGIN_PROVIDER = createField(DSL.name("social_login_provider"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>individual.social_login_provider_id</code>.
     */
    public final TableField<IndividualRecord, String> SOCIAL_LOGIN_PROVIDER_ID = createField(DSL.name("social_login_provider_id"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>individual.social_login_provider_image_url</code>.
     */
    public final TableField<IndividualRecord, String> SOCIAL_LOGIN_PROVIDER_IMAGE_URL = createField(DSL.name("social_login_provider_image_url"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>individual.meta_data</code>.
     */
    public final TableField<IndividualRecord, IndividualMetaDataDTO> META_DATA = createField(DSL.name("meta_data"), SQLDataType.JSONB, this, "", new IndividualMetaDataJsonConverter());

    /**
     * The column <code>individual.is_active</code>.
     */
    public final TableField<IndividualRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>individual.created_by</code>.
     */
    public final TableField<IndividualRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual.updated_by</code>.
     */
    public final TableField<IndividualRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual.created_on</code>.
     */
    public final TableField<IndividualRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>individual.updated_on</code>.
     */
    public final TableField<IndividualRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    private Individual(Name alias, Table<IndividualRecord> aliased) {
        this(alias, aliased, null);
    }

    private Individual(Name alias, Table<IndividualRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>individual</code> table reference
     */
    public Individual(String alias) {
        this(DSL.name(alias), INDIVIDUAL);
    }

    /**
     * Create an aliased <code>individual</code> table reference
     */
    public Individual(Name alias) {
        this(alias, INDIVIDUAL);
    }

    /**
     * Create a <code>individual</code> table reference
     */
    public Individual() {
        this(DSL.name("individual"), null);
    }

    public <O extends Record> Individual(Table<O> child, ForeignKey<O, IndividualRecord> key) {
        super(child, key, INDIVIDUAL);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<IndividualRecord> getPrimaryKey() {
        return Keys.INDIVIDUAL_PKEY;
    }

    @Override
    public List<UniqueKey<IndividualRecord>> getKeys() {
        return Arrays.<UniqueKey<IndividualRecord>>asList(Keys.INDIVIDUAL_PKEY, Keys.INDIVIDUAL_EMAIL_UNIQUE);
    }

    @Override
    public Individual as(String alias) {
        return new Individual(DSL.name(alias), this);
    }

    @Override
    public Individual as(Name alias) {
        return new Individual(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Individual rename(String name) {
        return new Individual(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Individual rename(Name name) {
        return new Individual(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, String, String, String, String, String, String, String, IndividualMetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
