/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DonationReceipts implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID                       id;
    private String                     receiptNo;
    private UUID                       tenantOrgId;
    private DonationReceiptMetaDataDTO metaData;
    private UUID                       donationTypeId;
    private UUID                       donationHeadId;
    private LocalDate                  receiptDate;
    private LocalDateTime              createdOn;
    private UUID                       createdBy;
    private LocalDateTime              updatedOn;
    private UUID                       updatedBy;
    private Boolean                    isActive;
    private UUID                       donorId;
    private UUID                       donorOrgId;

    public DonationReceipts() {}

    public DonationReceipts(DonationReceipts value) {
        this.id = value.id;
        this.receiptNo = value.receiptNo;
        this.tenantOrgId = value.tenantOrgId;
        this.metaData = value.metaData;
        this.donationTypeId = value.donationTypeId;
        this.donationHeadId = value.donationHeadId;
        this.receiptDate = value.receiptDate;
        this.createdOn = value.createdOn;
        this.createdBy = value.createdBy;
        this.updatedOn = value.updatedOn;
        this.updatedBy = value.updatedBy;
        this.isActive = value.isActive;
        this.donorId = value.donorId;
        this.donorOrgId = value.donorOrgId;
    }

    public DonationReceipts(
        UUID                       id,
        String                     receiptNo,
        UUID                       tenantOrgId,
        DonationReceiptMetaDataDTO metaData,
        UUID                       donationTypeId,
        UUID                       donationHeadId,
        LocalDate                  receiptDate,
        LocalDateTime              createdOn,
        UUID                       createdBy,
        LocalDateTime              updatedOn,
        UUID                       updatedBy,
        Boolean                    isActive,
        UUID                       donorId,
        UUID                       donorOrgId
    ) {
        this.id = id;
        this.receiptNo = receiptNo;
        this.tenantOrgId = tenantOrgId;
        this.metaData = metaData;
        this.donationTypeId = donationTypeId;
        this.donationHeadId = donationHeadId;
        this.receiptDate = receiptDate;
        this.createdOn = createdOn;
        this.createdBy = createdBy;
        this.updatedOn = updatedOn;
        this.updatedBy = updatedBy;
        this.isActive = isActive;
        this.donorId = donorId;
        this.donorOrgId = donorOrgId;
    }

    /**
     * Getter for <code>donation_receipts.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>donation_receipts.id</code>.
     */
    public DonationReceipts setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.receipt_no</code>.
     */
    public String getReceiptNo() {
        return this.receiptNo;
    }

    /**
     * Setter for <code>donation_receipts.receipt_no</code>.
     */
    public DonationReceipts setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.tenant_org_id</code>.
     */
    public UUID getTenantOrgId() {
        return this.tenantOrgId;
    }

    /**
     * Setter for <code>donation_receipts.tenant_org_id</code>.
     */
    public DonationReceipts setTenantOrgId(UUID tenantOrgId) {
        this.tenantOrgId = tenantOrgId;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.meta_data</code>.
     */
    public DonationReceiptMetaDataDTO getMetaData() {
        return this.metaData;
    }

    /**
     * Setter for <code>donation_receipts.meta_data</code>.
     */
    public DonationReceipts setMetaData(DonationReceiptMetaDataDTO metaData) {
        this.metaData = metaData;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donation_type_id</code>.
     */
    public UUID getDonationTypeId() {
        return this.donationTypeId;
    }

    /**
     * Setter for <code>donation_receipts.donation_type_id</code>.
     */
    public DonationReceipts setDonationTypeId(UUID donationTypeId) {
        this.donationTypeId = donationTypeId;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donation_head_id</code>.
     */
    public UUID getDonationHeadId() {
        return this.donationHeadId;
    }

    /**
     * Setter for <code>donation_receipts.donation_head_id</code>.
     */
    public DonationReceipts setDonationHeadId(UUID donationHeadId) {
        this.donationHeadId = donationHeadId;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.receipt_date</code>.
     */
    public LocalDate getReceiptDate() {
        return this.receiptDate;
    }

    /**
     * Setter for <code>donation_receipts.receipt_date</code>.
     */
    public DonationReceipts setReceiptDate(LocalDate receiptDate) {
        this.receiptDate = receiptDate;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>donation_receipts.created_on</code>.
     */
    public DonationReceipts setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>donation_receipts.created_by</code>.
     */
    public DonationReceipts setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>donation_receipts.updated_on</code>.
     */
    public DonationReceipts setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>donation_receipts.updated_by</code>.
     */
    public DonationReceipts setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>donation_receipts.is_active</code>.
     */
    public DonationReceipts setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donor_id</code>.
     */
    public UUID getDonorId() {
        return this.donorId;
    }

    /**
     * Setter for <code>donation_receipts.donor_id</code>.
     */
    public DonationReceipts setDonorId(UUID donorId) {
        this.donorId = donorId;
        return this;
    }

    /**
     * Getter for <code>donation_receipts.donor_org_id</code>.
     */
    public UUID getDonorOrgId() {
        return this.donorOrgId;
    }

    /**
     * Setter for <code>donation_receipts.donor_org_id</code>.
     */
    public DonationReceipts setDonorOrgId(UUID donorOrgId) {
        this.donorOrgId = donorOrgId;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final DonationReceipts other = (DonationReceipts) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (receiptNo == null) {
            if (other.receiptNo != null)
                return false;
        }
        else if (!receiptNo.equals(other.receiptNo))
            return false;
        if (tenantOrgId == null) {
            if (other.tenantOrgId != null)
                return false;
        }
        else if (!tenantOrgId.equals(other.tenantOrgId))
            return false;
        if (metaData == null) {
            if (other.metaData != null)
                return false;
        }
        else if (!metaData.equals(other.metaData))
            return false;
        if (donationTypeId == null) {
            if (other.donationTypeId != null)
                return false;
        }
        else if (!donationTypeId.equals(other.donationTypeId))
            return false;
        if (donationHeadId == null) {
            if (other.donationHeadId != null)
                return false;
        }
        else if (!donationHeadId.equals(other.donationHeadId))
            return false;
        if (receiptDate == null) {
            if (other.receiptDate != null)
                return false;
        }
        else if (!receiptDate.equals(other.receiptDate))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (donorId == null) {
            if (other.donorId != null)
                return false;
        }
        else if (!donorId.equals(other.donorId))
            return false;
        if (donorOrgId == null) {
            if (other.donorOrgId != null)
                return false;
        }
        else if (!donorOrgId.equals(other.donorOrgId))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.receiptNo == null) ? 0 : this.receiptNo.hashCode());
        result = prime * result + ((this.tenantOrgId == null) ? 0 : this.tenantOrgId.hashCode());
        result = prime * result + ((this.metaData == null) ? 0 : this.metaData.hashCode());
        result = prime * result + ((this.donationTypeId == null) ? 0 : this.donationTypeId.hashCode());
        result = prime * result + ((this.donationHeadId == null) ? 0 : this.donationHeadId.hashCode());
        result = prime * result + ((this.receiptDate == null) ? 0 : this.receiptDate.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.donorId == null) ? 0 : this.donorId.hashCode());
        result = prime * result + ((this.donorOrgId == null) ? 0 : this.donorOrgId.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DonationReceipts (");

        sb.append(id);
        sb.append(", ").append(receiptNo);
        sb.append(", ").append(tenantOrgId);
        sb.append(", ").append(metaData);
        sb.append(", ").append(donationTypeId);
        sb.append(", ").append(donationHeadId);
        sb.append(", ").append(receiptDate);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(isActive);
        sb.append(", ").append(donorId);
        sb.append(", ").append(donorOrgId);

        sb.append(")");
        return sb.toString();
    }
}
