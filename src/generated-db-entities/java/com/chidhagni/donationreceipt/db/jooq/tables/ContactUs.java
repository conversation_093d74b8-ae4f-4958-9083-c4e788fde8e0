/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ContactUsRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ContactUs extends TableImpl<ContactUsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>contact_us</code>
     */
    public static final ContactUs CONTACT_US = new ContactUs();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ContactUsRecord> getRecordType() {
        return ContactUsRecord.class;
    }

    /**
     * The column <code>contact_us.id</code>.
     */
    public final TableField<ContactUsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>contact_us.name</code>.
     */
    public final TableField<ContactUsRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>contact_us.email</code>.
     */
    public final TableField<ContactUsRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>contact_us.contact_number</code>.
     */
    public final TableField<ContactUsRecord, String> CONTACT_NUMBER = createField(DSL.name("contact_number"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>contact_us.message</code>.
     */
    public final TableField<ContactUsRecord, String> MESSAGE = createField(DSL.name("message"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>contact_us.ip_address</code>.
     */
    public final TableField<ContactUsRecord, String> IP_ADDRESS = createField(DSL.name("ip_address"), SQLDataType.VARCHAR(40), this, "");

    /**
     * The column <code>contact_us.created_on</code>.
     */
    public final TableField<ContactUsRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>contact_us.updated_on</code>.
     */
    public final TableField<ContactUsRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    private ContactUs(Name alias, Table<ContactUsRecord> aliased) {
        this(alias, aliased, null);
    }

    private ContactUs(Name alias, Table<ContactUsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>contact_us</code> table reference
     */
    public ContactUs(String alias) {
        this(DSL.name(alias), CONTACT_US);
    }

    /**
     * Create an aliased <code>contact_us</code> table reference
     */
    public ContactUs(Name alias) {
        this(alias, CONTACT_US);
    }

    /**
     * Create a <code>contact_us</code> table reference
     */
    public ContactUs() {
        this(DSL.name("contact_us"), null);
    }

    public <O extends Record> ContactUs(Table<O> child, ForeignKey<O, ContactUsRecord> key) {
        super(child, key, CONTACT_US);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<ContactUsRecord> getPrimaryKey() {
        return Keys.CONTACT_US_PKEY;
    }

    @Override
    public List<UniqueKey<ContactUsRecord>> getKeys() {
        return Arrays.<UniqueKey<ContactUsRecord>>asList(Keys.CONTACT_US_PKEY);
    }

    @Override
    public ContactUs as(String alias) {
        return new ContactUs(DSL.name(alias), this);
    }

    @Override
    public ContactUs as(Name alias) {
        return new ContactUs(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ContactUs rename(String name) {
        return new ContactUs(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ContactUs rename(Name name) {
        return new ContactUs(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, String, String, String, String, String, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
