/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualRoleRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualRole extends TableImpl<IndividualRoleRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>individual_role</code>
     */
    public static final IndividualRole INDIVIDUAL_ROLE = new IndividualRole();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IndividualRoleRecord> getRecordType() {
        return IndividualRoleRecord.class;
    }

    /**
     * The column <code>individual_role.id</code>.
     */
    public final TableField<IndividualRoleRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_role.individual_id</code>.
     */
    public final TableField<IndividualRoleRecord, UUID> INDIVIDUAL_ID = createField(DSL.name("individual_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_role.role_id</code>.
     */
    public final TableField<IndividualRoleRecord, UUID> ROLE_ID = createField(DSL.name("role_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_role.created_on</code>.
     */
    public final TableField<IndividualRoleRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>individual_role.updated_on</code>.
     */
    public final TableField<IndividualRoleRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>individual_role.created_by</code>.
     */
    public final TableField<IndividualRoleRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_role.updated_by</code>.
     */
    public final TableField<IndividualRoleRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>individual_role.org_id</code>.
     */
    public final TableField<IndividualRoleRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID, this, "");

    private IndividualRole(Name alias, Table<IndividualRoleRecord> aliased) {
        this(alias, aliased, null);
    }

    private IndividualRole(Name alias, Table<IndividualRoleRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>individual_role</code> table reference
     */
    public IndividualRole(String alias) {
        this(DSL.name(alias), INDIVIDUAL_ROLE);
    }

    /**
     * Create an aliased <code>individual_role</code> table reference
     */
    public IndividualRole(Name alias) {
        this(alias, INDIVIDUAL_ROLE);
    }

    /**
     * Create a <code>individual_role</code> table reference
     */
    public IndividualRole() {
        this(DSL.name("individual_role"), null);
    }

    public <O extends Record> IndividualRole(Table<O> child, ForeignKey<O, IndividualRoleRecord> key) {
        super(child, key, INDIVIDUAL_ROLE);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<IndividualRoleRecord> getPrimaryKey() {
        return Keys.INDIVIDUAL_ROLE_ID_PK;
    }

    @Override
    public List<UniqueKey<IndividualRoleRecord>> getKeys() {
        return Arrays.<UniqueKey<IndividualRoleRecord>>asList(Keys.INDIVIDUAL_ROLE_ID_PK);
    }

    @Override
    public List<ForeignKey<IndividualRoleRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<IndividualRoleRecord, ?>>asList(Keys.INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_INDIVIDUAL_ID_FK, Keys.INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_ROLE_ID_FK, Keys.INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_ORGANISATION_FK);
    }

    private transient Individual _individual;
    private transient Roles _roles;
    private transient Organisation _organisation;

    public Individual individual() {
        if (_individual == null)
            _individual = new Individual(this, Keys.INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_INDIVIDUAL_ID_FK);

        return _individual;
    }

    public Roles roles() {
        if (_roles == null)
            _roles = new Roles(this, Keys.INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_ROLE_ID_FK);

        return _roles;
    }

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.INDIVIDUAL_ROLE__INDIVIDUAL_ROLE_ORGANISATION_FK);

        return _organisation;
    }

    @Override
    public IndividualRole as(String alias) {
        return new IndividualRole(DSL.name(alias), this);
    }

    @Override
    public IndividualRole as(Name alias) {
        return new IndividualRole(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualRole rename(String name) {
        return new IndividualRole(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualRole rename(Name name) {
        return new IndividualRole(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, UUID, UUID, LocalDateTime, LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
