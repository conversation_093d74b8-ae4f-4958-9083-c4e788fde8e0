/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DocumentRepoRecord;
import com.chidhagni.donationreceipt.documentrepo.dto.request.TagsDTO;
import com.chidhagni.donationreceipt.documentrepo.jooq.DocumentRepoTagsJsonConverter;
import com.chidhagni.donationreceipt.documentrepo.jooq.docReceiverMetaDataJsonConverter;
import com.chidhagni.donationreceipt.documentrepo.jooq.docSenderMetaDataJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DocumentRepo extends TableImpl<DocumentRepoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>document_repo</code>
     */
    public static final DocumentRepo DOCUMENT_REPO = new DocumentRepo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DocumentRepoRecord> getRecordType() {
        return DocumentRepoRecord.class;
    }

    /**
     * The column <code>document_repo.id</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>document_repo.category</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> CATEGORY = createField(DSL.name("category"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>document_repo.sub_category</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> SUB_CATEGORY = createField(DSL.name("sub_category"), SQLDataType.UUID, this, "");

    /**
     * The column <code>document_repo.sender</code>.
     */
    public final TableField<DocumentRepoRecord, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO> SENDER = createField(DSL.name("sender"), SQLDataType.JSONB, this, "", new docSenderMetaDataJsonConverter());

    /**
     * The column <code>document_repo.recipients</code>.
     */
    public final TableField<DocumentRepoRecord, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>> RECIPIENTS = createField(DSL.name("recipients"), SQLDataType.JSONB, this, "", new docReceiverMetaDataJsonConverter());

    /**
     * The column <code>document_repo.path</code>.
     */
    public final TableField<DocumentRepoRecord, String> PATH = createField(DSL.name("path"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>document_repo.file_date</code>.
     */
    public final TableField<DocumentRepoRecord, LocalDateTime> FILE_DATE = createField(DSL.name("file_date"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>document_repo.created_by</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>document_repo.updated_by</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>document_repo.created_on</code>.
     */
    public final TableField<DocumentRepoRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>document_repo.updated_on</code>.
     */
    public final TableField<DocumentRepoRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>document_repo.remarks</code>.
     */
    public final TableField<DocumentRepoRecord, String> REMARKS = createField(DSL.name("remarks"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>document_repo.tags</code>.
     */
    public final TableField<DocumentRepoRecord, TagsDTO> TAGS = createField(DSL.name("tags"), SQLDataType.JSONB, this, "", new DocumentRepoTagsJsonConverter());

    /**
     * The column <code>document_repo.is_active</code>.
     */
    public final TableField<DocumentRepoRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    private DocumentRepo(Name alias, Table<DocumentRepoRecord> aliased) {
        this(alias, aliased, null);
    }

    private DocumentRepo(Name alias, Table<DocumentRepoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>document_repo</code> table reference
     */
    public DocumentRepo(String alias) {
        this(DSL.name(alias), DOCUMENT_REPO);
    }

    /**
     * Create an aliased <code>document_repo</code> table reference
     */
    public DocumentRepo(Name alias) {
        this(alias, DOCUMENT_REPO);
    }

    /**
     * Create a <code>document_repo</code> table reference
     */
    public DocumentRepo() {
        this(DSL.name("document_repo"), null);
    }

    public <O extends Record> DocumentRepo(Table<O> child, ForeignKey<O, DocumentRepoRecord> key) {
        super(child, key, DOCUMENT_REPO);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<DocumentRepoRecord> getPrimaryKey() {
        return Keys.DOCUMENT_REPO_ID_PK;
    }

    @Override
    public List<UniqueKey<DocumentRepoRecord>> getKeys() {
        return Arrays.<UniqueKey<DocumentRepoRecord>>asList(Keys.DOCUMENT_REPO_ID_PK);
    }

    @Override
    public List<ForeignKey<DocumentRepoRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<DocumentRepoRecord, ?>>asList(Keys.DOCUMENT_REPO__DOCUMENTS_REPO_CATEGORY_FK, Keys.DOCUMENT_REPO__DOCUMENTS_REPO_SUB_CATEGORY_FK);
    }

    private transient Resource _documentsRepoCategoryFk;
    private transient Resource _documentsRepoSubCategoryFk;

    public Resource documentsRepoCategoryFk() {
        if (_documentsRepoCategoryFk == null)
            _documentsRepoCategoryFk = new Resource(this, Keys.DOCUMENT_REPO__DOCUMENTS_REPO_CATEGORY_FK);

        return _documentsRepoCategoryFk;
    }

    public Resource documentsRepoSubCategoryFk() {
        if (_documentsRepoSubCategoryFk == null)
            _documentsRepoSubCategoryFk = new Resource(this, Keys.DOCUMENT_REPO__DOCUMENTS_REPO_SUB_CATEGORY_FK);

        return _documentsRepoSubCategoryFk;
    }

    @Override
    public DocumentRepo as(String alias) {
        return new DocumentRepo(DSL.name(alias), this);
    }

    @Override
    public DocumentRepo as(Name alias) {
        return new DocumentRepo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DocumentRepo rename(String name) {
        return new DocumentRepo(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DocumentRepo rename(Name name) {
        return new DocumentRepo(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, UUID, UUID, com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO, List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>, String, LocalDateTime, UUID, UUID, LocalDateTime, LocalDateTime, String, TagsDTO, Boolean> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
