/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Indexes;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportBatchRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row12;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ImportBatch extends TableImpl<ImportBatchRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>import_batch</code>
     */
    public static final ImportBatch IMPORT_BATCH = new ImportBatch();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ImportBatchRecord> getRecordType() {
        return ImportBatchRecord.class;
    }

    /**
     * The column <code>import_batch.id</code>.
     */
    public final TableField<ImportBatchRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>import_batch.tenant_org_id</code>.
     */
    public final TableField<ImportBatchRecord, UUID> TENANT_ORG_ID = createField(DSL.name("tenant_org_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>import_batch.category</code>.
     */
    public final TableField<ImportBatchRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.VARCHAR(50).nullable(false), this, "");

    /**
     * The column <code>import_batch.file_name</code>.
     */
    public final TableField<ImportBatchRecord, String> FILE_NAME = createField(DSL.name("file_name"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>import_batch.total_records</code>.
     */
    public final TableField<ImportBatchRecord, Integer> TOTAL_RECORDS = createField(DSL.name("total_records"), SQLDataType.INTEGER.defaultValue(DSL.field("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>import_batch.processed_records</code>.
     */
    public final TableField<ImportBatchRecord, Integer> PROCESSED_RECORDS = createField(DSL.name("processed_records"), SQLDataType.INTEGER.defaultValue(DSL.field("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>import_batch.status</code>.
     */
    public final TableField<ImportBatchRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>import_batch.created_on</code>.
     */
    public final TableField<ImportBatchRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>import_batch.updated_on</code>.
     */
    public final TableField<ImportBatchRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>import_batch.created_by</code>.
     */
    public final TableField<ImportBatchRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>import_batch.updated_by</code>.
     */
    public final TableField<ImportBatchRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>import_batch.is_active</code>.
     */
    public final TableField<ImportBatchRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    private ImportBatch(Name alias, Table<ImportBatchRecord> aliased) {
        this(alias, aliased, null);
    }

    private ImportBatch(Name alias, Table<ImportBatchRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>import_batch</code> table reference
     */
    public ImportBatch(String alias) {
        this(DSL.name(alias), IMPORT_BATCH);
    }

    /**
     * Create an aliased <code>import_batch</code> table reference
     */
    public ImportBatch(Name alias) {
        this(alias, IMPORT_BATCH);
    }

    /**
     * Create a <code>import_batch</code> table reference
     */
    public ImportBatch() {
        this(DSL.name("import_batch"), null);
    }

    public <O extends Record> ImportBatch(Table<O> child, ForeignKey<O, ImportBatchRecord> key) {
        super(child, key, IMPORT_BATCH);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.IDX_IMPORT_BATCH_CATEGORY, Indexes.IDX_IMPORT_BATCH_CREATED_ON, Indexes.IDX_IMPORT_BATCH_STATUS, Indexes.IDX_IMPORT_BATCH_TENANT_ORG_ID);
    }

    @Override
    public UniqueKey<ImportBatchRecord> getPrimaryKey() {
        return Keys.IMPORT_BATCH_PKEY;
    }

    @Override
    public List<UniqueKey<ImportBatchRecord>> getKeys() {
        return Arrays.<UniqueKey<ImportBatchRecord>>asList(Keys.IMPORT_BATCH_PKEY);
    }

    @Override
    public List<ForeignKey<ImportBatchRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<ImportBatchRecord, ?>>asList(Keys.IMPORT_BATCH__IMPORT_BATCH_TENANT_ORG_ID_FKEY);
    }

    private transient Organisation _organisation;

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.IMPORT_BATCH__IMPORT_BATCH_TENANT_ORG_ID_FKEY);

        return _organisation;
    }

    @Override
    public ImportBatch as(String alias) {
        return new ImportBatch(DSL.name(alias), this);
    }

    @Override
    public ImportBatch as(Name alias) {
        return new ImportBatch(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ImportBatch rename(String name) {
        return new ImportBatch(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ImportBatch rename(Name name) {
        return new ImportBatch(name, null);
    }

    // -------------------------------------------------------------------------
    // Row12 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, UUID, String, String, Integer, Integer, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean> fieldsRow() {
        return (Row12) super.fieldsRow();
    }
}
