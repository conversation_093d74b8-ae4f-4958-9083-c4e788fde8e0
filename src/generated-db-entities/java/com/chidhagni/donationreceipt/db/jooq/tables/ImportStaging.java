/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Indexes;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.ImportStagingRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row16;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ImportStaging extends TableImpl<ImportStagingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>import_staging</code>
     */
    public static final ImportStaging IMPORT_STAGING = new ImportStaging();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ImportStagingRecord> getRecordType() {
        return ImportStagingRecord.class;
    }

    /**
     * The column <code>import_staging.id</code>.
     */
    public final TableField<ImportStagingRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>import_staging.import_batch_id</code>.
     */
    public final TableField<ImportStagingRecord, UUID> IMPORT_BATCH_ID = createField(DSL.name("import_batch_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>import_staging.category</code>.
     */
    public final TableField<ImportStagingRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.VARCHAR(50).nullable(false), this, "");

    /**
     * The column <code>import_staging.name</code>.
     */
    public final TableField<ImportStagingRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>import_staging.mobile_number</code>.
     */
    public final TableField<ImportStagingRecord, String> MOBILE_NUMBER = createField(DSL.name("mobile_number"), SQLDataType.VARCHAR(15), this, "");

    /**
     * The column <code>import_staging.email</code>.
     */
    public final TableField<ImportStagingRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>import_staging.pan_no</code>.
     */
    public final TableField<ImportStagingRecord, String> PAN_NO = createField(DSL.name("pan_no"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>import_staging.meta_data</code>.
     */
    public final TableField<ImportStagingRecord, JSONB> META_DATA = createField(DSL.name("meta_data"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>import_staging.status</code>.
     */
    public final TableField<ImportStagingRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>import_staging.created_on</code>.
     */
    public final TableField<ImportStagingRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>import_staging.updated_on</code>.
     */
    public final TableField<ImportStagingRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>import_staging.created_by</code>.
     */
    public final TableField<ImportStagingRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>import_staging.updated_by</code>.
     */
    public final TableField<ImportStagingRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>import_staging.is_active</code>.
     */
    public final TableField<ImportStagingRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>import_staging.encrypted_pan_no</code>.
     */
    public final TableField<ImportStagingRecord, String> ENCRYPTED_PAN_NO = createField(DSL.name("encrypted_pan_no"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>import_staging.pan_no_nonce</code>.
     */
    public final TableField<ImportStagingRecord, String> PAN_NO_NONCE = createField(DSL.name("pan_no_nonce"), SQLDataType.VARCHAR(32), this, "");

    private ImportStaging(Name alias, Table<ImportStagingRecord> aliased) {
        this(alias, aliased, null);
    }

    private ImportStaging(Name alias, Table<ImportStagingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>import_staging</code> table reference
     */
    public ImportStaging(String alias) {
        this(DSL.name(alias), IMPORT_STAGING);
    }

    /**
     * Create an aliased <code>import_staging</code> table reference
     */
    public ImportStaging(Name alias) {
        this(alias, IMPORT_STAGING);
    }

    /**
     * Create a <code>import_staging</code> table reference
     */
    public ImportStaging() {
        this(DSL.name("import_staging"), null);
    }

    public <O extends Record> ImportStaging(Table<O> child, ForeignKey<O, ImportStagingRecord> key) {
        super(child, key, IMPORT_STAGING);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.<Index>asList(Indexes.IDX_IMPORT_STAGING_BATCH_ID, Indexes.IDX_IMPORT_STAGING_CATEGORY, Indexes.IDX_IMPORT_STAGING_EMAIL, Indexes.IDX_IMPORT_STAGING_METADATA, Indexes.IDX_IMPORT_STAGING_PAN_NO, Indexes.IDX_IMPORT_STAGING_STATUS);
    }

    @Override
    public UniqueKey<ImportStagingRecord> getPrimaryKey() {
        return Keys.IMPORT_STAGING_PKEY;
    }

    @Override
    public List<UniqueKey<ImportStagingRecord>> getKeys() {
        return Arrays.<UniqueKey<ImportStagingRecord>>asList(Keys.IMPORT_STAGING_PKEY);
    }

    @Override
    public List<ForeignKey<ImportStagingRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<ImportStagingRecord, ?>>asList(Keys.IMPORT_STAGING__IMPORT_STAGING_IMPORT_BATCH_ID_FKEY);
    }

    private transient ImportBatch _importBatch;

    public ImportBatch importBatch() {
        if (_importBatch == null)
            _importBatch = new ImportBatch(this, Keys.IMPORT_STAGING__IMPORT_STAGING_IMPORT_BATCH_ID_FKEY);

        return _importBatch;
    }

    @Override
    public ImportStaging as(String alias) {
        return new ImportStaging(DSL.name(alias), this);
    }

    @Override
    public ImportStaging as(Name alias) {
        return new ImportStaging(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ImportStaging rename(String name) {
        return new ImportStaging(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ImportStaging rename(Name name) {
        return new ImportStaging(name, null);
    }

    // -------------------------------------------------------------------------
    // Row16 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row16<UUID, UUID, String, String, String, String, String, JSONB, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean, String, String> fieldsRow() {
        return (Row16) super.fieldsRow();
    }
}
