/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq;


import com.chidhagni.donationreceipt.db.jooq.tables.ContactUs;
import com.chidhagni.donationreceipt.db.jooq.tables.DocumentRepo;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads;
import com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportBatch;
import com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging;
import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualSessions;
import com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.db.jooq.tables.ListNames;
import com.chidhagni.donationreceipt.db.jooq.tables.ListValues;
import com.chidhagni.donationreceipt.db.jooq.tables.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.Payments;
import com.chidhagni.donationreceipt.db.jooq.tables.Resource;
import com.chidhagni.donationreceipt.db.jooq.tables.Roles;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DefaultSchema extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>DEFAULT_SCHEMA</code>
     */
    public static final DefaultSchema DEFAULT_SCHEMA = new DefaultSchema();

    /**
     * The table <code>contact_us</code>.
     */
    public final ContactUs CONTACT_US = ContactUs.CONTACT_US;

    /**
     * The table <code>document_repo</code>.
     */
    public final DocumentRepo DOCUMENT_REPO = DocumentRepo.DOCUMENT_REPO;

    /**
     * The table <code>donation_heads</code>.
     */
    public final DonationHeads DONATION_HEADS = DonationHeads.DONATION_HEADS;

    /**
     * The table <code>donation_receipts</code>.
     */
    public final DonationReceipts DONATION_RECEIPTS = DonationReceipts.DONATION_RECEIPTS;

    /**
     * The table <code>donor_group_mapping</code>.
     */
    public final DonorGroupMapping DONOR_GROUP_MAPPING = DonorGroupMapping.DONOR_GROUP_MAPPING;

    /**
     * The table <code>donor_groups</code>.
     */
    public final DonorGroups DONOR_GROUPS = DonorGroups.DONOR_GROUPS;

    /**
     * The table <code>donors</code>.
     */
    public final Donors DONORS = Donors.DONORS;

    /**
     * The table <code>import_batch</code>.
     */
    public final ImportBatch IMPORT_BATCH = ImportBatch.IMPORT_BATCH;

    /**
     * The table <code>import_staging</code>.
     */
    public final ImportStaging IMPORT_STAGING = ImportStaging.IMPORT_STAGING;

    /**
     * The table <code>individual</code>.
     */
    public final Individual INDIVIDUAL = Individual.INDIVIDUAL;

    /**
     * The table <code>individual_password_reset_audit</code>.
     */
    public final IndividualPasswordResetAudit INDIVIDUAL_PASSWORD_RESET_AUDIT = IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT;

    /**
     * The table <code>individual_permission</code>.
     */
    public final IndividualPermission INDIVIDUAL_PERMISSION = IndividualPermission.INDIVIDUAL_PERMISSION;

    /**
     * The table <code>individual_role</code>.
     */
    public final IndividualRole INDIVIDUAL_ROLE = IndividualRole.INDIVIDUAL_ROLE;

    /**
     * The table <code>individual_sessions</code>.
     */
    public final IndividualSessions INDIVIDUAL_SESSIONS = IndividualSessions.INDIVIDUAL_SESSIONS;

    /**
     * The table <code>individual_verification_audit</code>.
     */
    public final IndividualVerificationAudit INDIVIDUAL_VERIFICATION_AUDIT = IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT;

    /**
     * The table <code>list_names</code>.
     */
    public final ListNames LIST_NAMES = ListNames.LIST_NAMES;

    /**
     * The table <code>list_values</code>.
     */
    public final ListValues LIST_VALUES = ListValues.LIST_VALUES;

    /**
     * The table <code>organisation</code>.
     */
    public final Organisation ORGANISATION = Organisation.ORGANISATION;

    /**
     * The table <code>payments</code>.
     */
    public final Payments PAYMENTS = Payments.PAYMENTS;

    /**
     * The table <code>resource</code>.
     */
    public final Resource RESOURCE = Resource.RESOURCE;

    /**
     * The table <code>roles</code>.
     */
    public final Roles ROLES = Roles.ROLES;

    /**
     * No further instances allowed
     */
    private DefaultSchema() {
        super("", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.<Table<?>>asList(
            ContactUs.CONTACT_US,
            DocumentRepo.DOCUMENT_REPO,
            DonationHeads.DONATION_HEADS,
            DonationReceipts.DONATION_RECEIPTS,
            DonorGroupMapping.DONOR_GROUP_MAPPING,
            DonorGroups.DONOR_GROUPS,
            Donors.DONORS,
            ImportBatch.IMPORT_BATCH,
            ImportStaging.IMPORT_STAGING,
            Individual.INDIVIDUAL,
            IndividualPasswordResetAudit.INDIVIDUAL_PASSWORD_RESET_AUDIT,
            IndividualPermission.INDIVIDUAL_PERMISSION,
            IndividualRole.INDIVIDUAL_ROLE,
            IndividualSessions.INDIVIDUAL_SESSIONS,
            IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT,
            ListNames.LIST_NAMES,
            ListValues.LIST_VALUES,
            Organisation.ORGANISATION,
            Payments.PAYMENTS,
            Resource.RESOURCE,
            Roles.ROLES);
    }
}
