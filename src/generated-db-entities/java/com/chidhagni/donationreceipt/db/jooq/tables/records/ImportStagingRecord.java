/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.ImportStaging;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ImportStagingRecord extends UpdatableRecordImpl<ImportStagingRecord> implements Record16<UUID, UUID, String, String, String, String, String, JSONB, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>import_staging.id</code>.
     */
    public ImportStagingRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>import_staging.import_batch_id</code>.
     */
    public ImportStagingRecord setImportBatchId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.import_batch_id</code>.
     */
    public UUID getImportBatchId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>import_staging.category</code>.
     */
    public ImportStagingRecord setCategory(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.category</code>.
     */
    public String getCategory() {
        return (String) get(2);
    }

    /**
     * Setter for <code>import_staging.name</code>.
     */
    public ImportStagingRecord setName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.name</code>.
     */
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>import_staging.mobile_number</code>.
     */
    public ImportStagingRecord setMobileNumber(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.mobile_number</code>.
     */
    public String getMobileNumber() {
        return (String) get(4);
    }

    /**
     * Setter for <code>import_staging.email</code>.
     */
    public ImportStagingRecord setEmail(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.email</code>.
     */
    public String getEmail() {
        return (String) get(5);
    }

    /**
     * Setter for <code>import_staging.pan_no</code>.
     */
    public ImportStagingRecord setPanNo(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.pan_no</code>.
     */
    public String getPanNo() {
        return (String) get(6);
    }

    /**
     * Setter for <code>import_staging.meta_data</code>.
     */
    public ImportStagingRecord setMetaData(JSONB value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.meta_data</code>.
     */
    public JSONB getMetaData() {
        return (JSONB) get(7);
    }

    /**
     * Setter for <code>import_staging.status</code>.
     */
    public ImportStagingRecord setStatus(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.status</code>.
     */
    public String getStatus() {
        return (String) get(8);
    }

    /**
     * Setter for <code>import_staging.created_on</code>.
     */
    public ImportStagingRecord setCreatedOn(LocalDateTime value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>import_staging.updated_on</code>.
     */
    public ImportStagingRecord setUpdatedOn(LocalDateTime value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>import_staging.created_by</code>.
     */
    public ImportStagingRecord setCreatedBy(UUID value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(11);
    }

    /**
     * Setter for <code>import_staging.updated_by</code>.
     */
    public ImportStagingRecord setUpdatedBy(UUID value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(12);
    }

    /**
     * Setter for <code>import_staging.is_active</code>.
     */
    public ImportStagingRecord setIsActive(Boolean value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(13);
    }

    /**
     * Setter for <code>import_staging.encrypted_pan_no</code>.
     */
    public ImportStagingRecord setEncryptedPanNo(String value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.encrypted_pan_no</code>.
     */
    public String getEncryptedPanNo() {
        return (String) get(14);
    }

    /**
     * Setter for <code>import_staging.pan_no_nonce</code>.
     */
    public ImportStagingRecord setPanNoNonce(String value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>import_staging.pan_no_nonce</code>.
     */
    public String getPanNoNonce() {
        return (String) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record16 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row16<UUID, UUID, String, String, String, String, String, JSONB, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean, String, String> fieldsRow() {
        return (Row16) super.fieldsRow();
    }

    @Override
    public Row16<UUID, UUID, String, String, String, String, String, JSONB, String, LocalDateTime, LocalDateTime, UUID, UUID, Boolean, String, String> valuesRow() {
        return (Row16) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ImportStaging.IMPORT_STAGING.ID;
    }

    @Override
    public Field<UUID> field2() {
        return ImportStaging.IMPORT_STAGING.IMPORT_BATCH_ID;
    }

    @Override
    public Field<String> field3() {
        return ImportStaging.IMPORT_STAGING.CATEGORY;
    }

    @Override
    public Field<String> field4() {
        return ImportStaging.IMPORT_STAGING.NAME;
    }

    @Override
    public Field<String> field5() {
        return ImportStaging.IMPORT_STAGING.MOBILE_NUMBER;
    }

    @Override
    public Field<String> field6() {
        return ImportStaging.IMPORT_STAGING.EMAIL;
    }

    @Override
    public Field<String> field7() {
        return ImportStaging.IMPORT_STAGING.PAN_NO;
    }

    @Override
    public Field<JSONB> field8() {
        return ImportStaging.IMPORT_STAGING.META_DATA;
    }

    @Override
    public Field<String> field9() {
        return ImportStaging.IMPORT_STAGING.STATUS;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return ImportStaging.IMPORT_STAGING.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return ImportStaging.IMPORT_STAGING.UPDATED_ON;
    }

    @Override
    public Field<UUID> field12() {
        return ImportStaging.IMPORT_STAGING.CREATED_BY;
    }

    @Override
    public Field<UUID> field13() {
        return ImportStaging.IMPORT_STAGING.UPDATED_BY;
    }

    @Override
    public Field<Boolean> field14() {
        return ImportStaging.IMPORT_STAGING.IS_ACTIVE;
    }

    @Override
    public Field<String> field15() {
        return ImportStaging.IMPORT_STAGING.ENCRYPTED_PAN_NO;
    }

    @Override
    public Field<String> field16() {
        return ImportStaging.IMPORT_STAGING.PAN_NO_NONCE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public UUID component2() {
        return getImportBatchId();
    }

    @Override
    public String component3() {
        return getCategory();
    }

    @Override
    public String component4() {
        return getName();
    }

    @Override
    public String component5() {
        return getMobileNumber();
    }

    @Override
    public String component6() {
        return getEmail();
    }

    @Override
    public String component7() {
        return getPanNo();
    }

    @Override
    public JSONB component8() {
        return getMetaData();
    }

    @Override
    public String component9() {
        return getStatus();
    }

    @Override
    public LocalDateTime component10() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component11() {
        return getUpdatedOn();
    }

    @Override
    public UUID component12() {
        return getCreatedBy();
    }

    @Override
    public UUID component13() {
        return getUpdatedBy();
    }

    @Override
    public Boolean component14() {
        return getIsActive();
    }

    @Override
    public String component15() {
        return getEncryptedPanNo();
    }

    @Override
    public String component16() {
        return getPanNoNonce();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public UUID value2() {
        return getImportBatchId();
    }

    @Override
    public String value3() {
        return getCategory();
    }

    @Override
    public String value4() {
        return getName();
    }

    @Override
    public String value5() {
        return getMobileNumber();
    }

    @Override
    public String value6() {
        return getEmail();
    }

    @Override
    public String value7() {
        return getPanNo();
    }

    @Override
    public JSONB value8() {
        return getMetaData();
    }

    @Override
    public String value9() {
        return getStatus();
    }

    @Override
    public LocalDateTime value10() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value11() {
        return getUpdatedOn();
    }

    @Override
    public UUID value12() {
        return getCreatedBy();
    }

    @Override
    public UUID value13() {
        return getUpdatedBy();
    }

    @Override
    public Boolean value14() {
        return getIsActive();
    }

    @Override
    public String value15() {
        return getEncryptedPanNo();
    }

    @Override
    public String value16() {
        return getPanNoNonce();
    }

    @Override
    public ImportStagingRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ImportStagingRecord value2(UUID value) {
        setImportBatchId(value);
        return this;
    }

    @Override
    public ImportStagingRecord value3(String value) {
        setCategory(value);
        return this;
    }

    @Override
    public ImportStagingRecord value4(String value) {
        setName(value);
        return this;
    }

    @Override
    public ImportStagingRecord value5(String value) {
        setMobileNumber(value);
        return this;
    }

    @Override
    public ImportStagingRecord value6(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public ImportStagingRecord value7(String value) {
        setPanNo(value);
        return this;
    }

    @Override
    public ImportStagingRecord value8(JSONB value) {
        setMetaData(value);
        return this;
    }

    @Override
    public ImportStagingRecord value9(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public ImportStagingRecord value10(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public ImportStagingRecord value11(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public ImportStagingRecord value12(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public ImportStagingRecord value13(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public ImportStagingRecord value14(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public ImportStagingRecord value15(String value) {
        setEncryptedPanNo(value);
        return this;
    }

    @Override
    public ImportStagingRecord value16(String value) {
        setPanNoNonce(value);
        return this;
    }

    @Override
    public ImportStagingRecord values(UUID value1, UUID value2, String value3, String value4, String value5, String value6, String value7, JSONB value8, String value9, LocalDateTime value10, LocalDateTime value11, UUID value12, UUID value13, Boolean value14, String value15, String value16) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ImportStagingRecord
     */
    public ImportStagingRecord() {
        super(ImportStaging.IMPORT_STAGING);
    }

    /**
     * Create a detached, initialised ImportStagingRecord
     */
    public ImportStagingRecord(UUID id, UUID importBatchId, String category, String name, String mobileNumber, String email, String panNo, JSONB metaData, String status, LocalDateTime createdOn, LocalDateTime updatedOn, UUID createdBy, UUID updatedBy, Boolean isActive, String encryptedPanNo, String panNoNonce) {
        super(ImportStaging.IMPORT_STAGING);

        setId(id);
        setImportBatchId(importBatchId);
        setCategory(category);
        setName(name);
        setMobileNumber(mobileNumber);
        setEmail(email);
        setPanNo(panNo);
        setMetaData(metaData);
        setStatus(status);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setIsActive(isActive);
        setEncryptedPanNo(encryptedPanNo);
        setPanNoNonce(panNoNonce);
    }
}
