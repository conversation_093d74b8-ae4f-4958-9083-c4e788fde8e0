/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.JSONB;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Resource implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        name;
    private String        description;
    private String        type;
    private UUID          parentResourceId;
    private JSONB         validations;
    private Boolean       isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID          createdBy;
    private UUID          updatedBy;

    public Resource() {}

    public Resource(Resource value) {
        this.id = value.id;
        this.name = value.name;
        this.description = value.description;
        this.type = value.type;
        this.parentResourceId = value.parentResourceId;
        this.validations = value.validations;
        this.isActive = value.isActive;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
    }

    public Resource(
        UUID          id,
        String        name,
        String        description,
        String        type,
        UUID          parentResourceId,
        JSONB         validations,
        Boolean       isActive,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        UUID          createdBy,
        UUID          updatedBy
    ) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.parentResourceId = parentResourceId;
        this.validations = validations;
        this.isActive = isActive;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    /**
     * Getter for <code>resource.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>resource.id</code>.
     */
    public Resource setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>resource.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>resource.name</code>.
     */
    public Resource setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>resource.description</code>.
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * Setter for <code>resource.description</code>.
     */
    public Resource setDescription(String description) {
        this.description = description;
        return this;
    }

    /**
     * Getter for <code>resource.type</code>.
     */
    public String getType() {
        return this.type;
    }

    /**
     * Setter for <code>resource.type</code>.
     */
    public Resource setType(String type) {
        this.type = type;
        return this;
    }

    /**
     * Getter for <code>resource.parent_resource_id</code>.
     */
    public UUID getParentResourceId() {
        return this.parentResourceId;
    }

    /**
     * Setter for <code>resource.parent_resource_id</code>.
     */
    public Resource setParentResourceId(UUID parentResourceId) {
        this.parentResourceId = parentResourceId;
        return this;
    }

    /**
     * Getter for <code>resource.validations</code>.
     */
    public JSONB getValidations() {
        return this.validations;
    }

    /**
     * Setter for <code>resource.validations</code>.
     */
    public Resource setValidations(JSONB validations) {
        this.validations = validations;
        return this;
    }

    /**
     * Getter for <code>resource.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>resource.is_active</code>.
     */
    public Resource setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>resource.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>resource.created_on</code>.
     */
    public Resource setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>resource.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>resource.updated_on</code>.
     */
    public Resource setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>resource.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>resource.created_by</code>.
     */
    public Resource setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>resource.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>resource.updated_by</code>.
     */
    public Resource setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Resource other = (Resource) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        }
        else if (!description.equals(other.description))
            return false;
        if (type == null) {
            if (other.type != null)
                return false;
        }
        else if (!type.equals(other.type))
            return false;
        if (parentResourceId == null) {
            if (other.parentResourceId != null)
                return false;
        }
        else if (!parentResourceId.equals(other.parentResourceId))
            return false;
        if (validations == null) {
            if (other.validations != null)
                return false;
        }
        else if (!validations.equals(other.validations))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!createdBy.equals(other.createdBy))
            return false;
        if (updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!updatedBy.equals(other.updatedBy))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.type == null) ? 0 : this.type.hashCode());
        result = prime * result + ((this.parentResourceId == null) ? 0 : this.parentResourceId.hashCode());
        result = prime * result + ((this.validations == null) ? 0 : this.validations.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Resource (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(type);
        sb.append(", ").append(parentResourceId);
        sb.append(", ").append(validations);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);

        sb.append(")");
        return sb.toString();
    }
}
