/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.RolesRecord;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import com.chidhagni.donationreceipt.roles.utils.NodeJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Roles extends TableImpl<RolesRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>roles</code>
     */
    public static final Roles ROLES = new Roles();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RolesRecord> getRecordType() {
        return RolesRecord.class;
    }

    /**
     * The column <code>roles.id</code>.
     */
    public final TableField<RolesRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>roles.name</code>.
     */
    public final TableField<RolesRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>roles.description</code>.
     */
    public final TableField<RolesRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>roles.parent_role_id</code>.
     */
    public final TableField<RolesRecord, UUID> PARENT_ROLE_ID = createField(DSL.name("parent_role_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>roles.is_active</code>.
     */
    public final TableField<RolesRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>roles.created_on</code>.
     */
    public final TableField<RolesRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>roles.updated_on</code>.
     */
    public final TableField<RolesRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>roles.created_by</code>.
     */
    public final TableField<RolesRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>roles.updated_by</code>.
     */
    public final TableField<RolesRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>roles.permissions</code>.
     */
    public final TableField<RolesRecord, List<RoleNode>> PERMISSIONS = createField(DSL.name("permissions"), SQLDataType.JSONB, this, "", new NodeJsonConverter());

    /**
     * The column <code>roles.org_id</code>.
     */
    public final TableField<RolesRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID, this, "");

    private Roles(Name alias, Table<RolesRecord> aliased) {
        this(alias, aliased, null);
    }

    private Roles(Name alias, Table<RolesRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>roles</code> table reference
     */
    public Roles(String alias) {
        this(DSL.name(alias), ROLES);
    }

    /**
     * Create an aliased <code>roles</code> table reference
     */
    public Roles(Name alias) {
        this(alias, ROLES);
    }

    /**
     * Create a <code>roles</code> table reference
     */
    public Roles() {
        this(DSL.name("roles"), null);
    }

    public <O extends Record> Roles(Table<O> child, ForeignKey<O, RolesRecord> key) {
        super(child, key, ROLES);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<RolesRecord> getPrimaryKey() {
        return Keys.ROLES_ID_PK;
    }

    @Override
    public List<UniqueKey<RolesRecord>> getKeys() {
        return Arrays.<UniqueKey<RolesRecord>>asList(Keys.ROLES_ID_PK);
    }

    @Override
    public List<ForeignKey<RolesRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<RolesRecord, ?>>asList(Keys.ROLES__ROLES_PARENT_ROLE_FK, Keys.ROLES__FK_ROLES_ORG);
    }

    private transient Roles _roles;
    private transient Organisation _organisation;

    public Roles roles() {
        if (_roles == null)
            _roles = new Roles(this, Keys.ROLES__ROLES_PARENT_ROLE_FK);

        return _roles;
    }

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.ROLES__FK_ROLES_ORG);

        return _organisation;
    }

    @Override
    public Roles as(String alias) {
        return new Roles(DSL.name(alias), this);
    }

    @Override
    public Roles as(Name alias) {
        return new Roles(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Roles rename(String name) {
        return new Roles(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Roles rename(Name name) {
        return new Roles(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, String, String, UUID, Boolean, LocalDateTime, LocalDateTime, UUID, UUID, List<RoleNode>, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
