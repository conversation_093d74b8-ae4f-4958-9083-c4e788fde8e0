/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.IndividualSessionsRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row9;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualSessions extends TableImpl<IndividualSessionsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>individual_sessions</code>
     */
    public static final IndividualSessions INDIVIDUAL_SESSIONS = new IndividualSessions();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<IndividualSessionsRecord> getRecordType() {
        return IndividualSessionsRecord.class;
    }

    /**
     * The column <code>individual_sessions.id</code>.
     */
    public final TableField<IndividualSessionsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_sessions.individual_id</code>.
     */
    public final TableField<IndividualSessionsRecord, UUID> INDIVIDUAL_ID = createField(DSL.name("individual_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>individual_sessions.accesstoken</code>.
     */
    public final TableField<IndividualSessionsRecord, String> ACCESSTOKEN = createField(DSL.name("accesstoken"), SQLDataType.VARCHAR.nullable(false), this, "");

    /**
     * The column <code>individual_sessions.accesstoken_expirytime</code>.
     */
    public final TableField<IndividualSessionsRecord, LocalDateTime> ACCESSTOKEN_EXPIRYTIME = createField(DSL.name("accesstoken_expirytime"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>individual_sessions.accesstoken_generated_on</code>.
     */
    public final TableField<IndividualSessionsRecord, LocalDateTime> ACCESSTOKEN_GENERATED_ON = createField(DSL.name("accesstoken_generated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>individual_sessions.refreshtoken</code>.
     */
    public final TableField<IndividualSessionsRecord, String> REFRESHTOKEN = createField(DSL.name("refreshtoken"), SQLDataType.VARCHAR.nullable(false), this, "");

    /**
     * The column <code>individual_sessions.refreshtoken_expirytime</code>.
     */
    public final TableField<IndividualSessionsRecord, LocalDateTime> REFRESHTOKEN_EXPIRYTIME = createField(DSL.name("refreshtoken_expirytime"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>individual_sessions.refreshtoken_generated_on</code>.
     */
    public final TableField<IndividualSessionsRecord, LocalDateTime> REFRESHTOKEN_GENERATED_ON = createField(DSL.name("refreshtoken_generated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>individual_sessions.ipaddress</code>.
     */
    public final TableField<IndividualSessionsRecord, String> IPADDRESS = createField(DSL.name("ipaddress"), SQLDataType.VARCHAR, this, "");

    private IndividualSessions(Name alias, Table<IndividualSessionsRecord> aliased) {
        this(alias, aliased, null);
    }

    private IndividualSessions(Name alias, Table<IndividualSessionsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>individual_sessions</code> table reference
     */
    public IndividualSessions(String alias) {
        this(DSL.name(alias), INDIVIDUAL_SESSIONS);
    }

    /**
     * Create an aliased <code>individual_sessions</code> table reference
     */
    public IndividualSessions(Name alias) {
        this(alias, INDIVIDUAL_SESSIONS);
    }

    /**
     * Create a <code>individual_sessions</code> table reference
     */
    public IndividualSessions() {
        this(DSL.name("individual_sessions"), null);
    }

    public <O extends Record> IndividualSessions(Table<O> child, ForeignKey<O, IndividualSessionsRecord> key) {
        super(child, key, INDIVIDUAL_SESSIONS);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<IndividualSessionsRecord> getPrimaryKey() {
        return Keys.INDIVIDUAL_SESSIONS_PKEY;
    }

    @Override
    public List<UniqueKey<IndividualSessionsRecord>> getKeys() {
        return Arrays.<UniqueKey<IndividualSessionsRecord>>asList(Keys.INDIVIDUAL_SESSIONS_PKEY);
    }

    @Override
    public List<ForeignKey<IndividualSessionsRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<IndividualSessionsRecord, ?>>asList(Keys.INDIVIDUAL_SESSIONS__INDIVIDUAL_SESSIONS_IND_FK);
    }

    private transient Individual _individual;

    public Individual individual() {
        if (_individual == null)
            _individual = new Individual(this, Keys.INDIVIDUAL_SESSIONS__INDIVIDUAL_SESSIONS_IND_FK);

        return _individual;
    }

    @Override
    public IndividualSessions as(String alias) {
        return new IndividualSessions(DSL.name(alias), this);
    }

    @Override
    public IndividualSessions as(Name alias) {
        return new IndividualSessions(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualSessions rename(String name) {
        return new IndividualSessions(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public IndividualSessions rename(Name name) {
        return new IndividualSessions(name, null);
    }

    // -------------------------------------------------------------------------
    // Row9 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, UUID, String, LocalDateTime, LocalDateTime, String, LocalDateTime, LocalDateTime, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }
}
