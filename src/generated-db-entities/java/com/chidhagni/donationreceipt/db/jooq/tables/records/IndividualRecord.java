/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables.records;


import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IndividualRecord extends UpdatableRecordImpl<IndividualRecord> implements Record14<UUID, String, String, String, String, String, String, String, IndividualMetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>individual.id</code>.
     */
    public IndividualRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>individual.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>individual.name</code>.
     */
    public IndividualRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>individual.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>individual.email</code>.
     */
    public IndividualRecord setEmail(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>individual.email</code>.
     */
    public String getEmail() {
        return (String) get(2);
    }

    /**
     * Setter for <code>individual.mobile_number</code>.
     */
    public IndividualRecord setMobileNumber(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>individual.mobile_number</code>.
     */
    public String getMobileNumber() {
        return (String) get(3);
    }

    /**
     * Setter for <code>individual.password</code>.
     */
    public IndividualRecord setPassword(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>individual.password</code>.
     */
    public String getPassword() {
        return (String) get(4);
    }

    /**
     * Setter for <code>individual.social_login_provider</code>.
     */
    public IndividualRecord setSocialLoginProvider(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>individual.social_login_provider</code>.
     */
    public String getSocialLoginProvider() {
        return (String) get(5);
    }

    /**
     * Setter for <code>individual.social_login_provider_id</code>.
     */
    public IndividualRecord setSocialLoginProviderId(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>individual.social_login_provider_id</code>.
     */
    public String getSocialLoginProviderId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>individual.social_login_provider_image_url</code>.
     */
    public IndividualRecord setSocialLoginProviderImageUrl(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>individual.social_login_provider_image_url</code>.
     */
    public String getSocialLoginProviderImageUrl() {
        return (String) get(7);
    }

    /**
     * Setter for <code>individual.meta_data</code>.
     */
    public IndividualRecord setMetaData(IndividualMetaDataDTO value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>individual.meta_data</code>.
     */
    public IndividualMetaDataDTO getMetaData() {
        return (IndividualMetaDataDTO) get(8);
    }

    /**
     * Setter for <code>individual.is_active</code>.
     */
    public IndividualRecord setIsActive(Boolean value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>individual.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(9);
    }

    /**
     * Setter for <code>individual.created_by</code>.
     */
    public IndividualRecord setCreatedBy(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>individual.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(10);
    }

    /**
     * Setter for <code>individual.updated_by</code>.
     */
    public IndividualRecord setUpdatedBy(UUID value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>individual.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(11);
    }

    /**
     * Setter for <code>individual.created_on</code>.
     */
    public IndividualRecord setCreatedOn(LocalDateTime value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>individual.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>individual.updated_on</code>.
     */
    public IndividualRecord setUpdatedOn(LocalDateTime value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>individual.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, String, String, String, String, String, String, String, IndividualMetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<UUID, String, String, String, String, String, String, String, IndividualMetaDataDTO, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Individual.INDIVIDUAL.ID;
    }

    @Override
    public Field<String> field2() {
        return Individual.INDIVIDUAL.NAME;
    }

    @Override
    public Field<String> field3() {
        return Individual.INDIVIDUAL.EMAIL;
    }

    @Override
    public Field<String> field4() {
        return Individual.INDIVIDUAL.MOBILE_NUMBER;
    }

    @Override
    public Field<String> field5() {
        return Individual.INDIVIDUAL.PASSWORD;
    }

    @Override
    public Field<String> field6() {
        return Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER;
    }

    @Override
    public Field<String> field7() {
        return Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER_ID;
    }

    @Override
    public Field<String> field8() {
        return Individual.INDIVIDUAL.SOCIAL_LOGIN_PROVIDER_IMAGE_URL;
    }

    @Override
    public Field<IndividualMetaDataDTO> field9() {
        return Individual.INDIVIDUAL.META_DATA;
    }

    @Override
    public Field<Boolean> field10() {
        return Individual.INDIVIDUAL.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field11() {
        return Individual.INDIVIDUAL.CREATED_BY;
    }

    @Override
    public Field<UUID> field12() {
        return Individual.INDIVIDUAL.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return Individual.INDIVIDUAL.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field14() {
        return Individual.INDIVIDUAL.UPDATED_ON;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getEmail();
    }

    @Override
    public String component4() {
        return getMobileNumber();
    }

    @Override
    public String component5() {
        return getPassword();
    }

    @Override
    public String component6() {
        return getSocialLoginProvider();
    }

    @Override
    public String component7() {
        return getSocialLoginProviderId();
    }

    @Override
    public String component8() {
        return getSocialLoginProviderImageUrl();
    }

    @Override
    public IndividualMetaDataDTO component9() {
        return getMetaData();
    }

    @Override
    public Boolean component10() {
        return getIsActive();
    }

    @Override
    public UUID component11() {
        return getCreatedBy();
    }

    @Override
    public UUID component12() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component13() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component14() {
        return getUpdatedOn();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getEmail();
    }

    @Override
    public String value4() {
        return getMobileNumber();
    }

    @Override
    public String value5() {
        return getPassword();
    }

    @Override
    public String value6() {
        return getSocialLoginProvider();
    }

    @Override
    public String value7() {
        return getSocialLoginProviderId();
    }

    @Override
    public String value8() {
        return getSocialLoginProviderImageUrl();
    }

    @Override
    public IndividualMetaDataDTO value9() {
        return getMetaData();
    }

    @Override
    public Boolean value10() {
        return getIsActive();
    }

    @Override
    public UUID value11() {
        return getCreatedBy();
    }

    @Override
    public UUID value12() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value13() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value14() {
        return getUpdatedOn();
    }

    @Override
    public IndividualRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public IndividualRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public IndividualRecord value3(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public IndividualRecord value4(String value) {
        setMobileNumber(value);
        return this;
    }

    @Override
    public IndividualRecord value5(String value) {
        setPassword(value);
        return this;
    }

    @Override
    public IndividualRecord value6(String value) {
        setSocialLoginProvider(value);
        return this;
    }

    @Override
    public IndividualRecord value7(String value) {
        setSocialLoginProviderId(value);
        return this;
    }

    @Override
    public IndividualRecord value8(String value) {
        setSocialLoginProviderImageUrl(value);
        return this;
    }

    @Override
    public IndividualRecord value9(IndividualMetaDataDTO value) {
        setMetaData(value);
        return this;
    }

    @Override
    public IndividualRecord value10(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public IndividualRecord value11(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public IndividualRecord value12(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public IndividualRecord value13(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public IndividualRecord value14(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public IndividualRecord values(UUID value1, String value2, String value3, String value4, String value5, String value6, String value7, String value8, IndividualMetaDataDTO value9, Boolean value10, UUID value11, UUID value12, LocalDateTime value13, LocalDateTime value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached IndividualRecord
     */
    public IndividualRecord() {
        super(Individual.INDIVIDUAL);
    }

    /**
     * Create a detached, initialised IndividualRecord
     */
    public IndividualRecord(UUID id, String name, String email, String mobileNumber, String password, String socialLoginProvider, String socialLoginProviderId, String socialLoginProviderImageUrl, IndividualMetaDataDTO metaData, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn) {
        super(Individual.INDIVIDUAL);

        setId(id);
        setName(name);
        setEmail(email);
        setMobileNumber(mobileNumber);
        setPassword(password);
        setSocialLoginProvider(socialLoginProvider);
        setSocialLoginProviderId(socialLoginProviderId);
        setSocialLoginProviderImageUrl(socialLoginProviderImageUrl);
        setMetaData(metaData);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
    }
}
