package com.chidhagni.config;

import com.chidhagni.donationreceipt.mail.MailService;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.PdfGenerateService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.mockito.Mockito;

import java.io.File;

@TestConfiguration
public class IntegrationTestConfig {

    @Bean("mailSetupProfessionals")
    @Primary
    public JavaMailSender mailSetupProfessionals() {
        return Mockito.mock(JavaMailSender.class);
    }

    @Bean("mailSetupSocieties")
    @Primary
    public JavaMailSender mailSetupSocieties() {
        return Mockito.mock(JavaMailSender.class);
    }

    @Bean("mailSetupDevelopers")
    @Primary
    public JavaMailSender mailSetupDevelopers() {
        return Mockito.mock(JavaMailSender.class);
    }

    @Bean("mailSetupRegistration")
    @Primary
    public JavaMailSender mailSetupRegistration() {
        return Mockito.mock(JavaMailSender.class);
    }

    @Bean("mailSetUpDonations")
    @Primary
    public JavaMailSender mailSetUpDonations() {
        return Mockito.mock(JavaMailSender.class);
    }

    @Bean
    @Primary
    public MailService mailService() {
        return Mockito.mock(MailService.class);
    }

    @Bean
    @Primary
    public NotificationManager notificationManager() {
        NotificationManager mockNotificationManager = Mockito.mock(NotificationManager.class);
        // Mock notification methods to prevent real notifications
        try {
            Mockito.doNothing().when(mockNotificationManager).sendActivationLink(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
            Mockito.doNothing().when(mockNotificationManager).sendGeneratedReceipt(Mockito.any(File.class), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        } catch (Exception e) {
            // Ignore exceptions in mock setup
        }
        return mockNotificationManager;
    }

    @Bean
    @Primary
    public WatiService watiService() {
        WatiService mockWatiService = Mockito.mock(WatiService.class);
        // Mock WATI service methods to prevent real API calls
        try {
            Mockito.when(mockWatiService.sendTemplateMessage(Mockito.anyString(), Mockito.any())).thenReturn(Mockito.mock(java.net.http.HttpResponse.class));
            Mockito.when(mockWatiService.sendSessionMessages(Mockito.anyString(), Mockito.anyString())).thenReturn(Mockito.mock(java.net.http.HttpResponse.class));
        } catch (Exception e) {
            // Ignore exceptions in mock setup
        }
        return mockWatiService;
    }

    @Bean
    @Primary
    public PdfGenerateService pdfGenerateService() {
        PdfGenerateService mockPdfService = Mockito.mock(PdfGenerateService.class);
        // Mock PDF generation to prevent file system operations
        Mockito.when(mockPdfService.generatePdfFile(Mockito.anyString(), Mockito.any(), Mockito.anyString()))
                .thenReturn("mock-pdf-content".getBytes());
        Mockito.when(mockPdfService.storePdfFileFromBytes(Mockito.any(byte[].class), Mockito.anyString()))
                .thenReturn(Mockito.mock(File.class));
        return mockPdfService;
    }

    @Bean
    @Primary
    public AuthenticationManager authenticationManager() {
        AuthenticationManager mockAuthManager = Mockito.mock(AuthenticationManager.class);
        // Mock authentication to always succeed for testing
        try {
            Mockito.when(mockAuthManager.authenticate(Mockito.any(UsernamePasswordAuthenticationToken.class)))
                    .thenReturn(Mockito.mock(Authentication.class));
        } catch (AuthenticationException e) {
            // Ignore exceptions in mock setup
        }
        return mockAuthManager;
    }

    @Bean
    @Primary
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
} 