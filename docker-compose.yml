version: '3.3'

volumes:
  donationsreceipt_db_data:
  donationsreceipt_minio_data:

networks:
  donationsreceipt-net:
    external: false

services:
  donationsreceipt-db:
    image: postgres
    environment:
      POSTGRES_USER: donationreceipt
      POSTGRES_PASSWORD: donationreceipt
      POSTGRES_DB: donationreceipt
    ports:
      - "35433:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 30s
      timeout: 30s
      retries: 3
    restart: on-failure
    deploy:
      restart_policy:
        condition: on-failure
    stdin_open: true
    tty: true
    networks:
      - donationsreceipt-net
    volumes:
      - donationsreceipt_db_data:/var/lib/postgresql
  minio:
    image: "minio/minio"
    command: ["server", "/data", "--console-address", ":9001"]
    restart: always
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - "donationsreceipt_minio_data:/data"
    stdin_open: true
    tty: true
    networks:
      - donationsreceipt-net