# PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: semgrep-results-pvc
  namespace: semgrep
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: do-block-storage
---
# Semgrep Job
apiVersion: batch/v1
kind: Job
metadata:
  name: semgrep-job
  namespace: semgrep
spec:
  template:
    spec:
      initContainers:
      - name: copy-source
        image: alpine/git
        command: ["sh", "-c", "timeout 120 git clone https://x-access-token:$(cat /etc/github/token)@github.com/ChidhagniConsulting/pure-heart-frontend-guest.git /src"]
        volumeMounts:
        - name: source-code
          mountPath: /src
        - name: secure-github-token
          mountPath: /etc/github
          readOnly: true
      containers:
      - name: semgrep
        image: returntocorp/semgrep:v1.57.0
        command:
          [
            "sh", "-c",
            "cat /src/.semgrepignore && semgrep --config=auto --json --output=/results/results.json /src"
          ]
        volumeMounts:
        - name: results
          mountPath: /results
        - name: source-code
          mountPath: /src
        securityContext:
          # runAsNonRoot: true
          allowPrivilegeEscalation: false
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1"
      restartPolicy: Never
      volumes:
      - name: results
        persistentVolumeClaim:
          claimName: semgrep-results-pvc
      - name: source-code
        emptyDir: {}
      - name: secure-github-token
        secret:
          secretName: secure-github-token 