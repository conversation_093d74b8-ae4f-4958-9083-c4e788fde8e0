apiVersion: v1
kind: Pod
metadata:
  name: semgrep-results-reader
  namespace: semgrep
spec:
  restartPolicy: Never
  securityContext:
    #runAsNonRoot: true
  containers:
    - name: reader
      image: alpine
      command: ["sleep", "3600"]
      securityContext:
       # runAsNonRoot: true
        allowPrivilegeEscalation: false
        runAsUser: 1000
      volumeMounts:
        - name: results
          mountPath: /results  # results.json will be available here
  volumes:
    - name: results
      persistentVolumeClaim:
        claimName: semgrep-results-pvc 